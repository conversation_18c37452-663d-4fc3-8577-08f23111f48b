"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./Components/PaperRocketAnimation.jsx":
/*!*********************************************!*\
  !*** ./Components/PaperRocketAnimation.jsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst StickManAnimation = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-6c62068782816f71\" + \" \" + \"fixed inset-0 pointer-events-none z-10 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-6c62068782816f71\" + \" \" + \"stickman-container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    width: \"50\",\n                    height: \"60\",\n                    viewBox: \"0 0 100 120\",\n                    fill: \"none\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"jsx-6c62068782816f71\" + \" \" + \"stickman\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"50\",\n                            cy: \"15\",\n                            r: \"8\",\n                            fill: \"none\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"3\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-6c62068782816f71\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"50\",\n                            y1: \"23\",\n                            x2: \"50\",\n                            y2: \"70\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"3\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-6c62068782816f71\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"50\",\n                            y1: \"35\",\n                            x2: \"30\",\n                            y2: \"50\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"3\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-6c62068782816f71\" + \" \" + \"left-arm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"50\",\n                            y1: \"35\",\n                            x2: \"70\",\n                            y2: \"50\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"3\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-6c62068782816f71\" + \" \" + \"right-arm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"50\",\n                            y1: \"70\",\n                            x2: \"35\",\n                            y2: \"95\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"3\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-6c62068782816f71\" + \" \" + \"left-leg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"50\",\n                            y1: \"70\",\n                            x2: \"65\",\n                            y2: \"95\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"3\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-6c62068782816f71\" + \" \" + \"right-leg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"46\",\n                            cy: \"12\",\n                            r: \"1.5\",\n                            fill: \"#000000\",\n                            className: \"jsx-6c62068782816f71\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"54\",\n                            cy: \"12\",\n                            r: \"1.5\",\n                            fill: \"#000000\",\n                            className: \"jsx-6c62068782816f71\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M 45 17 Q 50 20 55 17\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"1.5\",\n                            fill: \"none\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-6c62068782816f71\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"6c62068782816f71\",\n                children: \".stickman-container.jsx-6c62068782816f71{position:absolute;-webkit-animation:stickmanWalk 20s linear infinite;-moz-animation:stickmanWalk 20s linear infinite;-o-animation:stickmanWalk 20s linear infinite;animation:stickmanWalk 20s linear infinite}.stickman.jsx-6c62068782816f71{-webkit-transform-origin:center;-moz-transform-origin:center;-ms-transform-origin:center;-o-transform-origin:center;transform-origin:center;-webkit-filter:drop-shadow(2px 2px 4px rgba(0,0,0,.3));filter:drop-shadow(2px 2px 4px rgba(0,0,0,.3))}.left-arm.jsx-6c62068782816f71{-webkit-animation:leftArmSwing.8s ease-in-out infinite;-moz-animation:leftArmSwing.8s ease-in-out infinite;-o-animation:leftArmSwing.8s ease-in-out infinite;animation:leftArmSwing.8s ease-in-out infinite;-webkit-transform-origin:50px 35px;-moz-transform-origin:50px 35px;-ms-transform-origin:50px 35px;-o-transform-origin:50px 35px;transform-origin:50px 35px}.right-arm.jsx-6c62068782816f71{-webkit-animation:rightArmSwing.8s ease-in-out infinite;-moz-animation:rightArmSwing.8s ease-in-out infinite;-o-animation:rightArmSwing.8s ease-in-out infinite;animation:rightArmSwing.8s ease-in-out infinite;-webkit-transform-origin:50px 35px;-moz-transform-origin:50px 35px;-ms-transform-origin:50px 35px;-o-transform-origin:50px 35px;transform-origin:50px 35px}.left-leg.jsx-6c62068782816f71{-webkit-animation:leftLegWalk.8s ease-in-out infinite;-moz-animation:leftLegWalk.8s ease-in-out infinite;-o-animation:leftLegWalk.8s ease-in-out infinite;animation:leftLegWalk.8s ease-in-out infinite;-webkit-transform-origin:50px 70px;-moz-transform-origin:50px 70px;-ms-transform-origin:50px 70px;-o-transform-origin:50px 70px;transform-origin:50px 70px}.right-leg.jsx-6c62068782816f71{-webkit-animation:rightLegWalk.8s ease-in-out infinite;-moz-animation:rightLegWalk.8s ease-in-out infinite;-o-animation:rightLegWalk.8s ease-in-out infinite;animation:rightLegWalk.8s ease-in-out infinite;-webkit-transform-origin:50px 70px;-moz-transform-origin:50px 70px;-ms-transform-origin:50px 70px;-o-transform-origin:50px 70px;transform-origin:50px 70px}@-webkit-keyframes stickmanWalk{0%{top:80%;left:-70px;-webkit-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}25%{top:20%;left:25%;-webkit-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}50%{top:20%;left:-webkit-calc(100% + 70px);left:calc(100% + 70px);-webkit-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}75%{top:80%;left:75%;-webkit-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}100%{top:80%;left:-70px;-webkit-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}}@-moz-keyframes stickmanWalk{0%{top:80%;left:-70px;-moz-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}25%{top:20%;left:25%;-moz-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}50%{top:20%;left:-moz-calc(100% + 70px);left:calc(100% + 70px);-moz-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}75%{top:80%;left:75%;-moz-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}100%{top:80%;left:-70px;-moz-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}}@-o-keyframes stickmanWalk{0%{top:80%;left:-70px;-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}25%{top:20%;left:25%;-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}50%{top:20%;left:calc(100% + 70px);-o-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}75%{top:80%;left:75%;-o-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}100%{top:80%;left:-70px;-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}}@keyframes stickmanWalk{0%{top:80%;left:-70px;-webkit-transform:translatey(-50%)scalex(1);-moz-transform:translatey(-50%)scalex(1);-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}25%{top:20%;left:25%;-webkit-transform:translatey(-50%)scalex(1);-moz-transform:translatey(-50%)scalex(1);-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}50%{top:20%;left:-webkit-calc(100% + 70px);left:-moz-calc(100% + 70px);left:calc(100% + 70px);-webkit-transform:translatey(-50%)scalex(-1);-moz-transform:translatey(-50%)scalex(-1);-o-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}75%{top:80%;left:75%;-webkit-transform:translatey(-50%)scalex(-1);-moz-transform:translatey(-50%)scalex(-1);-o-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}100%{top:80%;left:-70px;-webkit-transform:translatey(-50%)scalex(1);-moz-transform:translatey(-50%)scalex(1);-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}}@-webkit-keyframes leftArmSwing{0%,100%{-webkit-transform:rotate(-20deg);transform:rotate(-20deg)}50%{-webkit-transform:rotate(20deg);transform:rotate(20deg)}}@-moz-keyframes leftArmSwing{0%,100%{-moz-transform:rotate(-20deg);transform:rotate(-20deg)}50%{-moz-transform:rotate(20deg);transform:rotate(20deg)}}@-o-keyframes leftArmSwing{0%,100%{-o-transform:rotate(-20deg);transform:rotate(-20deg)}50%{-o-transform:rotate(20deg);transform:rotate(20deg)}}@keyframes leftArmSwing{0%,100%{-webkit-transform:rotate(-20deg);-moz-transform:rotate(-20deg);-o-transform:rotate(-20deg);transform:rotate(-20deg)}50%{-webkit-transform:rotate(20deg);-moz-transform:rotate(20deg);-o-transform:rotate(20deg);transform:rotate(20deg)}}@-webkit-keyframes rightArmSwing{0%,100%{-webkit-transform:rotate(20deg);transform:rotate(20deg)}50%{-webkit-transform:rotate(-20deg);transform:rotate(-20deg)}}@-moz-keyframes rightArmSwing{0%,100%{-moz-transform:rotate(20deg);transform:rotate(20deg)}50%{-moz-transform:rotate(-20deg);transform:rotate(-20deg)}}@-o-keyframes rightArmSwing{0%,100%{-o-transform:rotate(20deg);transform:rotate(20deg)}50%{-o-transform:rotate(-20deg);transform:rotate(-20deg)}}@keyframes rightArmSwing{0%,100%{-webkit-transform:rotate(20deg);-moz-transform:rotate(20deg);-o-transform:rotate(20deg);transform:rotate(20deg)}50%{-webkit-transform:rotate(-20deg);-moz-transform:rotate(-20deg);-o-transform:rotate(-20deg);transform:rotate(-20deg)}}@-webkit-keyframes leftLegWalk{0%,100%{-webkit-transform:rotate(-15deg);transform:rotate(-15deg)}50%{-webkit-transform:rotate(15deg);transform:rotate(15deg)}}@-moz-keyframes leftLegWalk{0%,100%{-moz-transform:rotate(-15deg);transform:rotate(-15deg)}50%{-moz-transform:rotate(15deg);transform:rotate(15deg)}}@-o-keyframes leftLegWalk{0%,100%{-o-transform:rotate(-15deg);transform:rotate(-15deg)}50%{-o-transform:rotate(15deg);transform:rotate(15deg)}}@keyframes leftLegWalk{0%,100%{-webkit-transform:rotate(-15deg);-moz-transform:rotate(-15deg);-o-transform:rotate(-15deg);transform:rotate(-15deg)}50%{-webkit-transform:rotate(15deg);-moz-transform:rotate(15deg);-o-transform:rotate(15deg);transform:rotate(15deg)}}@-webkit-keyframes rightLegWalk{0%,100%{-webkit-transform:rotate(15deg);transform:rotate(15deg)}50%{-webkit-transform:rotate(-15deg);transform:rotate(-15deg)}}@-moz-keyframes rightLegWalk{0%,100%{-moz-transform:rotate(15deg);transform:rotate(15deg)}50%{-moz-transform:rotate(-15deg);transform:rotate(-15deg)}}@-o-keyframes rightLegWalk{0%,100%{-o-transform:rotate(15deg);transform:rotate(15deg)}50%{-o-transform:rotate(-15deg);transform:rotate(-15deg)}}@keyframes rightLegWalk{0%,100%{-webkit-transform:rotate(15deg);-moz-transform:rotate(15deg);-o-transform:rotate(15deg);transform:rotate(15deg)}50%{-webkit-transform:rotate(-15deg);-moz-transform:rotate(-15deg);-o-transform:rotate(-15deg);transform:rotate(-15deg)}}@media(max-width:768px){.stickman.jsx-6c62068782816f71{width:40px;height:48px}@-webkit-keyframes stickmanWalk{0%{top:70%;left:-60px;-webkit-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}25%{top:30%;left:30%;-webkit-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}50%{top:30%;left:-webkit-calc(100% + 60px);left:calc(100% + 60px);-webkit-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}75%{top:70%;left:70%;-webkit-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}100%{top:70%;left:-60px;-webkit-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}}@-moz-keyframes stickmanWalk{0%{top:70%;left:-60px;-moz-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}25%{top:30%;left:30%;-moz-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}50%{top:30%;left:-moz-calc(100% + 60px);left:calc(100% + 60px);-moz-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}75%{top:70%;left:70%;-moz-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}100%{top:70%;left:-60px;-moz-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}}@-o-keyframes stickmanWalk{0%{top:70%;left:-60px;-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}25%{top:30%;left:30%;-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}50%{top:30%;left:calc(100% + 60px);-o-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}75%{top:70%;left:70%;-o-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}100%{top:70%;left:-60px;-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}}@keyframes stickmanWalk{0%{top:70%;left:-60px;-webkit-transform:translatey(-50%)scalex(1);-moz-transform:translatey(-50%)scalex(1);-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}25%{top:30%;left:30%;-webkit-transform:translatey(-50%)scalex(1);-moz-transform:translatey(-50%)scalex(1);-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}50%{top:30%;left:-webkit-calc(100% + 60px);left:-moz-calc(100% + 60px);left:calc(100% + 60px);-webkit-transform:translatey(-50%)scalex(-1);-moz-transform:translatey(-50%)scalex(-1);-o-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}75%{top:70%;left:70%;-webkit-transform:translatey(-50%)scalex(-1);-moz-transform:translatey(-50%)scalex(-1);-o-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}100%{top:70%;left:-60px;-webkit-transform:translatey(-50%)scalex(1);-moz-transform:translatey(-50%)scalex(1);-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}}}.stickman-container.jsx-6c62068782816f71:hover{-webkit-animation-play-state:paused;-moz-animation-play-state:paused;-o-animation-play-state:paused;animation-play-state:paused}.stickman-container.jsx-6c62068782816f71:hover .left-arm.jsx-6c62068782816f71,.stickman-container.jsx-6c62068782816f71:hover .right-arm.jsx-6c62068782816f71,.stickman-container.jsx-6c62068782816f71:hover .left-leg.jsx-6c62068782816f71,.stickman-container.jsx-6c62068782816f71:hover .right-leg.jsx-6c62068782816f71{-webkit-animation-play-state:paused;-moz-animation-play-state:paused;-o-animation-play-state:paused;animation-play-state:paused}.stickman.jsx-6c62068782816f71 circle.jsx-6c62068782816f71:first-child{-webkit-animation:headBob.4s ease-in-out infinite;-moz-animation:headBob.4s ease-in-out infinite;-o-animation:headBob.4s ease-in-out infinite;animation:headBob.4s ease-in-out infinite;-webkit-transform-origin:center;-moz-transform-origin:center;-ms-transform-origin:center;-o-transform-origin:center;transform-origin:center}@-webkit-keyframes headBob{0%,100%{-webkit-transform:translatey(0px);transform:translatey(0px)}50%{-webkit-transform:translatey(-2px);transform:translatey(-2px)}}@-moz-keyframes headBob{0%,100%{-moz-transform:translatey(0px);transform:translatey(0px)}50%{-moz-transform:translatey(-2px);transform:translatey(-2px)}}@-o-keyframes headBob{0%,100%{-o-transform:translatey(0px);transform:translatey(0px)}50%{-o-transform:translatey(-2px);transform:translatey(-2px)}}@keyframes headBob{0%,100%{-webkit-transform:translatey(0px);-moz-transform:translatey(0px);-o-transform:translatey(0px);transform:translatey(0px)}50%{-webkit-transform:translatey(-2px);-moz-transform:translatey(-2px);-o-transform:translatey(-2px);transform:translatey(-2px)}}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n_c = StickManAnimation;\n/* harmony default export */ __webpack_exports__[\"default\"] = (StickManAnimation);\nvar _c;\n$RefreshReg$(_c, \"StickManAnimation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./Components/PaperRocketAnimation.jsx\n"));

/***/ })

});
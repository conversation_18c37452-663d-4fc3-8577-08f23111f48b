(()=>{var e={};e.id=1327,e.ids=[1327],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},33346:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var a=t(50482),r=t(69108),l=t(62563),o=t.n(l),i=t(68300),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);t.d(s,n);let c=["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,5521)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\contact\\page.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,39459)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\contact\\page.jsx"],m="/contact/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},81685:(e,s,t)=>{Promise.resolve().then(t.bind(t,97684))},28674:(e,s,t)=>{"use strict";t.d(s,{Z:()=>c});var a=t(95344),r=t(6880),l=t(41223),o=t.n(l),i=t(20783),n=t.n(i);t(3729);let c=()=>(0,a.jsxs)("div",{className:"flex justify-around flex-col gap-2 sm:gap-0 sm:flex-row bg-black py-5 items-center",children:[a.jsx(o(),{src:r.L.logo_light,alt:"Mr.Blogger",width:120}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex justify-center gap-4 mb-1",children:[a.jsx(n(),{href:"/about",className:"text-sm text-white hover:underline",children:"About Us"}),a.jsx(n(),{href:"/contact",className:"text-sm text-white hover:underline",children:"Contact Us"})]}),a.jsx("p",{className:"text-sm text-white",children:"All right reserved. Copyright @Mr.Blogger"})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx(o(),{src:r.L.facebook_icon,alt:"",width:40}),a.jsx(o(),{src:r.L.twitter_icon,alt:"",width:40}),a.jsx(o(),{src:r.L.googleplus_icon,alt:"",width:40})]})]})},97684:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var a=t(95344),r=t(6880),l=t(53608),o=t(41223),i=t.n(o),n=t(20783),c=t.n(n),d=t(22254),m=t(3729),x=t(69697);t(45996);var u=t(28674);let p=()=>{(0,d.useRouter)();let[e,s]=(0,m.useState)({name:"",email:"",message:""}),[t,o]=(0,m.useState)(!1),[n,p]=(0,m.useState)(!1),[h,g]=(0,m.useState)(!1),[b,j]=(0,m.useState)({email:"",password:""}),[f,v]=(0,m.useState)(!1),[w,y]=(0,m.useState)(!1);(0,m.useEffect)(()=>{let e=localStorage.getItem("rememberedEmail"),t=localStorage.getItem("rememberedPassword"),a="true"===localStorage.getItem("rememberMe");e&&t&&a&&(j({email:e,password:t}),y(!0));let r=localStorage.getItem("authToken"),o=localStorage.getItem("userId");if(!r||!o){p(!1),g(!0);return}p(!0),(async()=>{try{let e=await l.Z.get(`/api/profile?userId=${o}`);e.data.success&&s({name:e.data.user.name||"",email:e.data.user.email||"",message:""})}catch(e){console.error("Profile fetch error:",e)}})()},[]);let N=t=>{s({...e,[t.target.name]:t.target.value})},k=async t=>{if(t.preventDefault(),!n){g(!0);return}try{o(!0),(await l.Z.post("/api/feedback",e)).data.success&&(x.toast.success("Feedback submitted successfully"),s({...e,message:""}))}catch(e){x.toast.error(e.response?.data?.message||"Failed to submit feedback")}finally{o(!1)}},S=e=>{j({...b,[e.target.name]:e.target.value})},q=async e=>{e.preventDefault();try{let e=await l.Z.post("/api/auth",{email:b.email,password:b.password});e.data.success?(localStorage.setItem("authToken",e.data.token||"dummy-token"),localStorage.setItem("userRole",e.data.user.role),localStorage.setItem("userId",e.data.user.id),w?(localStorage.setItem("rememberedEmail",b.email),localStorage.setItem("rememberedPassword",b.password),localStorage.setItem("rememberMe","true")):(localStorage.removeItem("rememberedEmail"),localStorage.removeItem("rememberedPassword"),localStorage.removeItem("rememberMe")),x.toast.success("Login successful"),p(!0),g(!1),s({name:e.data.user.name||"",email:e.data.user.email||"",message:""})):x.toast.error("Invalid credentials")}catch(e){console.error("Login error:",e),x.toast.error(e.response?.data?.message||"Login failed")}};return(0,a.jsxs)(a.Fragment,{children:[a.jsx(x.ToastContainer,{theme:"dark"}),(0,a.jsxs)("div",{className:"bg-gray-200 py-5 px-5 md:px-12 lg:px-28",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx(c(),{href:"/",children:a.jsx(i(),{src:r.L.logo,width:180,alt:"Mr.Blogger",className:"w-[130px] sm:w-auto"})}),a.jsx(c(),{href:"/",children:a.jsx("button",{className:"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]",children:"Back to Home"})})]}),(0,a.jsxs)("div",{className:"text-center my-16",children:[a.jsx("h1",{className:"text-3xl sm:text-5xl font-semibold",children:"Contact Us"}),a.jsx("p",{className:"mt-4 text-gray-600",children:"We'd love to hear from you. Send us a message!"})]})]}),h&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white p-8 rounded-lg max-w-md w-full",children:[a.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Login Required"}),a.jsx("p",{className:"mb-4",children:"Please login to submit feedback"}),(0,a.jsxs)("form",{onSubmit:q,children:[(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("label",{className:"block text-gray-700 mb-2",children:"Email"}),a.jsx("input",{type:"email",name:"email",value:b.email,onChange:S,className:"w-full px-3 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("label",{className:"block text-gray-700 mb-2",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("input",{type:f?"text":"password",name:"password",value:b.password,onChange:S,className:"w-full px-3 py-2 border border-gray-300 rounded-md pr-10",required:!0}),a.jsx("button",{type:"button",onClick:()=>{v(!f)},className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600",children:f?(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[a.jsx("path",{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}),a.jsx("path",{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"})]}):(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[a.jsx("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),a.jsx("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]})})]})]}),a.jsx("div",{className:"mb-4",children:(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:w,onChange:e=>y(e.target.checked),className:"mr-2"}),a.jsx("span",{className:"text-sm text-gray-700",children:"Remember me"})]})}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("button",{type:"submit",className:"bg-black text-white px-4 py-2 rounded-md",children:"Login"}),a.jsx(c(),{href:"/",children:a.jsx("button",{type:"button",className:"text-gray-600 px-4 py-2",children:"Cancel"})})]})]})]})}),a.jsx("div",{className:"container mx-auto px-4 py-12 max-w-4xl",children:a.jsx("div",{className:"bg-white rounded-lg shadow-md p-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"Get in Touch"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"Have questions, suggestions, or feedback? Fill out the form and we'll get back to you as soon as possible."}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"bg-black p-3 rounded-full mr-4",children:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium",children:"Email"}),a.jsx("p",{className:"text-gray-600",children:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"bg-black p-3 rounded-full mr-4",children:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium",children:"Phone"}),a.jsx("p",{className:"text-gray-600",children:"+****************"})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"bg-black p-3 rounded-full mr-4",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium",children:"Address"}),a.jsx("p",{className:"text-gray-600",children:"123 Blog Street, Content City, 10001"})]})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"Send a Message"}),n?(0,a.jsxs)("form",{onSubmit:k,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:"Your Name"}),a.jsx("input",{type:"text",id:"name",name:"name",placeholder:"John Doe",value:e.name,onChange:N,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black",required:!0,readOnly:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Your Email"}),a.jsx("input",{type:"email",id:"email",name:"email",placeholder:"<EMAIL>",value:e.email,onChange:N,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black",required:!0,readOnly:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-1",children:"Your Message"}),a.jsx("textarea",{id:"message",name:"message",placeholder:"How can we help you?",value:e.message,onChange:N,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black min-h-[150px]",required:!0})]}),a.jsx("button",{type:"submit",disabled:t,className:"w-full bg-black text-white py-3 px-4 rounded-md font-medium hover:bg-gray-800 transition disabled:opacity-70",children:t?"Sending...":"Send Message"})]}):(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx("p",{className:"text-gray-600 mb-4",children:"Please log in to send us a message"}),a.jsx("button",{onClick:()=>g(!0),className:"bg-black text-white py-2 px-6 rounded-md font-medium hover:bg-gray-800 transition",children:"Login"})]})]})]})})}),a.jsx(u.Z,{})]})}},5521:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>r,default:()=>o});let a=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\contact\page.jsx`),{__esModule:r,$$typeof:l}=a,o=a.default},45996:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,3998,337,8468,5757],()=>t(33346));module.exports=a})();
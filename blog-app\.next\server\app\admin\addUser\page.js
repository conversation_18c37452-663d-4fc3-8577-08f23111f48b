(()=>{var e={};e.id=8858,e.ids=[8858],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},15124:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>n});var a=r(50482),t=r(69108),l=r(62563),o=r.n(l),d=r(68300),i={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);r.d(s,i);let n=["",{children:["admin",{children:["addUser",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,16349)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\addUser\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,32143)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,39459)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\addUser\\page.jsx"],u="/admin/addUser/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/admin/addUser/page",pathname:"/admin/addUser",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},98915:(e,s,r)=>{Promise.resolve().then(r.bind(r,35470))},35470:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d});var a=r(95344),t=r(53608),l=r(3729),o=r(69697);let d=()=>{let[e,s]=(0,l.useState)("add"),[r,d]=(0,l.useState)({email:"",password:"",confirmPassword:"",role:"user"}),[i,n]=(0,l.useState)([]),[c,u]=(0,l.useState)([]),[m,p]=(0,l.useState)(!1),[x,h]=(0,l.useState)("all"),[g,b]=(0,l.useState)("");(0,l.useEffect)(()=>{"manage"===e&&y()},[e]),(0,l.useEffect)(()=>{f()},[i,x,g]);let y=async()=>{try{p(!0);let e=await t.Z.get("/api/users");e.data.success?n(e.data.users):o.toast.error("Failed to load users")}catch(e){console.error("Error fetching users:",e),o.toast.error("Failed to load users")}finally{p(!1)}},f=()=>{let e=[...i];if("all"!==x&&(e=e.filter(e=>e.role===x)),g.trim()){let s=g.toLowerCase();e=e.filter(e=>e.email.toLowerCase().includes(s))}u(e)},j=e=>{d({...r,[e.target.name]:e.target.value})},w=async e=>{if(e.preventDefault(),r.password!==r.confirmPassword){o.toast.error("Passwords do not match");return}try{p(!0);let e=await t.Z.post("/api/register",{email:r.email,password:r.password,role:r.role});e.data.success?(o.toast.success("User created successfully"),d({email:"",password:"",confirmPassword:"",role:"user"}),s("manage")):o.toast.error(e.data.message||"Failed to create user")}catch(e){console.error("User creation error:",e),o.toast.error(e.response?.data?.message||"Failed to create user")}finally{p(!1)}},v=async e=>{if(window.confirm("Are you sure you want to delete this user?"))try{p(!0);let s=await t.Z.delete("/api/users",{params:{id:e}});s.data.success?(o.toast.success("User deleted successfully"),s.data.users?n(s.data.users):await y()):o.toast.error(s.data.message||"Failed to delete user")}catch(e){console.error("Error deleting user:",e),o.toast.error(e.response?.data?.message||"Failed to delete user")}finally{p(!1)}};return(0,a.jsxs)("div",{className:"pt-5 px-5 sm:pt-12 sm:pl-16",children:[a.jsx("h1",{className:"text-2xl font-bold mb-6",children:"User Management"}),(0,a.jsxs)("div",{className:"flex border-b border-gray-300 mb-6",children:[a.jsx("button",{className:`py-3 px-6 font-medium rounded-t-lg ${"add"===e?"bg-black text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,onClick:()=>s("add"),children:"Add New User"}),a.jsx("button",{className:`py-3 px-6 font-medium rounded-t-lg ml-2 ${"manage"===e?"bg-black text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,onClick:()=>s("manage"),children:"Manage Users"})]}),"add"===e&&(0,a.jsxs)("form",{onSubmit:w,className:"max-w-[500px] bg-white p-6 rounded-lg shadow-md",children:[a.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Create New User Account"}),(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("label",{className:"block text-gray-700 mb-2",children:"Email"}),a.jsx("input",{type:"email",name:"email",value:r.email,onChange:j,className:"w-full px-4 py-3 border border-gray-300 rounded-md",placeholder:"<EMAIL>",required:!0})]}),(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("label",{className:"block text-gray-700 mb-2",children:"Password"}),a.jsx("input",{type:"password",name:"password",value:r.password,onChange:j,className:"w-full px-4 py-3 border border-gray-300 rounded-md",placeholder:"Enter password",required:!0})]}),(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("label",{className:"block text-gray-700 mb-2",children:"Confirm Password"}),a.jsx("input",{type:"password",name:"confirmPassword",value:r.confirmPassword,onChange:j,className:"w-full px-4 py-3 border border-gray-300 rounded-md",placeholder:"Confirm password",required:!0})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("label",{className:"block text-gray-700 mb-2",children:"User Role"}),(0,a.jsxs)("select",{name:"role",value:r.role,onChange:j,className:"w-full px-4 py-3 border border-gray-300 rounded-md",required:!0,children:[a.jsx("option",{value:"user",children:"User"}),a.jsx("option",{value:"admin",children:"Admin"})]})]}),a.jsx("button",{type:"submit",className:"w-full py-3 bg-black text-white rounded-md hover:bg-gray-800 transition-colors",disabled:m,children:m?"Creating...":"Create User"})]}),"manage"===e&&(0,a.jsxs)("div",{className:"max-w-[800px] bg-white p-6 rounded-lg shadow-md",children:[a.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Manage User Accounts"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-[200px]",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search by Email"}),a.jsx("input",{type:"text",value:g,onChange:e=>b(e.target.value),placeholder:"Search users...",className:"w-full px-3 py-2 border border-gray-300 rounded-md"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Filter by Role"}),(0,a.jsxs)("select",{value:x,onChange:e=>h(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md",children:[a.jsx("option",{value:"all",children:"All Users"}),a.jsx("option",{value:"admin",children:"Admins Only"}),a.jsx("option",{value:"user",children:"Regular Users Only"})]})]})]}),m?(0,a.jsxs)("div",{className:"flex justify-center items-center py-8",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-black"}),a.jsx("span",{className:"ml-2",children:"Loading users..."})]}):c.length>0?a.jsx("div",{className:"relative overflow-x-auto border border-gray-300 rounded-lg",children:(0,a.jsxs)("table",{className:"w-full text-sm text-left text-gray-500",children:[a.jsx("thead",{className:"text-xs text-gray-700 uppercase bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{scope:"col",className:"px-6 py-3",children:"Email"}),a.jsx("th",{scope:"col",className:"px-6 py-3",children:"Role"}),a.jsx("th",{scope:"col",className:"px-6 py-3",children:"Actions"})]})}),a.jsx("tbody",{children:c.map(e=>(0,a.jsxs)("tr",{className:"bg-white border-b hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4",children:e.email}),a.jsx("td",{className:"px-6 py-4",children:a.jsx("span",{className:`px-2 py-1 rounded-full text-xs ${"admin"===e.role?"bg-blue-100 text-blue-800":"bg-gray-100"}`,children:e.role})}),a.jsx("td",{className:"px-6 py-4",children:a.jsx("button",{onClick:()=>v(e._id),className:"text-white bg-red-600 hover:bg-red-700 px-3 py-1 rounded-md text-sm",disabled:m,children:"Delete"})})]},e._id))})]})}):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[a.jsx("p",{children:0===i.length?"No users found.":"No users match your filters."}),0===i.length&&a.jsx("button",{onClick:()=>s("add"),className:"mt-4 text-blue-600 hover:underline",children:"Add your first user"}),i.length>0&&a.jsx("button",{onClick:()=>{h("all"),b("")},className:"mt-4 text-blue-600 hover:underline",children:"Clear filters"})]}),i.length>0&&a.jsx("div",{className:"mt-4 text-sm text-gray-500",children:(0,a.jsxs)("p",{children:["Showing ",c.length," of ",i.length," users","all"!==x&&` (filtered by ${x} role)`,g&&" (filtered by search)"]})})]})]})}},16349:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>l,__esModule:()=>t,default:()=>o});let a=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\admin\addUser\page.jsx`),{__esModule:t,$$typeof:l}=a,o=a.default}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[1638,3998,337,8468,5757,7388],()=>r(15124));module.exports=a})();
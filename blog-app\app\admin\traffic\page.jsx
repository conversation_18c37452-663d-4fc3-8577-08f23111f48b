'use client'
import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { fetchAnalytics, formatNumber } from '@/utils/analyticsUtils';
import Link from 'next/link';
import axios from 'axios';

const TrafficAnalytics = () => {
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('7days');
  const [analytics, setAnalytics] = useState({
    totalViews: 0,
    uniqueVisitors: 0,
    viewsByType: [],
    topPages: [],
    trafficOverTime: [],
    topReferrers: []
  });
  
  const getAnalyticsData = async () => {
    try {
      setLoading(true);
      console.log('Fetching analytics data for period:', period);
      
      const data = await fetchAnalytics(period);
      console.log('Analytics data received:', data);
      
      if (data.success) {
        setAnalytics(data.data);
        console.log('Analytics data set to state:', data.data);
      } else {
        toast.error('Failed to load analytics data');
        console.error('Failed to load analytics data:', data.message);
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
      toast.error('Failed to load analytics data');
      
      // If unauthorized, redirect to login
      if (error.response?.status === 401) {
        toast.error('Please log in as admin to view analytics');
        // Redirect to login page or home page
        window.location.href = '/';
      }
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    getAnalyticsData();
  }, [period]);
  
  const handlePeriodChange = (e) => {
    setPeriod(e.target.value);
  };
  
  const handleRefresh = () => {
    toast.info('Refreshing analytics data...');
    getAnalyticsData();
  };
  
  return (
    <div className="flex-1 pt-5 px-5 sm:pt-12 sm:pl-16">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Traffic Analytics</h1>
        
        {/* Refresh Button */}
        <button
          onClick={handleRefresh}
          disabled={loading}
          className="flex items-center gap-2 px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition-colors disabled:opacity-50"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M23 4v6h-6"></path>
            <path d="M1 20v-6h6"></path>
            <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
            <path d="M20.49 15a9 9 0 0 1-14.85 3.36L1 14"></path>
          </svg>
          {loading ? 'Refreshing...' : 'Refresh Data'}
        </button>
      </div>
      
      {/* Period selector */}
      <div className="mb-6">
        <label htmlFor="period" className="block text-sm font-medium text-gray-700 mb-1">Time Period</label>
        <select
          id="period"
          value={period}
          onChange={handlePeriodChange}
          className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="24hours">Last 24 Hours</option>
          <option value="7days">Last 7 Days</option>
          <option value="30days">Last 30 Days</option>
          <option value="90days">Last 90 Days</option>
        </select>
      </div>
      
      {loading ? (
        <div className="text-center py-10">
          <p className="text-gray-500">Loading analytics data...</p>
        </div>
      ) : (
        <>
          {/* Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg border border-gray-300 shadow-md">
              <h2 className="text-lg font-semibold text-gray-700">Total Page Views</h2>
              <p className="text-3xl font-bold mt-2">{formatNumber(analytics.totalViews)}</p>
              <p className="text-sm text-gray-500 mt-1">All pages combined</p>
            </div>
            
            <div className="bg-white p-6 rounded-lg border border-gray-300 shadow-md">
              <h2 className="text-lg font-semibold text-gray-700">Unique Visitors</h2>
              <p className="text-3xl font-bold mt-2">{formatNumber(analytics.uniqueVisitors)}</p>
              <p className="text-sm text-gray-500 mt-1">Based on unique IP addresses</p>
            </div>
            
            <div className="bg-white p-6 rounded-lg border border-gray-300 shadow-md">
              <h2 className="text-lg font-semibold text-gray-700">Pages Per Visitor</h2>
              <p className="text-3xl font-bold mt-2">
                {analytics.uniqueVisitors > 0 
                  ? (analytics.totalViews / analytics.uniqueVisitors).toFixed(1) 
                  : '0.0'}
              </p>
              <p className="text-sm text-gray-500 mt-1">Average pages viewed</p>
            </div>
          </div>
          
          {/* Content Type Breakdown */}
          <div className="bg-white p-6 rounded-lg border border-gray-300 shadow-md mb-8">
            <h2 className="text-lg font-semibold text-gray-700 mb-4">Content Type Breakdown</h2>
            
            {analytics.viewsByType.length === 0 ? (
              <p className="text-gray-500">No content type data available</p>
            ) : (
              <div className="space-y-3">
                {analytics.viewsByType.map((item) => (
                  <div key={item._id} className="flex justify-between items-center">
                    <span className="capitalize">{item._id}</span>
                    <span className="font-semibold">{formatNumber(item.count)} views</span>
                  </div>
                ))}
              </div>
            )}
          </div>
          
          {/* Top Pages */}
          <div className="bg-white p-6 rounded-lg border border-gray-300 shadow-md mb-8">
            <h2 className="text-lg font-semibold text-gray-700 mb-4">Top Pages</h2>
            
            {analytics.topPages.length === 0 ? (
              <p className="text-gray-500">No page view data available</p>
            ) : (
              <div className="space-y-3">
                {analytics.topPages.map((page) => (
                  <div key={page._id} className="flex justify-between items-center">
                    <span className="truncate max-w-md">{page._id}</span>
                    <span className="font-semibold">{formatNumber(page.count)} views</span>
                  </div>
                ))}
              </div>
            )}
          </div>
          
          {/* Top Referrers */}
          <div className="bg-white p-6 rounded-lg border border-gray-300 shadow-md mb-8">
            <h2 className="text-lg font-semibold text-gray-700 mb-4">Top Referrers</h2>
            
            {analytics.topReferrers.length === 0 ? (
              <p className="text-gray-500">No referrer data available</p>
            ) : (
              <div className="space-y-3">
                {analytics.topReferrers.map((referrer) => (
                  <div key={referrer._id} className="flex justify-between items-center">
                    <span className="truncate max-w-md">{referrer._id}</span>
                    <span className="font-semibold">{formatNumber(referrer.count)} visits</span>
                  </div>
                ))}
              </div>
            )}
          </div>
          
          {/* No Data Message */}
          {analytics.totalViews === 0 && (
            <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-md">
              <h3 className="font-medium text-yellow-800 mb-2">No analytics data available</h3>
              <p className="text-yellow-700">
                This could be because:
              </p>
              <ul className="list-disc ml-5 mt-2 text-yellow-700">
                <li>No page views have been tracked yet</li>
                <li>Analytics tracking is disabled in development mode</li>
                <li>The selected time period doesn&apos;t contain any data</li>
              </ul>
              <p className="mt-3 text-yellow-700">
                Try visiting some pages on your site or switching to a different time period.
              </p>
            </div>
          )}
          
          {/* Debug Button - Remove in production */}
          <div className="mt-8 border-t pt-4">
            <h3 className="text-lg font-semibold mb-2">Debug Tools</h3>
            <button
              onClick={async () => {
                try {
                  const token = localStorage.getItem('authToken');
                  console.log('Current auth token:', token ? 'Token exists' : 'No token');
                  
                  // Check analytics data
                  const analyticsDebug = await axios.get('/api/analytics/debug');
                  console.log('Analytics debug info:', analyticsDebug.data);
                  
                  toast.info('Debug info logged to console');
                } catch (error) {
                  console.error('Debug check failed:', error);
                  toast.error('Debug check failed');
                }
              }}
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded"
            >
              Run Diagnostics
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default TrafficAnalytics;

// Add this near the end of your component, before the final closing tag
{/* Debug Button - Remove in production */}
<div className="mt-8 border-t pt-4">
  <h3 className="text-lg font-semibold mb-2">Debug Tools</h3>
  <button
    onClick={async () => {
      try {
        const token = localStorage.getItem('authToken');
        console.log('Current auth token:', token ? 'Token exists' : 'No token');
        
        // Check token validity
        const debugResponse = await axios.get('/api/debug', {
          headers: { Authorization: `Bearer ${token}` }
        });
        console.log('Token debug info:', debugResponse.data);
        
        // Check analytics data
        const analyticsDebug = await axios.get('/api/analytics/debug');
        console.log('Analytics debug info:', analyticsDebug.data);
        
        toast.info('Debug info logged to console');
      } catch (error) {
        console.error('Debug check failed:', error);
        toast.error('Debug check failed');
      }
    }}
    className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded"
  >
    Run Diagnostics
  </button>
</div>




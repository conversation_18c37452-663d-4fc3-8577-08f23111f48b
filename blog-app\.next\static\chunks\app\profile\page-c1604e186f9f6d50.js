(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[178],{5930:function(e,t,a){Promise.resolve().then(a.bind(a,4540))},4257:function(e,t,a){"use strict";a.d(t,{L:function(){return r}});let r={facebook_icon:{src:"/_next/static/media/facebook_icon.cbcfc36d.png",height:58,width:58,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAbUlEQVR42m3HsRGCQBCG0X/sgArowytFG9BCqIESnLEHAhswNrqYmcvkAmB3P4Yh5WVPXCRP3ntvV2mf7B7sArtJGhsvkJfPAl7GRjUZxIs3hFOTcmsVvutvBZtyK+nfgRPA1OlQHvMwD+WpMxvnWUuxSavcBwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},googleplus_icon:{src:"/_next/static/media/googleplus_icon.15e2de32.png",height:59,width:59,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAe0lEQVR42mP4z8TA8Mv+z5x/M36bMzCAeAy/k/5Dwe9gBgaGZyL/P3z6f/3Xv59///97fIOT4b3d///H/s76v/3/g///f77WY7il8P/r+/+Hf73/9f//39dnhBkYGD41/f8PAv/+fyphgICXSV+3fNv4IoIBHfxnZGAAALhiS7/aN4AvAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},twitter_icon:{src:"/_next/static/media/twitter_icon.0d1dc581.png",height:59,width:59,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAeklEQVR42l3HvQnCUBiG0Uc7ax1AsLe10yFsrDODEziFhStYCO5gIThCGkHEgJqfC8mX5L4hJFVOd9AYbFOfyqOtoB1loJ5tgddM/0w/uxVOet4nxGspqtFCuWTfJeHcu0pnC02qoscUSA9eLa9kT+cTuKu7vHcMaQQNWbdKicv1GyQAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},profile_icon:{src:"/_next/static/media/profile_icon.fa2679c4.png",height:92,width:92,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABCklEQVR42iWPMU/CUACEn7/B+CPen3CS+BccTNyMm2kdpDoYiFEjVkgcQGOVONRookZUGlqf5Ukk9CkVU4ymxk2CU/e+5Shw2919NxwZqtf7o93ggzPmSKt6L5vNBvf9Nzoq+/1/KjwvUlUFiQWZINC0NFyXRcmAkjAMeTabwewUiRvlPMyt9BCMS6Ui6i7jxG+35fRMCgVlHt+VM7DDHFKTBHv6PhzHlqQbBHJ7N4eTDQW/1iWqO2vIryyi5jhgjwkghMc9IfBwexV/Xp/CXF5A7e4mfu908MRsTl5Fi9Y5j1ovz/ixL2CocyhurqJsHEWWbY+fZPQCNY8P+Jd1Liu6Jo31JZ7Eo3IAXaWfc0g8AF4AAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},logo:{src:"/_next/static/media/logo.c649e147.png",height:53,width:186,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAQAAADPnVVmAAAAKklEQVR42mNgaGdIY9BhcGQwZjBgMGHQYWCoZWhnMGSwY3BjyGSIYjAAAFAcBMpReGCWAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:2},arrow:{src:"/_next/static/media/arrow.35bdbbc1.png",height:16,width:18,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAQAAACfUMTVAAAATUlEQVR42mPQZNRgZGDQ4NC4qeHPwKDJxgADGvYazzRcGRgYNLk1eTR4NPkZGDS8NF5o+DBoHtI4p3lW44zmFY1tGp80fmGowDQD3RYA4awVkVQ4JrAAAAAASUVORK5CYII=",blurWidth:8,blurHeight:7},logo_light:{src:"/_next/static/media/logo_light.9ce1f99e.png",height:55,width:201,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAQAAADPnVVmAAAALElEQVR42mP41/Av9J/uP7d/5v8s/tn8M2X41/Sv9p/OP9t/rv9y/0X/MwIAZagUsO6duCoAAAAASUVORK5CYII=",blurWidth:8,blurHeight:2},blog_icon:{src:"/_next/static/media/blog_icon.6cf97bbc.png",height:32,width:32,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAVklEQVR42jXNMRGDQBAAwC2pGfozgA4KcjMMFIejN5IoSI8YLGAAKtbAQiil0xshNGky2J1GygccLrue1YKf22HQsUn8fTEpygwgFaGZpUVq4m03qxI8rIYRbx4WRDgAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},add_icon:{src:"/_next/static/media/add_icon.17426346.png",height:32,width:32,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAARElEQVR42mWNsQnAMAwEr3LwUMoGmiJKlf37HMZgg/+aP4EkRpKSZOYhaBI2kxboAqFRNOWTzqXxroGtILn3lePo8fYH8E4LJKezO8EAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},email_icon:{src:"/_next/static/media/email_icon.4caec7c6.png",height:32,width:32,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAUUlEQVR42l3NsQmAMAAEwHONrCBqlVJrG1dQ1MIt4pZmHQMBC7nu4f9pAEADg9lSzDoIsmQskiwQ7S5tcTlEotPmlqw1CB63qagV9N/ogP/tC+8IDv7EJZnRAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},upload_area:{src:"/_next/static/media/upload_area.1ee5fe3d.png",height:140,width:240,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAMAAABPT11nAAAANlBMVEX6+vr6+vn5+fn5+fj5+Pj4+Pj39/f29vb19fXz8/Py8vLv7+/u7u7r6+rp6ejj5evf4OXc3+fsgmBfAAAALUlEQVR42g3GtwEAIAwDMFMc0wn/P0s0Cc1UqqzBiPsSDWJ3HxSU19kzR8QgfRm1AShVawqCAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:5}};Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now()},4540:function(e,t,a){"use strict";a.r(t);var r=a(7437),s=a(2265),o=a(4033),l=a(7948),i=a(1396),n=a.n(i),d=a(6691),c=a.n(d),m=a(2173),u=a(4257),g=a(8347),A=a(5535);t.default=()=>{let e=(0,o.useRouter)(),[t,a]=(0,s.useState)(!0),[i,d]=(0,s.useState)({name:"",email:"",profilePicture:"/default_profile.png"}),[h,p]=(0,s.useState)(null),[x,b]=(0,s.useState)("profile"),[f,w]=(0,s.useState)(!1),[y,v]=(0,s.useState)(null),[j,N]=(0,s.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[C,k]=(0,s.useState)(!1),[P,S]=(0,s.useState)([]),[R,U]=(0,s.useState)(!1),[B,D]=(0,s.useState)(!1),[I,E]=(0,s.useState)(!1),[V,Q]=(0,s.useState)(null),[L,G]=(0,s.useState)({x:0,y:0}),[_,F]=(0,s.useState)(1),[M,H]=(0,s.useState)(null),[O,Y]=(0,s.useState)(null),W=()=>{w(!0)};(0,s.useEffect)(()=>{let t=localStorage.getItem("authToken"),r=localStorage.getItem("userId");if(!t||!r){l.toast.error("Please log in to view your profile"),e.push("/");return}(async()=>{try{let e=await m.Z.get("/api/profile?userId=".concat(r));e.data.success?d({id:e.data.user.id,email:e.data.user.email,name:e.data.user.name||"",role:e.data.user.role,profilePicture:e.data.user.profilePicture}):l.toast.error(e.data.message||"Failed to load profile")}catch(e){console.error("Profile fetch error:",e),l.toast.error("Failed to load profile data")}finally{a(!1)}})(),"favorites"===x&&K(t)},[e,x]);let K=async e=>{try{U(!0);let t=await m.Z.get("/api/favorites",{headers:{Authorization:"Bearer ".concat(e)}});t.data.success?S(t.data.favorites||[]):l.toast.error("Failed to load favorites")}catch(e){console.error("Error fetching favorites:",e),l.toast.error("Failed to load favorites")}finally{U(!1)}},T=async e=>{try{let t=localStorage.getItem("authToken");(await m.Z.delete("/api/favorites?blogId=".concat(e),{headers:{Authorization:"Bearer ".concat(t)}})).data.success?(S(P.filter(t=>t._id.toString()!==e)),l.toast.success("Removed from favorites")):l.toast.error("Failed to remove from favorites")}catch(e){console.error("Error removing favorite:",e),l.toast.error("Failed to remove from favorites")}},Z=async(e,t)=>{let a=new(c());a.src=e;let r=document.createElement("canvas"),s=r.getContext("2d");return r.width=t.width,r.height=t.height,s.drawImage(a,t.x,t.y,t.width,t.height,0,0,t.width,t.height),new Promise(e=>{r.toBlob(t=>{e(t)},"image/jpeg")})},q=async()=>{try{if(!M)return;let e=await Z(V,M),t=URL.createObjectURL(e);p(t);let a=new File([e],"cropped_profile.jpg",{type:"image/jpeg"});v(a),E(!1)}catch(e){console.error("Error applying crop:",e),l.toast.error("Failed to crop image")}},z=async e=>{e.preventDefault(),D(!1);try{let e=new FormData;e.append("userId",i.id),e.append("name",i.name),y&&e.append("profilePicture",y);let t=await m.Z.put("/api/profile",e);t.data.success?(localStorage.setItem("userName",t.data.user.name),localStorage.setItem("userProfilePicture",t.data.user.profilePicture),d({...i,name:t.data.user.name,profilePicture:t.data.user.profilePicture}),v(null),p(null),D(!0),l.toast.success("Profile updated successfully"),setTimeout(()=>{window.location.reload()},1e3)):l.toast.error(t.data.message||"Failed to update profile")}catch(e){console.error("Profile update error:",e),l.toast.error("Failed to update profile")}},J=e=>{N({...j,[e.target.name]:e.target.value})},X=async e=>{if(e.preventDefault(),j.newPassword!==j.confirmPassword){l.toast.error("New passwords do not match");return}try{let e=await m.Z.put("/api/password",{userId:i.id,currentPassword:j.currentPassword,newPassword:j.newPassword});e.data.success?(l.toast.success("Password updated successfully"),N({currentPassword:"",newPassword:"",confirmPassword:""}),k(!1)):l.toast.error(e.data.message||"Failed to update password")}catch(e){var t,a;console.error("Password update error:",e),l.toast.error((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"Failed to update password")}};return t?(0,r.jsx)("div",{className:"min-h-screen flex justify-center items-center",children:(0,r.jsx)("p",{children:"Loading profile..."})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"bg-gray-200 py-5 px-5 md:px-12 lg:px-28",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)(n(),{href:"/",children:(0,r.jsx)(c(),{src:u.L.logo,width:180,alt:"Mr.Blogger",className:"w-[130px] sm:w-auto"})}),(0,r.jsx)(n(),{href:"/",children:(0,r.jsx)("button",{className:"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]",children:"Back to Home"})})]}),(0,r.jsxs)("div",{className:"text-center my-16",children:[(0,r.jsx)("h1",{className:"text-3xl sm:text-5xl font-semibold",children:"Your Profile"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Manage your account and preferences"})]})]}),(0,r.jsx)("div",{className:"min-h-screen bg-gray-100 py-8",children:(0,r.jsx)("div",{className:"container mx-auto px-4 max-w-4xl",children:(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"flex border-b border-gray-300",children:[(0,r.jsx)("button",{onClick:()=>b("profile"),className:"py-3 px-6 font-medium ".concat("profile"===x?"border-b-2 border-black text-black":"text-gray-500 hover:text-gray-700"),children:"Profile"}),(0,r.jsx)("button",{onClick:()=>b("favorites"),className:"py-3 px-6 font-medium ".concat("favorites"===x?"border-b-2 border-black text-black":"text-gray-500 hover:text-gray-700"),children:"My Favorites"})]})}),"profile"===x?(0,r.jsxs)("form",{onSubmit:z,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center mb-8",children:[(0,r.jsxs)("div",{className:"relative mb-4",children:[(0,r.jsx)(c(),{src:h||i.profilePicture,width:150,height:150,alt:"Profile",className:"rounded-full object-cover w-[150px] h-[150px] border-4 border-gray-200"}),(0,r.jsx)("label",{htmlFor:"profilePicture",className:"absolute bottom-0 right-0 bg-black text-white p-2 rounded-full cursor-pointer",children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,r.jsx)("path",{d:"M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"}),(0,r.jsx)("circle",{cx:"12",cy:"13",r:"4"})]})}),(0,r.jsx)("input",{type:"file",id:"profilePicture",onChange:e=>{let t=e.target.files[0];if(t){let e=new FileReader;e.onloadend=()=>{Q(e.result),E(!0)},e.readAsDataURL(t)}},className:"hidden",accept:"image/*"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Click the camera icon to change your profile picture"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"Email"}),(0,r.jsx)("input",{type:"email",value:i.email,className:"w-full px-4 py-3 border border-gray-300 rounded-md bg-gray-100",disabled:!0}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Email cannot be changed"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"Display Name"}),(0,r.jsx)("input",{type:"text",value:i.name,onChange:e=>{d({...i,name:e.target.value})},className:"w-full px-4 py-3 border border-gray-300 rounded-md",placeholder:"Enter your name"})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4",children:[B&&(0,r.jsx)("div",{className:"fixed top-5 right-5 p-4 bg-white border-l-4 border-green-500 text-green-700 rounded shadow-lg z-50 max-w-xs animate-fade-in",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"mr-3",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-bold",children:"Success!"}),(0,r.jsx)("p",{className:"text-sm",children:"Profile updated successfully."})]})]})}),(0,r.jsx)("button",{type:"submit",className:"px-6 py-3 bg-black text-white font-medium rounded-md hover:bg-gray-800 transition-colors",children:"Save Changes"}),(0,r.jsx)("button",{type:"button",onClick:()=>k(!C),className:"px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 transition-colors",children:"Change Password"}),"admin"===i.role&&(0,r.jsx)("button",{type:"button",onClick:t=>{t.preventDefault(),e.push("/admin")},className:"px-6 py-3 bg-black text-white font-medium rounded-md hover:bg-gray-800 transition-colors",children:"Dashboard"}),(0,r.jsx)("button",{type:"button",onClick:e=>{e.preventDefault(),W()},className:"px-6 py-3 bg-red-600 text-white font-medium rounded-md hover:bg-red-700 transition-colors",children:"Logout"}),(0,r.jsx)("button",{type:"button",onClick:t=>{t.preventDefault(),e.push("/")},className:"px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 transition-colors",children:"Cancel"})]})]}):(0,r.jsx)("div",{children:R?(0,r.jsx)("div",{className:"flex justify-center items-center py-16",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-black"})}):P.length>0?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:P.map(e=>(0,r.jsxs)("div",{className:"bg-white rounded-lg overflow-hidden shadow-md border border-gray-200",children:[(0,r.jsx)("div",{className:"relative h-48",children:(0,r.jsx)(c(),{src:e.image||u.L.placeholder,alt:e.title,fill:!0,className:"object-cover"})}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,r.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 rounded-full",children:e.category}),(0,r.jsx)("button",{onClick:()=>T(e._id),className:"text-yellow-500 hover:text-yellow-700",title:"Remove from favorites",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,r.jsx)("polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"})})})]}),(0,r.jsx)("h2",{className:"text-lg font-semibold mb-2 line-clamp-2",children:e.title}),(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-3 line-clamp-3",dangerouslySetInnerHTML:{__html:(0,g.A)(e.description,120)}}),(0,r.jsx)(n(),{href:"/blogs/".concat(e._id),className:"inline-block px-3 py-1 bg-black text-white text-sm rounded hover:bg-gray-800 transition",children:"Read Article"})]})]},e._id))}):(0,r.jsxs)("div",{className:"bg-white rounded-lg p-8 text-center",children:[(0,r.jsx)("div",{className:"text-5xl mb-4",children:"⭐"}),(0,r.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"No favorites yet"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Start adding blogs to your favorites by clicking the star icon on blog posts."}),(0,r.jsx)(n(),{href:"/",className:"inline-block px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition",children:"Browse Blogs"})]})}),C&&"profile"===x&&(0,r.jsxs)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Change Password"}),(0,r.jsxs)("form",{onSubmit:X,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"Current Password"}),(0,r.jsx)("input",{type:"password",name:"currentPassword",value:j.currentPassword,onChange:J,className:"w-full px-4 py-3 border border-gray-300 rounded-md",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"New Password"}),(0,r.jsx)("input",{type:"password",name:"newPassword",value:j.newPassword,onChange:J,className:"w-full px-4 py-3 border border-gray-300 rounded-md",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"Confirm New Password"}),(0,r.jsx)("input",{type:"password",name:"confirmPassword",value:j.confirmPassword,onChange:J,className:"w-full px-4 py-3 border border-gray-300 rounded-md",required:!0})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("button",{type:"submit",className:"px-6 py-3 bg-black text-white font-medium rounded-md hover:bg-gray-800 transition-colors",children:"Update Password"}),(0,r.jsx)("button",{type:"button",onClick:()=>k(!1),className:"px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 transition-colors",children:"Cancel"})]})]})]})]})})}),f&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-6 rounded-md shadow-lg max-w-sm w-full",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Confirm Logout"}),(0,r.jsx)("p",{className:"mb-6",children:"Are you sure you want to log out? You will need to log in again to access your account."}),(0,r.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,r.jsx)("button",{onClick:()=>{w(!1)},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"}),(0,r.jsx)("button",{onClick:()=>{localStorage.removeItem("authToken"),localStorage.removeItem("userRole"),localStorage.removeItem("userId"),localStorage.removeItem("userName"),localStorage.removeItem("userProfilePicture"),l.toast.success("Logged out successfully"),setTimeout(()=>{e.push("/")},300)},className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",children:"Logout"})]})]})}),I&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg max-w-2xl w-full",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Adjust Profile Picture"}),(0,r.jsx)("div",{className:"relative h-80 mb-4",children:(0,r.jsx)(A.ZP,{image:V,crop:L,zoom:_,aspect:1,cropShape:"round",onCropChange:G,onCropComplete:(e,t)=>{H(t)},onZoomChange:F})}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Zoom: ",_.toFixed(1),"x"]}),(0,r.jsx)("input",{type:"range",min:1,max:3,step:.1,value:_,onChange:e=>F(parseFloat(e.target.value)),className:"w-full"})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{type:"button",onClick:q,className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",children:"Apply"}),(0,r.jsx)("button",{type:"button",onClick:()=>{E(!1),Q(null)},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"})]})]})})]})}},8347:function(e,t,a){"use strict";a.d(t,{A:function(){return s},D:function(){return r}});let r=e=>{if(!e)return"";let t=e;return(t=(t=t.replace(/\{\{image:[^}]+\}\}/g,"")).replace(/\[\[[^\]]+\]\]/g,"")).replace(/\s+/g," ").trim()},s=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:120,a=r(e);return a.length>t?a.substring(0,t)+"...":a}}},function(e){e.O(0,[580,691,396,442,971,938,744],function(){return e(e.s=5930)}),_N_E=e.O()}]);
"use strict";(()=>{var e={};e.id=5932,e.ids=[5932],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},12781:e=>{e.exports=require("stream")},73837:e=>{e.exports=require("util")},89494:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>h,originalPathname:()=>v,patchFetch:()=>y,requestAsyncStorage:()=>g,routeModule:()=>d,serverHooks:()=>f,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>S});var o={};t.r(o),t.d(o,{POST:()=>p});var n=t(95419),s=t(69108),a=t(99678),i=t(78070),u=t(91887),l=t(96488),c=t(11497);async function p(e){try{let{email:r,password:t}=await e.json(),o=await l.Z.findOne({email:r});if(!o||o.password!==t)return i.Z.json({success:!1,message:"Invalid credentials"},{status:401});{let e=(0,c.V)({userId:o._id.toString(),role:o.role});return console.log("Generated token for user:",o._id.toString()),i.Z.json({success:!0,token:e,user:{id:o._id.toString(),email:o.email,role:o.role,name:o.name||"",profilePicture:o.profilePicture||"/default_profile.png"}})}}catch(e){return console.error("Login error:",e),i.Z.json({success:!1,message:"Server error"},{status:500})}}(0,u.n)();let d=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/auth/route",pathname:"/api/auth",filename:"route",bundlePath:"app/api/auth/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\auth\\route.js",nextConfigOutput:"",userland:o}),{requestAsyncStorage:g,staticGenerationAsyncStorage:m,serverHooks:f,headerHooks:h,staticGenerationBailout:S}=d,v="/api/auth/route";function y(){return(0,a.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:m})}},91887:(e,r,t)=>{t.d(r,{n:()=>s});var o=t(11185),n=t.n(o);let s=async()=>{try{if(n().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await n().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},96488:(e,r,t)=>{t.d(r,{Z:()=>a});var o=t(11185),n=t.n(o);let s=new(n()).Schema({email:{type:String,required:!0,unique:!0},password:{type:String,required:!0},role:{type:String,default:"user",enum:["user","admin"]},profilePicture:{type:String,default:"/default_profile.png"},name:{type:String,default:""},date:{type:Date,default:Date.now()}}),a=n().models.user||n().model("user",s)},11497:(e,r,t)=>{t.d(r,{V:()=>a,W:()=>i});var o=t(46082),n=t.n(o);let s=process.env.JWT_SECRET||"your-secret-key-here",a=e=>n().sign(e,s,{expiresIn:"7d"}),i=e=>{try{return n().verify(e,s)}catch(e){return console.error("Token verification error:",e.message),null}}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[1638,2993,185],()=>t(89494));module.exports=o})();
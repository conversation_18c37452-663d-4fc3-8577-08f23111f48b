(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[858],{4939:function(e,t,a){Promise.resolve().then(a.bind(a,8996))},8996:function(e,t,a){"use strict";a.r(t);var s=a(7437),n=a(2173),r=a(2265),o=a(7948);t.default=()=>{let[e,t]=(0,r.useState)("add"),[a,l]=(0,r.useState)({email:"",password:"",confirmPassword:"",role:"user"}),[i,c]=(0,r.useState)([]),[d,u]=(0,r.useState)([]),[m,p]=(0,r.useState)(!1),[f,g]=(0,r.useState)("all"),[y,h]=(0,r.useState)("");(0,r.useEffect)(()=>{"manage"===e&&v()},[e]),(0,r.useEffect)(()=>{b()},[i,f,y]);let v=async()=>{try{p(!0);let e=await n.Z.get("/api/users");e.data.success?c(e.data.users):o.toast.error("Failed to load users")}catch(e){console.error("Error fetching users:",e),o.toast.error("Failed to load users")}finally{p(!1)}},b=()=>{let e=[...i];if("all"!==f&&(e=e.filter(e=>e.role===f)),y.trim()){let t=y.toLowerCase();e=e.filter(e=>e.email.toLowerCase().includes(t))}u(e)},x=e=>{l({...a,[e.target.name]:e.target.value})},E=async e=>{if(e.preventDefault(),a.password!==a.confirmPassword){o.toast.error("Passwords do not match");return}try{p(!0);let e=await n.Z.post("/api/register",{email:a.email,password:a.password,role:a.role});e.data.success?(o.toast.success("User created successfully"),l({email:"",password:"",confirmPassword:"",role:"user"}),t("manage")):o.toast.error(e.data.message||"Failed to create user")}catch(e){var s,r;console.error("User creation error:",e),o.toast.error((null===(r=e.response)||void 0===r?void 0:null===(s=r.data)||void 0===s?void 0:s.message)||"Failed to create user")}finally{p(!1)}},N=async e=>{if(window.confirm("Are you sure you want to delete this user?"))try{p(!0);let t=await n.Z.delete("/api/users",{params:{id:e}});t.data.success?(o.toast.success("User deleted successfully"),t.data.users?c(t.data.users):await v()):o.toast.error(t.data.message||"Failed to delete user")}catch(e){var t,a;console.error("Error deleting user:",e),o.toast.error((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"Failed to delete user")}finally{p(!1)}};return(0,s.jsxs)("div",{className:"pt-5 px-5 sm:pt-12 sm:pl-16",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"User Management"}),(0,s.jsxs)("div",{className:"flex border-b border-gray-300 mb-6",children:[(0,s.jsx)("button",{className:"py-3 px-6 font-medium rounded-t-lg ".concat("add"===e?"bg-black text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),onClick:()=>t("add"),children:"Add New User"}),(0,s.jsx)("button",{className:"py-3 px-6 font-medium rounded-t-lg ml-2 ".concat("manage"===e?"bg-black text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),onClick:()=>t("manage"),children:"Manage Users"})]}),"add"===e&&(0,s.jsxs)("form",{onSubmit:E,className:"max-w-[500px] bg-white p-6 rounded-lg shadow-md",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Create New User Account"}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-gray-700 mb-2",children:"Email"}),(0,s.jsx)("input",{type:"email",name:"email",value:a.email,onChange:x,className:"w-full px-4 py-3 border border-gray-300 rounded-md",placeholder:"<EMAIL>",required:!0})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-gray-700 mb-2",children:"Password"}),(0,s.jsx)("input",{type:"password",name:"password",value:a.password,onChange:x,className:"w-full px-4 py-3 border border-gray-300 rounded-md",placeholder:"Enter password",required:!0})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-gray-700 mb-2",children:"Confirm Password"}),(0,s.jsx)("input",{type:"password",name:"confirmPassword",value:a.confirmPassword,onChange:x,className:"w-full px-4 py-3 border border-gray-300 rounded-md",placeholder:"Confirm password",required:!0})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-gray-700 mb-2",children:"User Role"}),(0,s.jsxs)("select",{name:"role",value:a.role,onChange:x,className:"w-full px-4 py-3 border border-gray-300 rounded-md",required:!0,children:[(0,s.jsx)("option",{value:"user",children:"User"}),(0,s.jsx)("option",{value:"admin",children:"Admin"})]})]}),(0,s.jsx)("button",{type:"submit",className:"w-full py-3 bg-black text-white rounded-md hover:bg-gray-800 transition-colors",disabled:m,children:m?"Creating...":"Create User"})]}),"manage"===e&&(0,s.jsxs)("div",{className:"max-w-[800px] bg-white p-6 rounded-lg shadow-md",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Manage User Accounts"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4 mb-6",children:[(0,s.jsxs)("div",{className:"flex-1 min-w-[200px]",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search by Email"}),(0,s.jsx)("input",{type:"text",value:y,onChange:e=>h(e.target.value),placeholder:"Search users...",className:"w-full px-3 py-2 border border-gray-300 rounded-md"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Filter by Role"}),(0,s.jsxs)("select",{value:f,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md",children:[(0,s.jsx)("option",{value:"all",children:"All Users"}),(0,s.jsx)("option",{value:"admin",children:"Admins Only"}),(0,s.jsx)("option",{value:"user",children:"Regular Users Only"})]})]})]}),m?(0,s.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-black"}),(0,s.jsx)("span",{className:"ml-2",children:"Loading users..."})]}):d.length>0?(0,s.jsx)("div",{className:"relative overflow-x-auto border border-gray-300 rounded-lg",children:(0,s.jsxs)("table",{className:"w-full text-sm text-left text-gray-500",children:[(0,s.jsx)("thead",{className:"text-xs text-gray-700 uppercase bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Email"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Role"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Actions"})]})}),(0,s.jsx)("tbody",{children:d.map(e=>(0,s.jsxs)("tr",{className:"bg-white border-b hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4",children:e.email}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs ".concat("admin"===e.role?"bg-blue-100 text-blue-800":"bg-gray-100"),children:e.role})}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("button",{onClick:()=>N(e._id),className:"text-white bg-red-600 hover:bg-red-700 px-3 py-1 rounded-md text-sm",disabled:m,children:"Delete"})})]},e._id))})]})}):(0,s.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,s.jsx)("p",{children:0===i.length?"No users found.":"No users match your filters."}),0===i.length&&(0,s.jsx)("button",{onClick:()=>t("add"),className:"mt-4 text-blue-600 hover:underline",children:"Add your first user"}),i.length>0&&(0,s.jsx)("button",{onClick:()=>{g("all"),h("")},className:"mt-4 text-blue-600 hover:underline",children:"Clear filters"})]}),i.length>0&&(0,s.jsx)("div",{className:"mt-4 text-sm text-gray-500",children:(0,s.jsxs)("p",{children:["Showing ",d.length," of ",i.length," users","all"!==f&&" (filtered by ".concat(f," role)"),y&&" (filtered by search)"]})})]})]})}},7948:function(e,t,a){"use strict";a.r(t),a.d(t,{Bounce:function(){return M},Flip:function(){return B},Icons:function(){return A},Slide:function(){return O},ToastContainer:function(){return z},Zoom:function(){return R},collapseToast:function(){return d},cssTransition:function(){return u},toast:function(){return k},useToast:function(){return N},useToastContainer:function(){return E}});var s=a(2265),n=function(){for(var e,t,a=0,s="",n=arguments.length;a<n;a++)(e=arguments[a])&&(t=function e(t){var a,s,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t){if(Array.isArray(t)){var r=t.length;for(a=0;a<r;a++)t[a]&&(s=e(t[a]))&&(n&&(n+=" "),n+=s)}else for(s in t)t[s]&&(n&&(n+=" "),n+=s)}return n}(e))&&(s&&(s+=" "),s+=t);return s};let r=e=>"number"==typeof e&&!isNaN(e),o=e=>"string"==typeof e,l=e=>"function"==typeof e,i=e=>o(e)||l(e)?e:null,c=e=>(0,s.isValidElement)(e)||o(e)||l(e)||r(e);function d(e,t,a){void 0===a&&(a=300);let{scrollHeight:s,style:n}=e;requestAnimationFrame(()=>{n.minHeight="initial",n.height=s+"px",n.transition=`all ${a}ms`,requestAnimationFrame(()=>{n.height="0",n.padding="0",n.margin="0",setTimeout(t,a)})})}function u(e){let{enter:t,exit:a,appendPosition:n=!1,collapse:r=!0,collapseDuration:o=300}=e;return function(e){let{children:l,position:i,preventExitTransition:c,done:u,nodeRef:m,isIn:p,playToast:f}=e,g=n?`${t}--${i}`:t,y=n?`${a}--${i}`:a,h=(0,s.useRef)(0);return(0,s.useLayoutEffect)(()=>{let e=m.current,t=g.split(" "),a=s=>{s.target===m.current&&(f(),e.removeEventListener("animationend",a),e.removeEventListener("animationcancel",a),0===h.current&&"animationcancel"!==s.type&&e.classList.remove(...t))};e.classList.add(...t),e.addEventListener("animationend",a),e.addEventListener("animationcancel",a)},[]),(0,s.useEffect)(()=>{let e=m.current,t=()=>{e.removeEventListener("animationend",t),r?d(e,u,o):u()};p||(c?t():(h.current=1,e.className+=` ${y}`,e.addEventListener("animationend",t)))},[p]),s.createElement(s.Fragment,null,l)}}function m(e,t){return null!=e?{content:e.content,containerId:e.props.containerId,id:e.props.toastId,theme:e.props.theme,type:e.props.type,data:e.props.data||{},isLoading:e.props.isLoading,icon:e.props.icon,status:t}:{}}let p=new Map,f=[],g=new Set,y=e=>g.forEach(t=>t(e)),h=()=>p.size>0;function v(e,t){var a;if(t)return!(null==(a=p.get(t))||!a.isToastActive(e));let s=!1;return p.forEach(t=>{t.isToastActive(e)&&(s=!0)}),s}function b(e,t){c(e)&&(h()||f.push({content:e,options:t}),p.forEach(a=>{a.buildToast(e,t)}))}function x(e,t){p.forEach(a=>{null!=t&&null!=t&&t.containerId?(null==t?void 0:t.containerId)===a.id&&a.toggle(e,null==t?void 0:t.id):a.toggle(e,null==t?void 0:t.id)})}function E(e){let{subscribe:t,getSnapshot:a,setProps:n}=(0,s.useRef)(function(e){let t=e.containerId||1;return{subscribe(a){let n=function(e,t,a){let n=1,d=0,u=[],p=[],f=[],g=t,y=new Map,h=new Set,v=()=>{f=Array.from(y.values()),h.forEach(e=>e())},b=e=>{p=null==e?[]:p.filter(t=>t!==e),v()},x=e=>{let{toastId:t,onOpen:n,updateId:r,children:o}=e.props,i=null==r;e.staleId&&y.delete(e.staleId),y.set(t,e),p=[...p,e.props.toastId].filter(t=>t!==e.staleId),v(),a(m(e,i?"added":"updated")),i&&l(n)&&n((0,s.isValidElement)(o)&&o.props)};return{id:e,props:g,observe:e=>(h.add(e),()=>h.delete(e)),toggle:(e,t)=>{y.forEach(a=>{null!=t&&t!==a.props.toastId||l(a.toggle)&&a.toggle(e)})},removeToast:b,toasts:y,clearQueue:()=>{d-=u.length,u=[]},buildToast:(t,p)=>{var f,h;if((t=>{let{containerId:a,toastId:s,updateId:n}=t,r=y.has(s)&&null==n;return(a?a!==e:1!==e)||r})(p))return;let{toastId:E,updateId:N,data:w,staleId:T,delay:C}=p,_=()=>{b(E)},j=null==N;j&&d++;let I={...g,style:g.toastStyle,key:n++,...Object.fromEntries(Object.entries(p).filter(e=>{let[t,a]=e;return null!=a})),toastId:E,updateId:N,data:w,closeToast:_,isIn:!1,className:i(p.className||g.toastClassName),bodyClassName:i(p.bodyClassName||g.bodyClassName),progressClassName:i(p.progressClassName||g.progressClassName),autoClose:!p.isLoading&&(f=p.autoClose,h=g.autoClose,!1===f||r(f)&&f>0?f:h),deleteToast(){let e=y.get(E),{onClose:t,children:n}=e.props;l(t)&&t((0,s.isValidElement)(n)&&n.props),a(m(e,"removed")),y.delete(E),--d<0&&(d=0),u.length>0?x(u.shift()):v()}};I.closeButton=g.closeButton,!1===p.closeButton||c(p.closeButton)?I.closeButton=p.closeButton:!0===p.closeButton&&(I.closeButton=!c(g.closeButton)||g.closeButton);let k=t;(0,s.isValidElement)(t)&&!o(t.type)?k=(0,s.cloneElement)(t,{closeToast:_,toastProps:I,data:w}):l(t)&&(k=t({closeToast:_,toastProps:I,data:w}));let L={content:k,props:I,staleId:T};g.limit&&g.limit>0&&d>g.limit&&j?u.push(L):r(C)?setTimeout(()=>{x(L)},C):x(L)},setProps(e){g=e},setToggle:(e,t)=>{y.get(e).toggle=t},isToastActive:e=>p.some(t=>t===e),getSnapshot:()=>g.newestOnTop?f.reverse():f}}(t,e,y);p.set(t,n);let d=n.observe(a);return f.forEach(e=>b(e.content,e.options)),f=[],()=>{d(),p.delete(t)}},setProps(e){var a;null==(a=p.get(t))||a.setProps(e)},getSnapshot(){var e;return null==(e=p.get(t))?void 0:e.getSnapshot()}}}(e)).current;n(e);let d=(0,s.useSyncExternalStore)(t,a,a);return{getToastToRender:function(e){if(!d)return[];let t=new Map;return d.forEach(e=>{let{position:a}=e.props;t.has(a)||t.set(a,[]),t.get(a).push(e)}),Array.from(t,t=>e(t[0],t[1]))},isToastActive:v,count:null==d?void 0:d.length}}function N(e){var t,a;let[n,r]=(0,s.useState)(!1),[o,l]=(0,s.useState)(!1),i=(0,s.useRef)(null),c=(0,s.useRef)({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:d,pauseOnHover:u,closeToast:m,onClick:f,closeOnClick:g}=e;function y(){r(!0)}function h(){r(!1)}function v(t){let a=i.current;c.canDrag&&a&&(c.didMove=!0,n&&h(),c.delta="x"===e.draggableDirection?t.clientX-c.start:t.clientY-c.start,c.start!==t.clientX&&(c.canCloseOnClick=!1),a.style.transform=`translate3d(${"x"===e.draggableDirection?`${c.delta}px, var(--y)`:`0, calc(${c.delta}px + var(--y))`},0)`,a.style.opacity=""+(1-Math.abs(c.delta/c.removalDistance)))}function b(){document.removeEventListener("pointermove",v),document.removeEventListener("pointerup",b);let t=i.current;if(c.canDrag&&c.didMove&&t){if(c.canDrag=!1,Math.abs(c.delta)>c.removalDistance)return l(!0),e.closeToast(),void e.collapseAll();t.style.transition="transform 0.2s, opacity 0.2s",t.style.removeProperty("transform"),t.style.removeProperty("opacity")}}null==(a=p.get((t={id:e.toastId,containerId:e.containerId,fn:r}).containerId||1))||a.setToggle(t.id,t.fn),(0,s.useEffect)(()=>{if(e.pauseOnFocusLoss)return document.hasFocus()||h(),window.addEventListener("focus",y),window.addEventListener("blur",h),()=>{window.removeEventListener("focus",y),window.removeEventListener("blur",h)}},[e.pauseOnFocusLoss]);let x={onPointerDown:function(t){if(!0===e.draggable||e.draggable===t.pointerType){c.didMove=!1,document.addEventListener("pointermove",v),document.addEventListener("pointerup",b);let a=i.current;c.canCloseOnClick=!0,c.canDrag=!0,a.style.transition="none","x"===e.draggableDirection?(c.start=t.clientX,c.removalDistance=a.offsetWidth*(e.draggablePercent/100)):(c.start=t.clientY,c.removalDistance=a.offsetHeight*(80===e.draggablePercent?1.5*e.draggablePercent:e.draggablePercent)/100)}},onPointerUp:function(t){let{top:a,bottom:s,left:n,right:r}=i.current.getBoundingClientRect();"touchend"!==t.nativeEvent.type&&e.pauseOnHover&&t.clientX>=n&&t.clientX<=r&&t.clientY>=a&&t.clientY<=s?h():y()}};return d&&u&&(x.onMouseEnter=h,e.stacked||(x.onMouseLeave=y)),g&&(x.onClick=e=>{f&&f(e),c.canCloseOnClick&&m()}),{playToast:y,pauseToast:h,isRunning:n,preventExitTransition:o,toastRef:i,eventHandlers:x}}function w(e){let{delay:t,isRunning:a,closeToast:r,type:o="default",hide:i,className:c,style:d,controlledProgress:u,progress:m,rtl:p,isIn:f,theme:g}=e,y=i||u&&0===m,h={...d,animationDuration:`${t}ms`,animationPlayState:a?"running":"paused"};u&&(h.transform=`scaleX(${m})`);let v=n("Toastify__progress-bar",u?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${g}`,`Toastify__progress-bar--${o}`,{"Toastify__progress-bar--rtl":p}),b=l(c)?c({rtl:p,type:o,defaultClassName:v}):n(v,c);return s.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":y},s.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${g} Toastify__progress-bar--${o}`}),s.createElement("div",{role:"progressbar","aria-hidden":y?"true":"false","aria-label":"notification timer",className:b,style:h,[u&&m>=1?"onTransitionEnd":"onAnimationEnd"]:u&&m<1?null:()=>{f&&r()}}))}let T=1,C=()=>""+T++;function _(e,t){return b(e,t),t.toastId}function j(e,t){return{...t,type:t&&t.type||e,toastId:t&&(o(t.toastId)||r(t.toastId))?t.toastId:C()}}function I(e){return(t,a)=>_(t,j(e,a))}function k(e,t){return _(e,j("default",t))}k.loading=(e,t)=>_(e,j("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...t})),k.promise=function(e,t,a){let s,{pending:n,error:r,success:i}=t;n&&(s=o(n)?k.loading(n,a):k.loading(n.render,{...a,...n}));let c={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},d=(e,t,n)=>{if(null==t)return void k.dismiss(s);let r={type:e,...c,...a,data:n},l=o(t)?{render:t}:t;return s?k.update(s,{...r,...l}):k(l.render,{...r,...l}),n},u=l(e)?e():e;return u.then(e=>d("success",i,e)).catch(e=>d("error",r,e)),u},k.success=I("success"),k.info=I("info"),k.error=I("error"),k.warning=I("warning"),k.warn=k.warning,k.dark=(e,t)=>_(e,j("default",{theme:"dark",...t})),k.dismiss=function(e){var t,a;h()?null==e||o(t=e)||r(t)?p.forEach(t=>{t.removeToast(e)}):e&&("containerId"in e||"id"in e)&&((null==(a=p.get(e.containerId))?void 0:a.removeToast(e.id))||p.forEach(t=>{t.removeToast(e.id)})):f=f.filter(t=>null!=e&&t.options.toastId!==e)},k.clearWaitingQueue=function(e){void 0===e&&(e={}),p.forEach(t=>{!t.props.limit||e.containerId&&t.id!==e.containerId||t.clearQueue()})},k.isActive=v,k.update=function(e,t){void 0===t&&(t={});let a=((e,t)=>{var a;let{containerId:s}=t;return null==(a=p.get(s||1))?void 0:a.toasts.get(e)})(e,t);if(a){let{props:s,content:n}=a,r={delay:100,...s,...t,toastId:t.toastId||e,updateId:C()};r.toastId!==e&&(r.staleId=e);let o=r.render||n;delete r.render,_(o,r)}},k.done=e=>{k.update(e,{progress:1})},k.onChange=function(e){return g.add(e),()=>{g.delete(e)}},k.play=e=>x(!0,e),k.pause=e=>x(!1,e);let L="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,P=e=>{let{theme:t,type:a,isLoading:n,...r}=e;return s.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:"colored"===t?"currentColor":`var(--toastify-icon-color-${a})`,...r})},A={info:function(e){return s.createElement(P,{...e},s.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))},warning:function(e){return s.createElement(P,{...e},s.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))},success:function(e){return s.createElement(P,{...e},s.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))},error:function(e){return s.createElement(P,{...e},s.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))},spinner:function(){return s.createElement("div",{className:"Toastify__spinner"})}},S=e=>{let{isRunning:t,preventExitTransition:a,toastRef:r,eventHandlers:o,playToast:i}=N(e),{closeButton:c,children:d,autoClose:u,onClick:m,type:p,hideProgressBar:f,closeToast:g,transition:y,position:h,className:v,style:b,bodyClassName:x,bodyStyle:E,progressClassName:T,progressStyle:C,updateId:_,role:j,progress:I,rtl:k,toastId:L,deleteToast:P,isIn:S,isLoading:$,closeOnClick:M,theme:O}=e,R=n("Toastify__toast",`Toastify__toast-theme--${O}`,`Toastify__toast--${p}`,{"Toastify__toast--rtl":k},{"Toastify__toast--close-on-click":M}),B=l(v)?v({rtl:k,position:h,type:p,defaultClassName:R}):n(R,v),D=function(e){let{theme:t,type:a,isLoading:n,icon:r}=e,o=null,i={theme:t,type:a,isLoading:n};return!1===r||(l(r)?o=r(i):(0,s.isValidElement)(r)?o=(0,s.cloneElement)(r,i):n?o=A.spinner():a in A&&(o=A[a](i))),o}(e),z=!!I||!u,F={closeToast:g,type:p,theme:O},U=null;return!1===c||(U=l(c)?c(F):(0,s.isValidElement)(c)?(0,s.cloneElement)(c,F):function(e){let{closeToast:t,theme:a,ariaLabel:n="close"}=e;return s.createElement("button",{className:`Toastify__close-button Toastify__close-button--${a}`,type:"button",onClick:e=>{e.stopPropagation(),t(e)},"aria-label":n},s.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},s.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}(F)),s.createElement(y,{isIn:S,done:P,position:h,preventExitTransition:a,nodeRef:r,playToast:i},s.createElement("div",{id:L,onClick:m,"data-in":S,className:B,...o,style:b,ref:r},s.createElement("div",{...S&&{role:j},className:l(x)?x({type:p}):n("Toastify__toast-body",x),style:E},null!=D&&s.createElement("div",{className:n("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!$})},D),s.createElement("div",null,d)),U,s.createElement(w,{..._&&!z?{key:`pb-${_}`}:{},rtl:k,theme:O,delay:u,isRunning:t,isIn:S,closeToast:g,hide:f,type:p,style:C,className:T,controlledProgress:z,progress:I||0})))},$=function(e,t){return void 0===t&&(t=!1),{enter:`Toastify--animate Toastify__${e}-enter`,exit:`Toastify--animate Toastify__${e}-exit`,appendPosition:t}},M=u($("bounce",!0)),O=u($("slide",!0)),R=u($("zoom")),B=u($("flip")),D={position:"top-right",transition:M,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light"};function z(e){let t={...D,...e},a=e.stacked,[r,o]=(0,s.useState)(!0),c=(0,s.useRef)(null),{getToastToRender:d,isToastActive:u,count:m}=E(t),{className:p,style:f,rtl:g,containerId:y}=t;function h(){a&&(o(!0),k.play())}return L(()=>{if(a){var e;let a=c.current.querySelectorAll('[data-in="true"]'),s=null==(e=t.position)?void 0:e.includes("top"),n=0,o=0;Array.from(a).reverse().forEach((e,t)=>{e.classList.add("Toastify__toast--stacked"),t>0&&(e.dataset.collapsed=`${r}`),e.dataset.pos||(e.dataset.pos=s?"top":"bot");let a=n*(r?.2:1)+(r?0:12*t);e.style.setProperty("--y",`${s?a:-1*a}px`),e.style.setProperty("--g","12"),e.style.setProperty("--s",""+(1-(r?o:0))),n+=e.offsetHeight,o+=.025})}},[r,m,a]),s.createElement("div",{ref:c,className:"Toastify",id:y,onMouseEnter:()=>{a&&(o(!1),k.pause())},onMouseLeave:h},d((e,t)=>{let r=t.length?{...f}:{...f,pointerEvents:"none"};return s.createElement("div",{className:function(e){let t=n("Toastify__toast-container",`Toastify__toast-container--${e}`,{"Toastify__toast-container--rtl":g});return l(p)?p({position:e,rtl:g,defaultClassName:t}):n(t,i(p))}(e),style:r,key:`container-${e}`},t.map(e=>{let{content:t,props:n}=e;return s.createElement(S,{...n,stacked:a,collapseAll:h,isIn:u(n.toastId,n.containerId),style:n.style,key:`toast-${n.key}`},t)}))}))}}},function(e){e.O(0,[580,971,938,744],function(){return e(e.s=4939)}),_N_E=e.O()}]);
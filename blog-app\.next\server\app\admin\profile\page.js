(()=>{var e={};e.id=9374,e.ids=[9374],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},46832:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(50482),a=t(69108),o=t(62563),l=t.n(o),i=t(68300),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);t.d(r,n);let d=["",{children:["admin",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,40923)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\profile\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,32143)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,39459)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\profile\\page.jsx"],u="/admin/profile/page",p={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/profile/page",pathname:"/admin/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},61081:(e,r,t)=>{Promise.resolve().then(t.bind(t,1723))},1723:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(95344),a=t(53608),o=t(41223),l=t.n(o),i=t(22254),n=t(3729),d=t(69697),c=t(79338);let u=()=>{let e=(0,i.useRouter)(),[r,t]=(0,n.useState)(!0),[o,u]=(0,n.useState)({id:"",email:"",name:"",profilePicture:"/default_profile.png"}),[p,m]=(0,n.useState)(null),[x,g]=(0,n.useState)(null),[h,b]=(0,n.useState)(!1),[f,w]=(0,n.useState)(!1),[y,j]=(0,n.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[P,v]=(0,n.useState)(!1),[N,C]=(0,n.useState)(null),[S,k]=(0,n.useState)({x:0,y:0}),[q,_]=(0,n.useState)(1),[D,I]=(0,n.useState)(null);(0,n.useEffect)(()=>{(async()=>{try{let r=localStorage.getItem("userId");if(!r){e.push("/");return}let t=await a.Z.get("/api/profile",{params:{userId:r}});t.data.success?u(t.data.user):d.toast.error("Failed to load profile data")}catch(e){console.error("Profile fetch error:",e),d.toast.error("Failed to load profile data")}finally{t(!1)}})()},[e]);let F=async(e,r)=>{let t=new(l());t.src=e;let s=document.createElement("canvas"),a=s.getContext("2d");return s.width=r.width,s.height=r.height,a.drawImage(t,r.x,r.y,r.width,r.height,0,0,r.width,r.height),new Promise(e=>{s.toBlob(r=>{e(r)},"image/jpeg")})},E=async()=>{try{if(!D)return;let e=await F(N,D),r=URL.createObjectURL(e);g(r);let t=new File([e],"cropped_profile.jpg",{type:"image/jpeg"});m(t),v(!1)}catch(e){console.error("Error applying crop:",e),d.toast.error("Failed to crop image")}},U=async e=>{e.preventDefault();try{let e=new FormData;e.append("userId",o.id),e.append("name",o.name),p&&e.append("profilePicture",p);let r=await a.Z.put("/api/profile",e);if(r.data.success){localStorage.setItem("userName",r.data.user.name),localStorage.setItem("userProfilePicture",r.data.user.profilePicture);let e=new CustomEvent("profileUpdate",{detail:{name:r.data.user.name,profilePicture:r.data.user.profilePicture}});window.dispatchEvent(e),d.toast.success("Profile updated successfully"),u({...o,name:r.data.user.name,profilePicture:r.data.user.profilePicture}),m(null),g(null)}else d.toast.error(r.data.message||"Failed to update profile")}catch(e){console.error("Profile update error:",e),d.toast.error("Failed to update profile")}},A=e=>{j({...y,[e.target.name]:e.target.value})},M=async e=>{if(e.preventDefault(),y.newPassword!==y.confirmPassword){d.toast.error("New passwords do not match");return}try{let e=await a.Z.put("/api/password",{userId:o.id,currentPassword:y.currentPassword,newPassword:y.newPassword});e.data.success?(d.toast.success("Password updated successfully"),j({currentPassword:"",newPassword:"",confirmPassword:""}),w(!1)):d.toast.error(e.data.message||"Failed to update password")}catch(e){console.error("Password update error:",e),d.toast.error(e.response?.data?.message||"Failed to update password")}};return r?s.jsx("div",{className:"pt-5 px-5 sm:pt-12 sm:pl-16",children:s.jsx("p",{children:"Loading profile data..."})}):(0,s.jsxs)("div",{className:"pt-5 px-5 sm:pt-12 sm:pl-16",children:[s.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Admin Profile"}),(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,s.jsxs)("form",{onSubmit:U,className:"max-w-2xl",children:[(0,s.jsxs)("div",{className:"mb-6",children:[s.jsx("label",{className:"block text-gray-700 mb-2",children:"Profile Picture"}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[s.jsx(l(),{src:x||o.profilePicture,width:100,height:100,alt:"Profile",className:"rounded-full object-cover w-24 h-24 border-2 border-gray-300"}),(0,s.jsxs)("label",{className:"cursor-pointer bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-md border border-gray-300",children:["Change Picture",s.jsx("input",{type:"file",accept:"image/*",onChange:e=>{let r=e.target.files[0];if(r){let e=new FileReader;e.onloadend=()=>{C(e.result),v(!0)},e.readAsDataURL(r)}},className:"hidden"})]})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-gray-700 mb-2",children:"Email"}),s.jsx("input",{type:"email",value:o.email,disabled:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-md bg-gray-50"})]}),(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-gray-700 mb-2",children:"Display Name"}),s.jsx("input",{type:"text",value:o.name,onChange:e=>{u({...o,name:e.target.value})},className:"w-full px-4 py-3 border border-gray-300 rounded-md",placeholder:"Enter your name"})]}),(0,s.jsxs)("div",{className:"flex gap-4",children:[s.jsx("button",{type:"submit",className:"mt-4 w-40 h-12 bg-black text-white",children:"Save Changes"}),s.jsx("button",{type:"button",onClick:()=>e.push("/admin"),className:"mt-4 w-40 h-12 border border-black",children:"Cancel"}),s.jsx("button",{type:"button",onClick:()=>{b(!0)},className:"mt-4 w-40 h-12 bg-red-600 text-white",children:"Logout"})]})]}),(0,s.jsxs)("div",{className:"mt-12 border-t pt-6",children:[s.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Change Password"}),s.jsx("button",{type:"button",onClick:()=>w(!f),className:"mb-4 px-4 py-2 border border-gray-300 rounded-md",children:f?"Cancel":"Change Password"}),f&&(0,s.jsxs)("form",{onSubmit:M,className:"max-w-2xl",children:[(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-gray-700 mb-2",children:"Current Password"}),s.jsx("input",{type:"password",name:"currentPassword",value:y.currentPassword,onChange:A,className:"w-full px-4 py-3 border border-gray-300 rounded-md",required:!0})]}),(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-gray-700 mb-2",children:"New Password"}),s.jsx("input",{type:"password",name:"newPassword",value:y.newPassword,onChange:A,className:"w-full px-4 py-3 border border-gray-300 rounded-md",required:!0})]}),(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-gray-700 mb-2",children:"Confirm New Password"}),s.jsx("input",{type:"password",name:"confirmPassword",value:y.confirmPassword,onChange:A,className:"w-full px-4 py-3 border border-gray-300 rounded-md",required:!0})]}),s.jsx("button",{type:"submit",className:"mt-4 w-40 h-12 bg-black text-white",children:"Update Password"})]})]})]}),h&&s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white p-6 rounded-md shadow-lg max-w-sm w-full",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Confirm Logout"}),s.jsx("p",{className:"mb-6",children:"Are you sure you want to log out? You will need to log in again to access the admin panel."}),(0,s.jsxs)("div",{className:"flex justify-end gap-3",children:[s.jsx("button",{onClick:()=>{b(!1)},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"}),s.jsx("button",{onClick:()=>{localStorage.removeItem("authToken"),localStorage.removeItem("userRole"),localStorage.removeItem("userId"),localStorage.removeItem("userName"),localStorage.removeItem("userProfilePicture"),d.toast.success("Logged out successfully"),setTimeout(()=>{e.push("/")},300)},className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",children:"Logout"})]})]})}),P&&s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg max-w-2xl w-full",children:[s.jsx("h3",{className:"text-xl font-semibold mb-4",children:"Adjust Profile Picture"}),s.jsx("div",{className:"relative h-80 mb-4",children:s.jsx(c.ZP,{image:N,crop:S,zoom:q,aspect:1,cropShape:"round",onCropChange:k,onCropComplete:(e,r)=>{I(r)},onZoomChange:_})}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Zoom: ",q.toFixed(1),"x"]}),s.jsx("input",{type:"range",min:1,max:3,step:.1,value:q,onChange:e=>_(parseFloat(e.target.value)),className:"w-full"})]}),(0,s.jsxs)("div",{className:"flex gap-3",children:[s.jsx("button",{type:"button",onClick:E,className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",children:"Apply"}),s.jsx("button",{type:"button",onClick:()=>{v(!1),C(null)},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"})]})]})})]})}},40923:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>o,__esModule:()=>a,default:()=>l});let s=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\admin\profile\page.jsx`),{__esModule:a,$$typeof:o}=s,l=s.default}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,3998,337,8468,9338,5757,7388],()=>t(46832));module.exports=s})();
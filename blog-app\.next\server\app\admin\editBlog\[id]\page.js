(()=>{var e={};e.id=5894,e.ids=[5894],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},89001:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=s(50482),r=s(69108),i=s(62563),o=s.n(i),l=s(68300),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);s.d(t,n);let d=["",{children:["admin",{children:["editBlog",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,56524)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\editBlog\\[id]\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,32143)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,39459)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\editBlog\\[id]\\page.jsx"],u="/admin/editBlog/[id]/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/editBlog/[id]/page",pathname:"/admin/editBlog/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},41852:(e,t,s)=>{Promise.resolve().then(s.bind(s,29551))},29551:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var a=s(95344),r=s(6880),i=s(53608),o=s(41223),l=s.n(o),n=s(22254),d=s(3729),c=s(69697);let u=()=>{let e=(0,n.useRouter)(),t=(0,n.useParams)(),[s,o]=(0,d.useState)(!0),[u,m]=(0,d.useState)(null),[p,g]=(0,d.useState)(""),[h,x]=(0,d.useState)([]),[b,j]=(0,d.useState)([]),[w,y]=(0,d.useState)(0),[f,v]=(0,d.useState)({totalViews:0,uniqueVisitors:0}),[N,k]=(0,d.useState)({title:"",description:"",category:"",author:"",authorId:"",authorImg:"/author_img.png"}),[C,S]=(0,d.useState)(!1),[L,_]=(0,d.useState)([]),[q,B]=(0,d.useState)(""),[I,E]=(0,d.useState)(!1),[P,M]=(0,d.useState)([]),[D,F]=(0,d.useState)(""),[U,Z]=(0,d.useState)(!1),[A,T]=(0,d.useState)(null),$=async()=>{try{let e=await i.Z.get("/api/categories");e.data.success&&e.data.categories.length>0?x(e.data.categories):(console.error("No categories found"),c.toast.error("Failed to load categories"))}catch(e){console.error("Error fetching categories:",e),c.toast.error("Failed to load categories")}},W=async()=>{try{let e=await i.Z.get("/api/authors");e.data.success&&e.data.authors.length>0?j(e.data.authors):(console.error("No authors found"),c.toast.error("Failed to load authors"))}catch(e){console.error("Error fetching authors:",e),c.toast.error("Failed to load authors")}},z=async()=>{try{let e=await i.Z.get(`/api/blog/likes?id=${t.id}`);e.data.success&&y(e.data.count)}catch(e){console.error("Error fetching likes count:",e)}},G=async()=>{try{let e=await i.Z.get(`/api/blog/analytics?id=${t.id}`);e.data.success&&v(e.data.analytics)}catch(e){console.error("Error fetching blog analytics:",e)}},R=async()=>{try{let e=await i.Z.get("/api/blog");_(e.data.blogs||[])}catch(e){console.error("Error fetching blogs:",e)}},Y=(e,t)=>{let s=`[[${e}|${t}]]`,a=document.getElementById("blog-description"),r=a.selectionStart,i=N.description.substring(0,r),o=N.description.substring(r);k({...N,description:i+s+o}),S(!1),setTimeout(()=>{a.focus(),a.setSelectionRange(r+s.length,r+s.length)},100)},V=async()=>{try{let e=await i.Z.get("/api/images",{params:{blogId:t.id,limit:50}});e.data.success&&M(e.data.images)}catch(e){console.error("Error fetching images:",e),c.toast.error("Failed to fetch images")}},O=async()=>{if(!A){c.toast.error("Please select an image file");return}Z(!0);try{let e=new FormData;e.append("image",A),e.append("blogId",t.id);let s=await i.Z.post("/api/upload/image",e);s.data.success?(c.toast.success("Image uploaded successfully"),T(null),await V()):c.toast.error(s.data.message||"Failed to upload image")}catch(e){console.error("Error uploading image:",e),c.toast.error("Failed to upload image")}finally{Z(!1)}},H=async(e,t)=>{if(window.confirm("Are you sure you want to delete this image? This action cannot be undone."))try{let t=await i.Z.delete(`/api/images/${e}`);t.data.success?(c.toast.success("Image deleted successfully"),await V()):c.toast.error(t.data.message||"Failed to delete image")}catch(e){console.error("Error deleting image:",e),c.toast.error("Failed to delete image")}},X=(e,t)=>{let s=`{{image:${e}|${t}}}`,a=document.getElementById("blog-description"),r=a.selectionStart,i=N.description.substring(0,r),o=N.description.substring(r);k({...N,description:i+s+o}),E(!1),setTimeout(()=>{a.focus(),a.setSelectionRange(r+s.length,r+s.length)},100)};(0,d.useEffect)(()=>{(async()=>{try{let e=(await i.Z.get("/api/blog",{params:{id:t.id}})).data;k({title:e.title||"",description:e.description||"",category:e.category||"Startup",author:e.author||"",authorId:e.authorId||"",authorImg:e.authorImg||"/author_img.png"}),g(e.image||""),await $(),await W(),await z(),await G(),o(!1)}catch(t){console.error("Error fetching blog:",t),c.toast.error("Failed to load blog data"),e.push("/admin/blogList")}})()},[t.id,e]);let J=e=>{let t=e.target.name,s=e.target.value;if("authorId"===t){let e=b.find(e=>e._id===s);e&&k(t=>({...t,author:e.name,authorId:e._id,authorImg:e.image||"/author_img.png"}))}else k(e=>({...e,[t]:s}))},K=async s=>{s.preventDefault();try{let s=new FormData;s.append("id",t.id),s.append("title",N.title),s.append("description",N.description),s.append("category",N.category),s.append("author",N.author),s.append("authorId",N.authorId),s.append("authorImg",N.authorImg),u&&s.append("image",u);let a=await i.Z.put("/api/blog",s);a.data.success?(c.toast.success(a.data.msg||"Blog updated successfully"),e.push("/admin/blogList")):c.toast.error(a.data.msg||"Error updating blog")}catch(e){console.error("Error updating blog:",e),c.toast.error("Failed to update blog")}},Q=async()=>{if(window.confirm("Are you sure you want to delete this blog? This action cannot be undone."))try{let s=await i.Z.delete("/api/blog",{params:{id:t.id}});s.data.success?(c.toast.success(s.data.msg||"Blog deleted successfully"),e.push("/admin/blogList")):c.toast.error(s.data.msg||"Error deleting blog")}catch(e){console.error("Error deleting blog:",e),c.toast.error("Failed to delete blog")}};return s?a.jsx("div",{className:"pt-5 px-5 sm:pt-12 sm:pl-16",children:a.jsx("p",{children:"Loading blog data..."})}):a.jsx(a.Fragment,{children:(0,a.jsxs)("form",{onSubmit:K,className:"pt-5 px-5 sm:pt-12 sm:pl-16",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[a.jsx("h1",{className:"text-2xl font-bold",children:"Edit Blog"}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 bg-gray-100 px-3 py-2 rounded-md",children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",className:"text-red-500",children:a.jsx("path",{d:"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"})}),a.jsx("span",{className:"font-medium",children:w}),a.jsx("span",{className:"text-sm text-gray-500",children:"likes"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 bg-gray-100 px-3 py-2 rounded-md",children:[(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"text-blue-500",children:[a.jsx("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}),a.jsx("circle",{cx:"12",cy:"12",r:"3"})]}),a.jsx("span",{className:"font-medium",children:f.totalViews}),a.jsx("span",{className:"text-sm text-gray-500",children:"views"})]})]})]}),a.jsx("p",{className:"text-xl",children:"Current thumbnail"}),a.jsx("div",{className:"mt-4 mb-4",children:a.jsx(l(),{src:p,width:200,height:120,alt:"Current thumbnail",className:"border border-gray-300"})}),a.jsx("p",{className:"text-xl",children:"Upload new thumbnail (optional)"}),a.jsx("label",{htmlFor:"image",children:a.jsx(l(),{className:"mt-4 cursor-pointer",src:u?URL.createObjectURL(u):r.L.upload_area,width:140,height:70,alt:""})}),a.jsx("input",{onChange:e=>m(e.target.files[0]),type:"file",id:"image",hidden:!0}),a.jsx("p",{className:"text-xl mt-4",children:"Blog title"}),a.jsx("input",{name:"title",onChange:J,value:N.title,className:"w-full sm:w-[500px] mt-4 px-4 py-3 border",type:"text",placeholder:"Type here",required:!0}),(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("p",{className:"text-xl",children:"Blog Description"}),a.jsx("div",{className:"flex items-start mt-4",children:a.jsx("textarea",{id:"blog-description",name:"description",onChange:J,value:N.description,className:"w-full sm:w-[500px] px-4 py-3 border",placeholder:"Write content here",rows:6,required:!0})}),(0,a.jsxs)("div",{className:"mt-2 flex items-center flex-wrap gap-4",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>{R(),S(!0)},className:"text-sm flex items-center text-blue-600 hover:text-blue-800",children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101"})}),"Mention another blog"]}),(0,a.jsxs)("button",{type:"button",onClick:()=>{V(),E(!0)},className:"text-sm flex items-center text-green-600 hover:text-green-800",children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),"Insert image"]}),a.jsx("div",{className:"text-xs text-gray-500",children:(0,a.jsxs)("span",{children:["Formats: [[blogId|blogTitle]] | ","{{image:url|filename}}"]})})]}),C&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[a.jsx("h3",{className:"text-lg font-semibold",children:"Select a blog to mention"}),a.jsx("button",{onClick:()=>S(!1),className:"text-gray-500 hover:text-gray-700",children:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),a.jsx("input",{type:"text",placeholder:"Search blogs...",className:"w-full px-4 py-2 border rounded-md mb-4",value:q,onChange:e=>B(e.target.value)}),a.jsx("div",{className:"divide-y",children:L.filter(e=>e.title.toLowerCase().includes(q.toLowerCase())).map(e=>(0,a.jsxs)("div",{className:"py-3 px-2 hover:bg-gray-100 cursor-pointer flex items-center",onClick:()=>Y(e._id,e.title),children:[a.jsx("div",{className:"w-12 h-12 relative mr-3",children:a.jsx(l(),{src:e.image,alt:e.title,fill:!0,className:"object-cover rounded"})}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium",children:e.title}),a.jsx("p",{className:"text-sm text-gray-500",children:e.category})]})]},e._id))})]})}),I&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[a.jsx("h3",{className:"text-lg font-semibold",children:"Insert Image"}),a.jsx("button",{onClick:()=>E(!1),className:"text-gray-500 hover:text-gray-700",children:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsxs)("div",{className:"mb-6 p-4 border rounded-lg bg-gray-50",children:[a.jsx("h4",{className:"font-medium mb-3",children:"Upload New Image"}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[a.jsx("input",{type:"file",accept:"image/*",onChange:e=>T(e.target.files[0]),className:"flex-1"}),a.jsx("button",{type:"button",onClick:O,disabled:!A||U,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50",children:U?"Uploading...":"Upload"})]})]}),a.jsx("input",{type:"text",placeholder:"Search images...",className:"w-full px-4 py-2 border rounded-md mb-4",value:D,onChange:e=>F(e.target.value)}),a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:P.filter(e=>e.filename.toLowerCase().includes(D.toLowerCase())).map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-2 hover:bg-gray-100 cursor-pointer relative group",children:[(0,a.jsxs)("div",{className:"aspect-square relative mb-2",onClick:()=>X(e.url,e.filename),children:[a.jsx(l(),{src:e.url,alt:e.filename,fill:!0,className:"object-cover rounded"}),a.jsx("button",{onClick:t=>{t.stopPropagation(),H(e._id,e.url)},className:"absolute top-1 right-1 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",title:"Delete image",children:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsxs)("div",{onClick:()=>X(e.url,e.filename),children:[a.jsx("p",{className:"text-xs text-gray-600 truncate",children:e.filename}),a.jsx("p",{className:"text-xs text-gray-400",children:new Date(e.uploadDate).toLocaleDateString()})]})]},e._id))}),0===P.length&&a.jsx("div",{className:"text-center py-8 text-gray-500",children:"No images found. Upload your first image above."})]})})]}),a.jsx("p",{className:"text-xl mt-4",children:"Blog category"}),a.jsx("select",{name:"category",onChange:J,value:N.category,className:"w-40 mt-4 px-4 py-3 border text-gray-500",children:h.map(e=>a.jsx("option",{value:e.name,children:e.name},e._id))}),a.jsx("p",{className:"text-xl mt-4",children:"Blog author"}),b.length>0?(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-4 mt-4",children:[(0,a.jsxs)("select",{name:"authorId",onChange:J,value:N.authorId,className:"w-full sm:w-40 px-4 py-3 border text-gray-500",children:[a.jsx("option",{value:"",children:"Select an author"}),b.map(e=>a.jsx("option",{value:e._id,children:e.name},e._id))]}),N.authorId&&(0,a.jsxs)("div",{className:"flex items-center gap-3 mt-2 sm:mt-0",children:[a.jsx("img",{src:N.authorImg,alt:N.author,className:"w-10 h-10 rounded-full object-cover border border-gray-200"}),a.jsx("span",{className:"text-sm font-medium",children:N.author})]})]}):(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("p",{className:"text-red-500",children:"No authors available. Please add authors in Settings."}),a.jsx("input",{name:"author",onChange:J,value:N.author,className:"w-full sm:w-[500px] mt-2 px-4 py-3 border",type:"text",placeholder:"Author name",required:!0})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[a.jsx("button",{type:"submit",className:"mt-8 w-40 h-12 bg-black text-white",children:"UPDATE"}),a.jsx("button",{type:"button",onClick:()=>e.push("/admin/blogList"),className:"mt-8 w-40 h-12 border border-black",children:"CANCEL"}),a.jsx("button",{type:"button",onClick:Q,className:"mt-8 w-40 h-12 bg-red-600 text-white hover:bg-red-700",children:"DELETE"})]})]})})}},56524:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>r,default:()=>o});let a=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\admin\editBlog\[id]\page.jsx`),{__esModule:r,$$typeof:i}=a,o=a.default}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[1638,3998,337,8468,5757,7388],()=>s(89001));module.exports=a})();
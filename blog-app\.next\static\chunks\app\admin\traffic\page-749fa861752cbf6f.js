(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[524],{7769:function(e,t,a){Promise.resolve().then(a.bind(a,5210))},5210:function(e,t,a){"use strict";a.r(t);var s=a(7437),n=a(2265),o=a(7948),r=a(3799);a(1396);var i=a(2173);t.default=()=>{let[e,t]=(0,n.useState)(!0),[a,l]=(0,n.useState)("7days"),[c,d]=(0,n.useState)({totalViews:0,uniqueVisitors:0,viewsByType:[],topPages:[],trafficOverTime:[],topReferrers:[]}),u=async()=>{try{t(!0),console.log("Fetching analytics data for period:",a);let e=await (0,r.Z5)(a);console.log("Analytics data received:",e),e.success?(d(e.data),console.log("Analytics data set to state:",e.data)):(o.toast.error("Failed to load analytics data"),console.error("Failed to load analytics data:",e.message))}catch(t){var e;console.error("Error fetching analytics:",t),o.toast.error("Failed to load analytics data"),(null===(e=t.response)||void 0===e?void 0:e.status)===401&&(o.toast.error("Please log in as admin to view analytics"),window.location.href="/")}finally{t(!1)}};return(0,n.useEffect)(()=>{u()},[a]),(0,s.jsxs)("div",{className:"flex-1 pt-5 px-5 sm:pt-12 sm:pl-16",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Traffic Analytics"}),(0,s.jsxs)("button",{onClick:()=>{o.toast.info("Refreshing analytics data..."),u()},disabled:e,className:"flex items-center gap-2 px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition-colors disabled:opacity-50",children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,s.jsx)("path",{d:"M23 4v6h-6"}),(0,s.jsx)("path",{d:"M1 20v-6h6"}),(0,s.jsx)("path",{d:"M3.51 9a9 9 0 0 1 14.85-3.36L23 10"}),(0,s.jsx)("path",{d:"M20.49 15a9 9 0 0 1-14.85 3.36L1 14"})]}),e?"Refreshing...":"Refresh Data"]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{htmlFor:"period",className:"block text-sm font-medium text-gray-700 mb-1",children:"Time Period"}),(0,s.jsxs)("select",{id:"period",value:a,onChange:e=>{l(e.target.value)},className:"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"24hours",children:"Last 24 Hours"}),(0,s.jsx)("option",{value:"7days",children:"Last 7 Days"}),(0,s.jsx)("option",{value:"30days",children:"Last 30 Days"}),(0,s.jsx)("option",{value:"90days",children:"Last 90 Days"})]})]}),e?(0,s.jsx)("div",{className:"text-center py-10",children:(0,s.jsx)("p",{className:"text-gray-500",children:"Loading analytics data..."})}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-700",children:"Total Page Views"}),(0,s.jsx)("p",{className:"text-3xl font-bold mt-2",children:(0,r.uf)(c.totalViews)}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"All pages combined"})]}),(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-700",children:"Unique Visitors"}),(0,s.jsx)("p",{className:"text-3xl font-bold mt-2",children:(0,r.uf)(c.uniqueVisitors)}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Based on unique IP addresses"})]}),(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-700",children:"Pages Per Visitor"}),(0,s.jsx)("p",{className:"text-3xl font-bold mt-2",children:c.uniqueVisitors>0?(c.totalViews/c.uniqueVisitors).toFixed(1):"0.0"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Average pages viewed"})]})]}),(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md mb-8",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-700 mb-4",children:"Content Type Breakdown"}),0===c.viewsByType.length?(0,s.jsx)("p",{className:"text-gray-500",children:"No content type data available"}):(0,s.jsx)("div",{className:"space-y-3",children:c.viewsByType.map(e=>(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"capitalize",children:e._id}),(0,s.jsxs)("span",{className:"font-semibold",children:[(0,r.uf)(e.count)," views"]})]},e._id))})]}),(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md mb-8",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-700 mb-4",children:"Top Pages"}),0===c.topPages.length?(0,s.jsx)("p",{className:"text-gray-500",children:"No page view data available"}):(0,s.jsx)("div",{className:"space-y-3",children:c.topPages.map(e=>(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"truncate max-w-md",children:e._id}),(0,s.jsxs)("span",{className:"font-semibold",children:[(0,r.uf)(e.count)," views"]})]},e._id))})]}),(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md mb-8",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-700 mb-4",children:"Top Referrers"}),0===c.topReferrers.length?(0,s.jsx)("p",{className:"text-gray-500",children:"No referrer data available"}):(0,s.jsx)("div",{className:"space-y-3",children:c.topReferrers.map(e=>(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"truncate max-w-md",children:e._id}),(0,s.jsxs)("span",{className:"font-semibold",children:[(0,r.uf)(e.count)," visits"]})]},e._id))})]}),0===c.totalViews&&(0,s.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 p-4 rounded-md",children:[(0,s.jsx)("h3",{className:"font-medium text-yellow-800 mb-2",children:"No analytics data available"}),(0,s.jsx)("p",{className:"text-yellow-700",children:"This could be because:"}),(0,s.jsxs)("ul",{className:"list-disc ml-5 mt-2 text-yellow-700",children:[(0,s.jsx)("li",{children:"No page views have been tracked yet"}),(0,s.jsx)("li",{children:"Analytics tracking is disabled in development mode"}),(0,s.jsx)("li",{children:"The selected time period doesn't contain any data"})]}),(0,s.jsx)("p",{className:"mt-3 text-yellow-700",children:"Try visiting some pages on your site or switching to a different time period."})]}),(0,s.jsxs)("div",{className:"mt-8 border-t pt-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Debug Tools"}),(0,s.jsx)("button",{onClick:async()=>{try{let e=localStorage.getItem("authToken");console.log("Current auth token:",e?"Token exists":"No token");let t=await i.Z.get("/api/analytics/debug");console.log("Analytics debug info:",t.data),o.toast.info("Debug info logged to console")}catch(e){console.error("Debug check failed:",e),o.toast.error("Debug check failed")}},className:"bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded",children:"Run Diagnostics"})]})]})]})},(0,s.jsxs)("div",{className:"mt-8 border-t pt-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Debug Tools"}),(0,s.jsx)("button",{onClick:async()=>{try{let e=localStorage.getItem("authToken");console.log("Current auth token:",e?"Token exists":"No token");let t=await i.Z.get("/api/debug",{headers:{Authorization:"Bearer ".concat(e)}});console.log("Token debug info:",t.data);let a=await i.Z.get("/api/analytics/debug");console.log("Analytics debug info:",a.data),o.toast.info("Debug info logged to console")}catch(e){console.error("Debug check failed:",e),o.toast.error("Debug check failed")}},className:"bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded",children:"Run Diagnostics"})]})},6993:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return s}});let s=a(1024)._(a(2265)).default.createContext(null)},3799:function(e,t,a){"use strict";a.d(t,{Z0:function(){return n},Z5:function(){return r},uf:function(){return o}});var s=a(2173);let n=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"page",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;try{console.log("Tracking page view:",{path:e,contentType:t,blogId:a});let n=document.referrer||null,o=await s.Z.post("/api/analytics",{path:e,contentType:t,blogId:a,referrer:n});console.log("Analytics tracking response:",o.data)}catch(e){console.error("Analytics error:",e)}},o=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),r=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"7days",t=localStorage.getItem("authToken");if(!t)throw Error("Authentication required");return(await s.Z.get("/api/analytics?period=".concat(e),{headers:{Authorization:"Bearer ".concat(t)}})).data}},7948:function(e,t,a){"use strict";a.r(t),a.d(t,{Bounce:function(){return R},Flip:function(){return S},Icons:function(){return A},Slide:function(){return $},ToastContainer:function(){return z},Zoom:function(){return M},collapseToast:function(){return d},cssTransition:function(){return u},toast:function(){return k},useToast:function(){return N},useToastContainer:function(){return T}});var s=a(2265),n=function(){for(var e,t,a=0,s="",n=arguments.length;a<n;a++)(e=arguments[a])&&(t=function e(t){var a,s,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t){if(Array.isArray(t)){var o=t.length;for(a=0;a<o;a++)t[a]&&(s=e(t[a]))&&(n&&(n+=" "),n+=s)}else for(s in t)t[s]&&(n&&(n+=" "),n+=s)}return n}(e))&&(s&&(s+=" "),s+=t);return s};let o=e=>"number"==typeof e&&!isNaN(e),r=e=>"string"==typeof e,i=e=>"function"==typeof e,l=e=>r(e)||i(e)?e:null,c=e=>(0,s.isValidElement)(e)||r(e)||i(e)||o(e);function d(e,t,a){void 0===a&&(a=300);let{scrollHeight:s,style:n}=e;requestAnimationFrame(()=>{n.minHeight="initial",n.height=s+"px",n.transition=`all ${a}ms`,requestAnimationFrame(()=>{n.height="0",n.padding="0",n.margin="0",setTimeout(t,a)})})}function u(e){let{enter:t,exit:a,appendPosition:n=!1,collapse:o=!0,collapseDuration:r=300}=e;return function(e){let{children:i,position:l,preventExitTransition:c,done:u,nodeRef:m,isIn:p,playToast:g}=e,f=n?`${t}--${l}`:t,h=n?`${a}--${l}`:a,y=(0,s.useRef)(0);return(0,s.useLayoutEffect)(()=>{let e=m.current,t=f.split(" "),a=s=>{s.target===m.current&&(g(),e.removeEventListener("animationend",a),e.removeEventListener("animationcancel",a),0===y.current&&"animationcancel"!==s.type&&e.classList.remove(...t))};e.classList.add(...t),e.addEventListener("animationend",a),e.addEventListener("animationcancel",a)},[]),(0,s.useEffect)(()=>{let e=m.current,t=()=>{e.removeEventListener("animationend",t),o?d(e,u,r):u()};p||(c?t():(y.current=1,e.className+=` ${h}`,e.addEventListener("animationend",t)))},[p]),s.createElement(s.Fragment,null,i)}}function m(e,t){return null!=e?{content:e.content,containerId:e.props.containerId,id:e.props.toastId,theme:e.props.theme,type:e.props.type,data:e.props.data||{},isLoading:e.props.isLoading,icon:e.props.icon,status:t}:{}}let p=new Map,g=[],f=new Set,h=e=>f.forEach(t=>t(e)),y=()=>p.size>0;function v(e,t){var a;if(t)return!(null==(a=p.get(t))||!a.isToastActive(e));let s=!1;return p.forEach(t=>{t.isToastActive(e)&&(s=!0)}),s}function b(e,t){c(e)&&(y()||g.push({content:e,options:t}),p.forEach(a=>{a.buildToast(e,t)}))}function x(e,t){p.forEach(a=>{null!=t&&null!=t&&t.containerId?(null==t?void 0:t.containerId)===a.id&&a.toggle(e,null==t?void 0:t.id):a.toggle(e,null==t?void 0:t.id)})}function T(e){let{subscribe:t,getSnapshot:a,setProps:n}=(0,s.useRef)(function(e){let t=e.containerId||1;return{subscribe(a){let n=function(e,t,a){let n=1,d=0,u=[],p=[],g=[],f=t,h=new Map,y=new Set,v=()=>{g=Array.from(h.values()),y.forEach(e=>e())},b=e=>{p=null==e?[]:p.filter(t=>t!==e),v()},x=e=>{let{toastId:t,onOpen:n,updateId:o,children:r}=e.props,l=null==o;e.staleId&&h.delete(e.staleId),h.set(t,e),p=[...p,e.props.toastId].filter(t=>t!==e.staleId),v(),a(m(e,l?"added":"updated")),l&&i(n)&&n((0,s.isValidElement)(r)&&r.props)};return{id:e,props:f,observe:e=>(y.add(e),()=>y.delete(e)),toggle:(e,t)=>{h.forEach(a=>{null!=t&&t!==a.props.toastId||i(a.toggle)&&a.toggle(e)})},removeToast:b,toasts:h,clearQueue:()=>{d-=u.length,u=[]},buildToast:(t,p)=>{var g,y;if((t=>{let{containerId:a,toastId:s,updateId:n}=t,o=h.has(s)&&null==n;return(a?a!==e:1!==e)||o})(p))return;let{toastId:T,updateId:N,data:w,staleId:E,delay:j}=p,_=()=>{b(T)},I=null==N;I&&d++;let C={...f,style:f.toastStyle,key:n++,...Object.fromEntries(Object.entries(p).filter(e=>{let[t,a]=e;return null!=a})),toastId:T,updateId:N,data:w,closeToast:_,isIn:!1,className:l(p.className||f.toastClassName),bodyClassName:l(p.bodyClassName||f.bodyClassName),progressClassName:l(p.progressClassName||f.progressClassName),autoClose:!p.isLoading&&(g=p.autoClose,y=f.autoClose,!1===g||o(g)&&g>0?g:y),deleteToast(){let e=h.get(T),{onClose:t,children:n}=e.props;i(t)&&t((0,s.isValidElement)(n)&&n.props),a(m(e,"removed")),h.delete(T),--d<0&&(d=0),u.length>0?x(u.shift()):v()}};C.closeButton=f.closeButton,!1===p.closeButton||c(p.closeButton)?C.closeButton=p.closeButton:!0===p.closeButton&&(C.closeButton=!c(f.closeButton)||f.closeButton);let k=t;(0,s.isValidElement)(t)&&!r(t.type)?k=(0,s.cloneElement)(t,{closeToast:_,toastProps:C,data:w}):i(t)&&(k=t({closeToast:_,toastProps:C,data:w}));let L={content:k,props:C,staleId:E};f.limit&&f.limit>0&&d>f.limit&&I?u.push(L):o(j)?setTimeout(()=>{x(L)},j):x(L)},setProps(e){f=e},setToggle:(e,t)=>{h.get(e).toggle=t},isToastActive:e=>p.some(t=>t===e),getSnapshot:()=>f.newestOnTop?g.reverse():g}}(t,e,h);p.set(t,n);let d=n.observe(a);return g.forEach(e=>b(e.content,e.options)),g=[],()=>{d(),p.delete(t)}},setProps(e){var a;null==(a=p.get(t))||a.setProps(e)},getSnapshot(){var e;return null==(e=p.get(t))?void 0:e.getSnapshot()}}}(e)).current;n(e);let d=(0,s.useSyncExternalStore)(t,a,a);return{getToastToRender:function(e){if(!d)return[];let t=new Map;return d.forEach(e=>{let{position:a}=e.props;t.has(a)||t.set(a,[]),t.get(a).push(e)}),Array.from(t,t=>e(t[0],t[1]))},isToastActive:v,count:null==d?void 0:d.length}}function N(e){var t,a;let[n,o]=(0,s.useState)(!1),[r,i]=(0,s.useState)(!1),l=(0,s.useRef)(null),c=(0,s.useRef)({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:d,pauseOnHover:u,closeToast:m,onClick:g,closeOnClick:f}=e;function h(){o(!0)}function y(){o(!1)}function v(t){let a=l.current;c.canDrag&&a&&(c.didMove=!0,n&&y(),c.delta="x"===e.draggableDirection?t.clientX-c.start:t.clientY-c.start,c.start!==t.clientX&&(c.canCloseOnClick=!1),a.style.transform=`translate3d(${"x"===e.draggableDirection?`${c.delta}px, var(--y)`:`0, calc(${c.delta}px + var(--y))`},0)`,a.style.opacity=""+(1-Math.abs(c.delta/c.removalDistance)))}function b(){document.removeEventListener("pointermove",v),document.removeEventListener("pointerup",b);let t=l.current;if(c.canDrag&&c.didMove&&t){if(c.canDrag=!1,Math.abs(c.delta)>c.removalDistance)return i(!0),e.closeToast(),void e.collapseAll();t.style.transition="transform 0.2s, opacity 0.2s",t.style.removeProperty("transform"),t.style.removeProperty("opacity")}}null==(a=p.get((t={id:e.toastId,containerId:e.containerId,fn:o}).containerId||1))||a.setToggle(t.id,t.fn),(0,s.useEffect)(()=>{if(e.pauseOnFocusLoss)return document.hasFocus()||y(),window.addEventListener("focus",h),window.addEventListener("blur",y),()=>{window.removeEventListener("focus",h),window.removeEventListener("blur",y)}},[e.pauseOnFocusLoss]);let x={onPointerDown:function(t){if(!0===e.draggable||e.draggable===t.pointerType){c.didMove=!1,document.addEventListener("pointermove",v),document.addEventListener("pointerup",b);let a=l.current;c.canCloseOnClick=!0,c.canDrag=!0,a.style.transition="none","x"===e.draggableDirection?(c.start=t.clientX,c.removalDistance=a.offsetWidth*(e.draggablePercent/100)):(c.start=t.clientY,c.removalDistance=a.offsetHeight*(80===e.draggablePercent?1.5*e.draggablePercent:e.draggablePercent)/100)}},onPointerUp:function(t){let{top:a,bottom:s,left:n,right:o}=l.current.getBoundingClientRect();"touchend"!==t.nativeEvent.type&&e.pauseOnHover&&t.clientX>=n&&t.clientX<=o&&t.clientY>=a&&t.clientY<=s?y():h()}};return d&&u&&(x.onMouseEnter=y,e.stacked||(x.onMouseLeave=h)),f&&(x.onClick=e=>{g&&g(e),c.canCloseOnClick&&m()}),{playToast:h,pauseToast:y,isRunning:n,preventExitTransition:r,toastRef:l,eventHandlers:x}}function w(e){let{delay:t,isRunning:a,closeToast:o,type:r="default",hide:l,className:c,style:d,controlledProgress:u,progress:m,rtl:p,isIn:g,theme:f}=e,h=l||u&&0===m,y={...d,animationDuration:`${t}ms`,animationPlayState:a?"running":"paused"};u&&(y.transform=`scaleX(${m})`);let v=n("Toastify__progress-bar",u?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${f}`,`Toastify__progress-bar--${r}`,{"Toastify__progress-bar--rtl":p}),b=i(c)?c({rtl:p,type:r,defaultClassName:v}):n(v,c);return s.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":h},s.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${f} Toastify__progress-bar--${r}`}),s.createElement("div",{role:"progressbar","aria-hidden":h?"true":"false","aria-label":"notification timer",className:b,style:y,[u&&m>=1?"onTransitionEnd":"onAnimationEnd"]:u&&m<1?null:()=>{g&&o()}}))}let E=1,j=()=>""+E++;function _(e,t){return b(e,t),t.toastId}function I(e,t){return{...t,type:t&&t.type||e,toastId:t&&(r(t.toastId)||o(t.toastId))?t.toastId:j()}}function C(e){return(t,a)=>_(t,I(e,a))}function k(e,t){return _(e,I("default",t))}k.loading=(e,t)=>_(e,I("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...t})),k.promise=function(e,t,a){let s,{pending:n,error:o,success:l}=t;n&&(s=r(n)?k.loading(n,a):k.loading(n.render,{...a,...n}));let c={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},d=(e,t,n)=>{if(null==t)return void k.dismiss(s);let o={type:e,...c,...a,data:n},i=r(t)?{render:t}:t;return s?k.update(s,{...o,...i}):k(i.render,{...o,...i}),n},u=i(e)?e():e;return u.then(e=>d("success",l,e)).catch(e=>d("error",o,e)),u},k.success=C("success"),k.info=C("info"),k.error=C("error"),k.warning=C("warning"),k.warn=k.warning,k.dark=(e,t)=>_(e,I("default",{theme:"dark",...t})),k.dismiss=function(e){var t,a;y()?null==e||r(t=e)||o(t)?p.forEach(t=>{t.removeToast(e)}):e&&("containerId"in e||"id"in e)&&((null==(a=p.get(e.containerId))?void 0:a.removeToast(e.id))||p.forEach(t=>{t.removeToast(e.id)})):g=g.filter(t=>null!=e&&t.options.toastId!==e)},k.clearWaitingQueue=function(e){void 0===e&&(e={}),p.forEach(t=>{!t.props.limit||e.containerId&&t.id!==e.containerId||t.clearQueue()})},k.isActive=v,k.update=function(e,t){void 0===t&&(t={});let a=((e,t)=>{var a;let{containerId:s}=t;return null==(a=p.get(s||1))?void 0:a.toasts.get(e)})(e,t);if(a){let{props:s,content:n}=a,o={delay:100,...s,...t,toastId:t.toastId||e,updateId:j()};o.toastId!==e&&(o.staleId=e);let r=o.render||n;delete o.render,_(r,o)}},k.done=e=>{k.update(e,{progress:1})},k.onChange=function(e){return f.add(e),()=>{f.delete(e)}},k.play=e=>x(!0,e),k.pause=e=>x(!1,e);let L="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,P=e=>{let{theme:t,type:a,isLoading:n,...o}=e;return s.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:"colored"===t?"currentColor":`var(--toastify-icon-color-${a})`,...o})},A={info:function(e){return s.createElement(P,{...e},s.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))},warning:function(e){return s.createElement(P,{...e},s.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))},success:function(e){return s.createElement(P,{...e},s.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))},error:function(e){return s.createElement(P,{...e},s.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))},spinner:function(){return s.createElement("div",{className:"Toastify__spinner"})}},D=e=>{let{isRunning:t,preventExitTransition:a,toastRef:o,eventHandlers:r,playToast:l}=N(e),{closeButton:c,children:d,autoClose:u,onClick:m,type:p,hideProgressBar:g,closeToast:f,transition:h,position:y,className:v,style:b,bodyClassName:x,bodyStyle:T,progressClassName:E,progressStyle:j,updateId:_,role:I,progress:C,rtl:k,toastId:L,deleteToast:P,isIn:D,isLoading:B,closeOnClick:R,theme:$}=e,M=n("Toastify__toast",`Toastify__toast-theme--${$}`,`Toastify__toast--${p}`,{"Toastify__toast--rtl":k},{"Toastify__toast--close-on-click":R}),S=i(v)?v({rtl:k,position:y,type:p,defaultClassName:M}):n(M,v),O=function(e){let{theme:t,type:a,isLoading:n,icon:o}=e,r=null,l={theme:t,type:a,isLoading:n};return!1===o||(i(o)?r=o(l):(0,s.isValidElement)(o)?r=(0,s.cloneElement)(o,l):n?r=A.spinner():a in A&&(r=A[a](l))),r}(e),z=!!C||!u,F={closeToast:f,type:p,theme:$},V=null;return!1===c||(V=i(c)?c(F):(0,s.isValidElement)(c)?(0,s.cloneElement)(c,F):function(e){let{closeToast:t,theme:a,ariaLabel:n="close"}=e;return s.createElement("button",{className:`Toastify__close-button Toastify__close-button--${a}`,type:"button",onClick:e=>{e.stopPropagation(),t(e)},"aria-label":n},s.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},s.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}(F)),s.createElement(h,{isIn:D,done:P,position:y,preventExitTransition:a,nodeRef:o,playToast:l},s.createElement("div",{id:L,onClick:m,"data-in":D,className:S,...r,style:b,ref:o},s.createElement("div",{...D&&{role:I},className:i(x)?x({type:p}):n("Toastify__toast-body",x),style:T},null!=O&&s.createElement("div",{className:n("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!B})},O),s.createElement("div",null,d)),V,s.createElement(w,{..._&&!z?{key:`pb-${_}`}:{},rtl:k,theme:$,delay:u,isRunning:t,isIn:D,closeToast:f,hide:g,type:p,style:j,className:E,controlledProgress:z,progress:C||0})))},B=function(e,t){return void 0===t&&(t=!1),{enter:`Toastify--animate Toastify__${e}-enter`,exit:`Toastify--animate Toastify__${e}-exit`,appendPosition:t}},R=u(B("bounce",!0)),$=u(B("slide",!0)),M=u(B("zoom")),S=u(B("flip")),O={position:"top-right",transition:R,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light"};function z(e){let t={...O,...e},a=e.stacked,[o,r]=(0,s.useState)(!0),c=(0,s.useRef)(null),{getToastToRender:d,isToastActive:u,count:m}=T(t),{className:p,style:g,rtl:f,containerId:h}=t;function y(){a&&(r(!0),k.play())}return L(()=>{if(a){var e;let a=c.current.querySelectorAll('[data-in="true"]'),s=null==(e=t.position)?void 0:e.includes("top"),n=0,r=0;Array.from(a).reverse().forEach((e,t)=>{e.classList.add("Toastify__toast--stacked"),t>0&&(e.dataset.collapsed=`${o}`),e.dataset.pos||(e.dataset.pos=s?"top":"bot");let a=n*(o?.2:1)+(o?0:12*t);e.style.setProperty("--y",`${s?a:-1*a}px`),e.style.setProperty("--g","12"),e.style.setProperty("--s",""+(1-(o?r:0))),n+=e.offsetHeight,r+=.025})}},[o,m,a]),s.createElement("div",{ref:c,className:"Toastify",id:h,onMouseEnter:()=>{a&&(r(!1),k.pause())},onMouseLeave:y},d((e,t)=>{let o=t.length?{...g}:{...g,pointerEvents:"none"};return s.createElement("div",{className:function(e){let t=n("Toastify__toast-container",`Toastify__toast-container--${e}`,{"Toastify__toast-container--rtl":f});return i(p)?p({position:e,rtl:f,defaultClassName:t}):n(t,l(p))}(e),style:o,key:`container-${e}`},t.map(e=>{let{content:t,props:n}=e;return s.createElement(D,{...n,stacked:a,collapseAll:y,isIn:u(n.toastId,n.containerId),style:n.style,key:`toast-${n.key}`},t)}))}))}}},function(e){e.O(0,[580,396,971,938,744],function(){return e(e.s=7769)}),_N_E=e.O()}]);
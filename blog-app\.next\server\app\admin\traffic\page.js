(()=>{var e={};e.id=9524,e.ids=[9524],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},87477:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var a=s(50482),r=s(69108),i=s(62563),o=s.n(i),l=s(68300),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);s.d(t,n);let d=["",{children:["admin",{children:["traffic",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,15321)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\traffic\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,32143)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,39459)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\traffic\\page.jsx"],p="/admin/traffic/page",m={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/traffic/page",pathname:"/admin/traffic",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29907:(e,t,s)=>{Promise.resolve().then(s.bind(s,44266))},44266:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(95344),r=s(3729),i=s(69697),o=s(60267);s(20783);var l=s(53608);let n=()=>{let[e,t]=(0,r.useState)(!0),[s,n]=(0,r.useState)("7days"),[d,c]=(0,r.useState)({totalViews:0,uniqueVisitors:0,viewsByType:[],topPages:[],trafficOverTime:[],topReferrers:[]}),p=async()=>{try{t(!0),console.log("Fetching analytics data for period:",s);let e=await (0,o.Z5)(s);console.log("Analytics data received:",e),e.success?(c(e.data),console.log("Analytics data set to state:",e.data)):(i.toast.error("Failed to load analytics data"),console.error("Failed to load analytics data:",e.message))}catch(e){console.error("Error fetching analytics:",e),i.toast.error("Failed to load analytics data"),e.response?.status===401&&(i.toast.error("Please log in as admin to view analytics"),window.location.href="/")}finally{t(!1)}};return(0,r.useEffect)(()=>{p()},[s]),(0,a.jsxs)("div",{className:"flex-1 pt-5 px-5 sm:pt-12 sm:pl-16",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[a.jsx("h1",{className:"text-2xl font-bold",children:"Traffic Analytics"}),(0,a.jsxs)("button",{onClick:()=>{i.toast.info("Refreshing analytics data..."),p()},disabled:e,className:"flex items-center gap-2 px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition-colors disabled:opacity-50",children:[(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[a.jsx("path",{d:"M23 4v6h-6"}),a.jsx("path",{d:"M1 20v-6h6"}),a.jsx("path",{d:"M3.51 9a9 9 0 0 1 14.85-3.36L23 10"}),a.jsx("path",{d:"M20.49 15a9 9 0 0 1-14.85 3.36L1 14"})]}),e?"Refreshing...":"Refresh Data"]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("label",{htmlFor:"period",className:"block text-sm font-medium text-gray-700 mb-1",children:"Time Period"}),(0,a.jsxs)("select",{id:"period",value:s,onChange:e=>{n(e.target.value)},className:"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[a.jsx("option",{value:"24hours",children:"Last 24 Hours"}),a.jsx("option",{value:"7days",children:"Last 7 Days"}),a.jsx("option",{value:"30days",children:"Last 30 Days"}),a.jsx("option",{value:"90days",children:"Last 90 Days"})]})]}),e?a.jsx("div",{className:"text-center py-10",children:a.jsx("p",{className:"text-gray-500",children:"Loading analytics data..."})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md",children:[a.jsx("h2",{className:"text-lg font-semibold text-gray-700",children:"Total Page Views"}),a.jsx("p",{className:"text-3xl font-bold mt-2",children:(0,o.uf)(d.totalViews)}),a.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"All pages combined"})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md",children:[a.jsx("h2",{className:"text-lg font-semibold text-gray-700",children:"Unique Visitors"}),a.jsx("p",{className:"text-3xl font-bold mt-2",children:(0,o.uf)(d.uniqueVisitors)}),a.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Based on unique IP addresses"})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md",children:[a.jsx("h2",{className:"text-lg font-semibold text-gray-700",children:"Pages Per Visitor"}),a.jsx("p",{className:"text-3xl font-bold mt-2",children:d.uniqueVisitors>0?(d.totalViews/d.uniqueVisitors).toFixed(1):"0.0"}),a.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Average pages viewed"})]})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md mb-8",children:[a.jsx("h2",{className:"text-lg font-semibold text-gray-700 mb-4",children:"Content Type Breakdown"}),0===d.viewsByType.length?a.jsx("p",{className:"text-gray-500",children:"No content type data available"}):a.jsx("div",{className:"space-y-3",children:d.viewsByType.map(e=>(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"capitalize",children:e._id}),(0,a.jsxs)("span",{className:"font-semibold",children:[(0,o.uf)(e.count)," views"]})]},e._id))})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md mb-8",children:[a.jsx("h2",{className:"text-lg font-semibold text-gray-700 mb-4",children:"Top Pages"}),0===d.topPages.length?a.jsx("p",{className:"text-gray-500",children:"No page view data available"}):a.jsx("div",{className:"space-y-3",children:d.topPages.map(e=>(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"truncate max-w-md",children:e._id}),(0,a.jsxs)("span",{className:"font-semibold",children:[(0,o.uf)(e.count)," views"]})]},e._id))})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md mb-8",children:[a.jsx("h2",{className:"text-lg font-semibold text-gray-700 mb-4",children:"Top Referrers"}),0===d.topReferrers.length?a.jsx("p",{className:"text-gray-500",children:"No referrer data available"}):a.jsx("div",{className:"space-y-3",children:d.topReferrers.map(e=>(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"truncate max-w-md",children:e._id}),(0,a.jsxs)("span",{className:"font-semibold",children:[(0,o.uf)(e.count)," visits"]})]},e._id))})]}),0===d.totalViews&&(0,a.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 p-4 rounded-md",children:[a.jsx("h3",{className:"font-medium text-yellow-800 mb-2",children:"No analytics data available"}),a.jsx("p",{className:"text-yellow-700",children:"This could be because:"}),(0,a.jsxs)("ul",{className:"list-disc ml-5 mt-2 text-yellow-700",children:[a.jsx("li",{children:"No page views have been tracked yet"}),a.jsx("li",{children:"Analytics tracking is disabled in development mode"}),a.jsx("li",{children:"The selected time period doesn't contain any data"})]}),a.jsx("p",{className:"mt-3 text-yellow-700",children:"Try visiting some pages on your site or switching to a different time period."})]}),(0,a.jsxs)("div",{className:"mt-8 border-t pt-4",children:[a.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Debug Tools"}),a.jsx("button",{onClick:async()=>{try{let e=localStorage.getItem("authToken");console.log("Current auth token:",e?"Token exists":"No token");let t=await l.Z.get("/api/analytics/debug");console.log("Analytics debug info:",t.data),i.toast.info("Debug info logged to console")}catch(e){console.error("Debug check failed:",e),i.toast.error("Debug check failed")}},className:"bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded",children:"Run Diagnostics"})]})]})]})};(0,a.jsxs)("div",{className:"mt-8 border-t pt-4",children:[a.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Debug Tools"}),a.jsx("button",{onClick:async()=>{try{let e=localStorage.getItem("authToken");console.log("Current auth token:",e?"Token exists":"No token");let t=await l.Z.get("/api/debug",{headers:{Authorization:`Bearer ${e}`}});console.log("Token debug info:",t.data);let s=await l.Z.get("/api/analytics/debug");console.log("Analytics debug info:",s.data),i.toast.info("Debug info logged to console")}catch(e){console.error("Debug check failed:",e),i.toast.error("Debug check failed")}},className:"bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded",children:"Run Diagnostics"})]})},60267:(e,t,s)=>{"use strict";s.d(t,{Z0:()=>r,Z5:()=>o,uf:()=>i});var a=s(53608);let r=async(e,t="page",s=null)=>{try{console.log("Tracking page view:",{path:e,contentType:t,blogId:s});let r=document.referrer||null,i=await a.Z.post("/api/analytics",{path:e,contentType:t,blogId:s,referrer:r});console.log("Analytics tracking response:",i.data)}catch(e){console.error("Analytics error:",e)}},i=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),o=async(e="7days")=>{let t=localStorage.getItem("authToken");if(!t)throw Error("Authentication required");return(await a.Z.get(`/api/analytics?period=${e}`,{headers:{Authorization:`Bearer ${t}`}})).data}},15321:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>r,default:()=>o});let a=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\admin\traffic\page.jsx`),{__esModule:r,$$typeof:i}=a,o=a.default}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[1638,3998,337,8468,5757,7388],()=>s(87477));module.exports=a})();
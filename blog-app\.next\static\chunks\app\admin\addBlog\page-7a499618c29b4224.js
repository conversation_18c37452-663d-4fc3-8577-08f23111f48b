(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[714],{248:function(e,t,a){Promise.resolve().then(a.bind(a,9216))},4257:function(e,t,a){"use strict";a.d(t,{L:function(){return s}});let s={facebook_icon:{src:"/_next/static/media/facebook_icon.cbcfc36d.png",height:58,width:58,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAbUlEQVR42m3HsRGCQBCG0X/sgArowytFG9BCqIESnLEHAhswNrqYmcvkAmB3P4Yh5WVPXCRP3ntvV2mf7B7sArtJGhsvkJfPAl7GRjUZxIs3hFOTcmsVvutvBZtyK+nfgRPA1OlQHvMwD+WpMxvnWUuxSavcBwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},googleplus_icon:{src:"/_next/static/media/googleplus_icon.15e2de32.png",height:59,width:59,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAe0lEQVR42mP4z8TA8Mv+z5x/M36bMzCAeAy/k/5Dwe9gBgaGZyL/P3z6f/3Xv59///97fIOT4b3d///H/s76v/3/g///f77WY7il8P/r+/+Hf73/9f//39dnhBkYGD41/f8PAv/+fyphgICXSV+3fNv4IoIBHfxnZGAAALhiS7/aN4AvAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},twitter_icon:{src:"/_next/static/media/twitter_icon.0d1dc581.png",height:59,width:59,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAeklEQVR42l3HvQnCUBiG0Uc7ax1AsLe10yFsrDODEziFhStYCO5gIThCGkHEgJqfC8mX5L4hJFVOd9AYbFOfyqOtoB1loJ5tgddM/0w/uxVOet4nxGspqtFCuWTfJeHcu0pnC02qoscUSA9eLa9kT+cTuKu7vHcMaQQNWbdKicv1GyQAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},profile_icon:{src:"/_next/static/media/profile_icon.fa2679c4.png",height:92,width:92,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABCklEQVR42iWPMU/CUACEn7/B+CPen3CS+BccTNyMm2kdpDoYiFEjVkgcQGOVONRookZUGlqf5Ukk9CkVU4ymxk2CU/e+5Shw2919NxwZqtf7o93ggzPmSKt6L5vNBvf9Nzoq+/1/KjwvUlUFiQWZINC0NFyXRcmAkjAMeTabwewUiRvlPMyt9BCMS6Ui6i7jxG+35fRMCgVlHt+VM7DDHFKTBHv6PhzHlqQbBHJ7N4eTDQW/1iWqO2vIryyi5jhgjwkghMc9IfBwexV/Xp/CXF5A7e4mfu908MRsTl5Fi9Y5j1ovz/ixL2CocyhurqJsHEWWbY+fZPQCNY8P+Jd1Liu6Jo31JZ7Eo3IAXaWfc0g8AF4AAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},logo:{src:"/_next/static/media/logo.c649e147.png",height:53,width:186,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAQAAADPnVVmAAAAKklEQVR42mNgaGdIY9BhcGQwZjBgMGHQYWCoZWhnMGSwY3BjyGSIYjAAAFAcBMpReGCWAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:2},arrow:{src:"/_next/static/media/arrow.35bdbbc1.png",height:16,width:18,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAQAAACfUMTVAAAATUlEQVR42mPQZNRgZGDQ4NC4qeHPwKDJxgADGvYazzRcGRgYNLk1eTR4NPkZGDS8NF5o+DBoHtI4p3lW44zmFY1tGp80fmGowDQD3RYA4awVkVQ4JrAAAAAASUVORK5CYII=",blurWidth:8,blurHeight:7},logo_light:{src:"/_next/static/media/logo_light.9ce1f99e.png",height:55,width:201,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAQAAADPnVVmAAAALElEQVR42mP41/Av9J/uP7d/5v8s/tn8M2X41/Sv9p/OP9t/rv9y/0X/MwIAZagUsO6duCoAAAAASUVORK5CYII=",blurWidth:8,blurHeight:2},blog_icon:{src:"/_next/static/media/blog_icon.6cf97bbc.png",height:32,width:32,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAVklEQVR42jXNMRGDQBAAwC2pGfozgA4KcjMMFIejN5IoSI8YLGAAKtbAQiil0xshNGky2J1GygccLrue1YKf22HQsUn8fTEpygwgFaGZpUVq4m03qxI8rIYRbx4WRDgAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},add_icon:{src:"/_next/static/media/add_icon.17426346.png",height:32,width:32,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAARElEQVR42mWNsQnAMAwEr3LwUMoGmiJKlf37HMZgg/+aP4EkRpKSZOYhaBI2kxboAqFRNOWTzqXxroGtILn3lePo8fYH8E4LJKezO8EAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},email_icon:{src:"/_next/static/media/email_icon.4caec7c6.png",height:32,width:32,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAUUlEQVR42l3NsQmAMAAEwHONrCBqlVJrG1dQ1MIt4pZmHQMBC7nu4f9pAEADg9lSzDoIsmQskiwQ7S5tcTlEotPmlqw1CB63qagV9N/ogP/tC+8IDv7EJZnRAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},upload_area:{src:"/_next/static/media/upload_area.1ee5fe3d.png",height:140,width:240,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAMAAABPT11nAAAANlBMVEX6+vr6+vn5+fn5+fj5+Pj4+Pj39/f29vb19fXz8/Py8vLv7+/u7u7r6+rp6ejj5evf4OXc3+fsgmBfAAAALUlEQVR42g3GtwEAIAwDMFMc0wn/P0s0Cc1UqqzBiPsSDWJ3HxSU19kzR8QgfRm1AShVawqCAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:5}};Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now()},9216:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return m}});var s=a(7437),r=a(4257),o=a(2173),n=a(6691),l=a.n(n),i=a(2265),c=a(7948),d=a(1396),u=a.n(d),g=e=>{let{authorImg:t,title:a,author:o,date:n,deleteBlog:i,mongoId:c}=e,d=new Date(n);return(0,s.jsxs)("tr",{className:"bg-white border-b hover:bg-gray-50",children:[(0,s.jsx)("th",{scope:"row",className:"hidden sm:table-cell px-6 py-4 font-medium text-gray-900",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(l(),{width:40,height:40,src:t||r.L.profile_icon,alt:o||"Author",className:"rounded-full object-cover w-10 h-10 border border-gray-200"}),(0,s.jsx)("p",{className:"truncate",children:o||"No author"})]})}),(0,s.jsx)("td",{className:"px-6 py-4 truncate",children:a||"No title"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:d.toDateString()}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)(u(),{href:"/blogs/".concat(c),target:"_blank",className:"cursor-pointer text-green-600 hover:underline",children:"View"}),(0,s.jsx)(u(),{href:"/admin/editBlog/".concat(c),className:"cursor-pointer text-blue-600 hover:underline",children:"Edit"}),(0,s.jsx)("button",{onClick:()=>i(c),className:"cursor-pointer text-red-600 hover:underline",children:"Delete"})]})})]})},m=()=>{let[e,t]=(0,i.useState)("add"),[a,n]=(0,i.useState)(!1),[d,u]=(0,i.useState)([]),[m,h]=(0,i.useState)([]),[p,f]=(0,i.useState)(!0),[A,x]=(0,i.useState)([]),[b,y]=(0,i.useState)({title:"",description:"",category:"",author:"",authorId:"",authorImg:""}),[v,w]=(0,i.useState)(!1),[N,j]=(0,i.useState)([]),[C,E]=(0,i.useState)(""),[I,k]=(0,i.useState)(!1),[_,T]=(0,i.useState)([]),[B,L]=(0,i.useState)(""),[S,D]=(0,i.useState)(!1),[R,U]=(0,i.useState)(null),[P,M]=(0,i.useState)(null);(0,i.useEffect)(()=>{V(),O(),"manage"===e&&Q()},[e]),(0,i.useEffect)(()=>{M("temp_"+Date.now()+"_"+Math.random().toString(36).substring(2,11))},[]);let V=async()=>{try{let e=await o.Z.get("/api/categories");e.data.success&&e.data.categories.length>0?(u(e.data.categories),y(t=>({...t,category:e.data.categories[0].name}))):(c.toast.error("No categories found. Please add categories in Settings."),u([]))}catch(e){console.error("Error fetching categories:",e),c.toast.error("Failed to load categories"),u([])}finally{f(!1)}},O=async()=>{try{let e=await o.Z.get("/api/authors");e.data.success&&e.data.authors.length>0?(h(e.data.authors),y(t=>({...t,author:e.data.authors[0].name,authorId:e.data.authors[0]._id,authorImg:e.data.authors[0].image||"/author_img.png"}))):(c.toast.error("No authors found. Please add authors in Settings."),h([]))}catch(e){console.error("Error fetching authors:",e),c.toast.error("Failed to load authors"),h([])}},Q=async()=>{try{f(!0);let e=await o.Z.get("/api/blog");x(e.data.blogs||[])}catch(e){console.error("Error fetching blogs:",e),c.toast.error("Failed to load blogs")}finally{f(!1)}},F=e=>{let{name:t,value:a}=e.target;if("authorId"===t){let e=m.find(e=>e._id===a);e&&y({...b,author:e.name,authorId:e._id,authorImg:e.image||"/author_img.png"})}else y({...b,[t]:a})},G=async e=>{if(e.preventDefault(),!a){c.toast.error("Please select a blog thumbnail image");return}if(!b.title.trim()){c.toast.error("Please enter a blog title");return}let s=new FormData;s.append("title",b.title),s.append("description",b.description),s.append("category",b.category),s.append("author",b.author),s.append("authorId",b.authorId),s.append("authorImg",b.authorImg),s.append("image",a),s.append("tempBlogId",P);try{f(!0);let e=await o.Z.post("/api/blog",s);e.data.success?(c.toast.success(e.data.msg||"Blog added successfully"),n(!1),y({title:"",description:"",category:d.length>0?d[0].name:"",author:m.length>0?m[0].name:"",authorId:m.length>0?m[0]._id:"",authorImg:m.length>0&&m[0].image||"/author_img.png"}),M("temp_"+Date.now()+"_"+Math.random().toString(36).substring(2,11)),T([]),t("manage")):c.toast.error(e.data.msg||"Error adding blog")}catch(e){console.error("Error submitting blog:",e),c.toast.error("Failed to add blog")}finally{f(!1)}},H=async e=>{if(window.confirm("Are you sure you want to delete this blog?"))try{f(!0);let t=await o.Z.delete("/api/blog",{params:{id:e}});c.toast.success(t.data.msg||"Blog deleted successfully"),Q()}catch(e){console.error("Error deleting blog:",e),c.toast.error("Failed to delete blog")}finally{f(!1)}},W=async()=>{try{let e=await o.Z.get("/api/blog");j(e.data.blogs||[])}catch(e){console.error("Error fetching blogs:",e)}},Y=(e,t)=>{let a="[[".concat(e,"|").concat(t,"]]"),s=document.getElementById("blog-description"),r=s.selectionStart,o=b.description.substring(0,r),n=b.description.substring(r);y({...b,description:o+a+n}),w(!1),setTimeout(()=>{s.focus(),s.setSelectionRange(r+a.length,r+a.length)},100)},z=async()=>{if(P)try{let e=await o.Z.get("/api/images",{params:{blogId:P,limit:50}});e.data.success&&T(e.data.images)}catch(e){console.error("Error fetching images:",e),c.toast.error("Failed to fetch images")}},q=async()=>{if(!R){c.toast.error("Please select an image file");return}D(!0);try{let e=new FormData;e.append("image",R),e.append("blogId",P||"new");let t=await o.Z.post("/api/upload/image",e);t.data.success?(c.toast.success("Image uploaded successfully"),U(null),await z()):c.toast.error(t.data.message||"Failed to upload image")}catch(e){console.error("Error uploading image:",e),c.toast.error("Failed to upload image")}finally{D(!1)}},K=async(e,t)=>{if(window.confirm("Are you sure you want to delete this image? This action cannot be undone."))try{let t=await o.Z.delete("/api/images/".concat(e));t.data.success?(c.toast.success("Image deleted successfully"),await z()):c.toast.error(t.data.message||"Failed to delete image")}catch(e){console.error("Error deleting image:",e),c.toast.error("Failed to delete image")}},Z=(e,t)=>{let a="{{image:".concat(e,"|").concat(t,"}}"),s=document.getElementById("blog-description"),r=s.selectionStart,o=b.description.substring(0,r),n=b.description.substring(r);y({...b,description:o+a+n}),k(!1),setTimeout(()=>{s.focus(),s.setSelectionRange(r+a.length,r+a.length)},100)};return(0,s.jsxs)("div",{className:"pt-5 px-5 sm:pt-12 sm:pl-16",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Blog Management"}),(0,s.jsxs)("div",{className:"flex border-b border-gray-300 mb-6",children:[(0,s.jsx)("button",{className:"py-3 px-6 font-medium rounded-t-lg ".concat("add"===e?"bg-black text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),onClick:()=>t("add"),children:"Add New Blog"}),(0,s.jsx)("button",{className:"py-3 px-6 font-medium rounded-t-lg ml-2 ".concat("manage"===e?"bg-black text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),onClick:()=>t("manage"),children:"Manage Blogs"})]}),"add"===e&&(0,s.jsx)("form",{onSubmit:G,className:"max-w-[800px]",children:(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Create New Blog Post"}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("p",{className:"text-sm font-medium mb-2",children:["Upload Thumbnail ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("label",{htmlFor:"image",className:"cursor-pointer block",children:(0,s.jsx)(l(),{className:"border border-gray-300 rounded-md",src:a?URL.createObjectURL(a):r.L.upload_area,width:200,height:120,alt:"",style:{objectFit:"cover",height:"120px"}})}),(0,s.jsx)("input",{onChange:e=>n(e.target.files[0]),type:"file",id:"image",hidden:!0,required:!0})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium mb-2",children:["Blog Title ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{name:"title",onChange:F,value:b.title,className:"w-full px-4 py-3 border rounded-md",type:"text",placeholder:"Type here",required:!0})]}),(0,s.jsxs)("div",{className:"mb-4 relative",children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Blog Description"}),(0,s.jsx)("div",{className:"flex items-start",children:(0,s.jsx)("textarea",{id:"blog-description",name:"description",onChange:F,value:b.description,className:"w-full px-4 py-3 border rounded-md",placeholder:"Write content here",rows:6,required:!0})}),(0,s.jsxs)("div",{className:"mt-2 flex items-center flex-wrap gap-4",children:[(0,s.jsxs)("button",{type:"button",onClick:()=>{W(),w(!0)},className:"text-sm flex items-center text-blue-600 hover:text-blue-800",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101"})}),"Mention another blog"]}),(0,s.jsxs)("button",{type:"button",onClick:()=>{z(),k(!0)},className:"text-sm flex items-center text-green-600 hover:text-green-800",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),"Insert image"]}),(0,s.jsx)("div",{className:"text-xs text-gray-500",children:(0,s.jsxs)("span",{children:["Formats: [[blogId|blogTitle]] | ","{{image:url|filename}}"]})})]}),v&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Select a blog to mention"}),(0,s.jsx)("button",{onClick:()=>w(!1),className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsx)("input",{type:"text",placeholder:"Search blogs...",className:"w-full px-4 py-2 border rounded-md mb-4",value:C,onChange:e=>E(e.target.value)}),(0,s.jsx)("div",{className:"divide-y",children:N.filter(e=>e.title.toLowerCase().includes(C.toLowerCase())).map(e=>(0,s.jsxs)("div",{className:"py-3 px-2 hover:bg-gray-100 cursor-pointer flex items-center",onClick:()=>Y(e._id,e.title),children:[(0,s.jsx)("div",{className:"w-12 h-12 relative mr-3",children:(0,s.jsx)(l(),{src:e.image,alt:e.title,fill:!0,className:"object-cover rounded"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:e.category})]})]},e._id))})]})}),I&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Insert Image"}),(0,s.jsx)("button",{onClick:()=>k(!1),className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsxs)("div",{className:"mb-6 p-4 border rounded-lg bg-gray-50",children:[(0,s.jsx)("h4",{className:"font-medium mb-3",children:"Upload New Image"}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("input",{type:"file",accept:"image/*",onChange:e=>U(e.target.files[0]),className:"flex-1"}),(0,s.jsx)("button",{type:"button",onClick:q,disabled:!R||S,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50",children:S?"Uploading...":"Upload"})]})]}),(0,s.jsx)("input",{type:"text",placeholder:"Search images...",className:"w-full px-4 py-2 border rounded-md mb-4",value:B,onChange:e=>L(e.target.value)}),(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:_.filter(e=>e.filename.toLowerCase().includes(B.toLowerCase())).map(e=>(0,s.jsxs)("div",{className:"border rounded-lg p-2 hover:bg-gray-100 cursor-pointer relative group",children:[(0,s.jsxs)("div",{className:"aspect-square relative mb-2",onClick:()=>Z(e.url,e.filename),children:[(0,s.jsx)(l(),{src:e.url,alt:e.filename,fill:!0,className:"object-cover rounded"}),(0,s.jsx)("button",{onClick:t=>{t.stopPropagation(),K(e._id,e.url)},className:"absolute top-1 right-1 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",title:"Delete image",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsxs)("div",{onClick:()=>Z(e.url,e.filename),children:[(0,s.jsx)("p",{className:"text-xs text-gray-600 truncate",children:e.filename}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:new Date(e.uploadDate).toLocaleDateString()})]})]},e._id))}),0===_.length&&(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No images found. Upload your first image above."})]})})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Blog Category"}),p?(0,s.jsx)("p",{children:"Loading categories..."}):d.length>0?(0,s.jsx)("select",{name:"category",onChange:F,value:b.category,className:"w-full px-4 py-3 border rounded-md text-gray-700",required:!0,children:d.map(e=>(0,s.jsx)("option",{value:e.name,children:e.name},e._id))}):(0,s.jsx)("p",{className:"text-red-500",children:"No categories available. Please add categories in Settings."})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Blog Author"}),p?(0,s.jsx)("p",{children:"Loading authors..."}):m.length>0?(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("select",{name:"authorId",onChange:F,value:b.authorId,className:"w-full px-4 py-3 border rounded-md text-gray-700",required:!0,children:m.map(e=>(0,s.jsx)("option",{value:e._id,children:e.name},e._id))}),b.authorId&&(0,s.jsx)("div",{className:"flex items-center gap-2",children:(0,s.jsx)("img",{src:b.authorImg,alt:b.author,className:"w-10 h-10 rounded-full object-cover border border-gray-200"})})]}):(0,s.jsx)("p",{className:"text-red-500",children:"No authors available. Please add authors in Settings."})]}),(0,s.jsx)("button",{type:"submit",className:"w-full py-3 bg-black text-white rounded-md hover:bg-gray-800 transition-colors",disabled:p||0===d.length||0===m.length,children:p?"Creating...":"Create Blog Post"})]})}),"manage"===e&&(0,s.jsxs)("div",{className:"max-w-[850px] bg-white p-6 rounded-lg shadow-md",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Manage Blog Posts"}),p?(0,s.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-black"}),(0,s.jsx)("span",{className:"ml-2",children:"Loading blogs..."})]}):A.length>0?(0,s.jsx)("div",{className:"relative overflow-x-auto border border-gray-300 rounded-lg",children:(0,s.jsxs)("table",{className:"w-full text-sm text-gray-500 table-fixed",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Title"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Author"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{children:A.map((e,t)=>(0,s.jsx)(g,{mongoId:e._id,title:e.title,author:e.author,authorImg:e.authorImg,date:e.date,deleteBlog:H},t))})]})}):(0,s.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,s.jsx)("p",{children:"No blogs found."}),(0,s.jsx)("button",{onClick:()=>t("add"),className:"mt-4 text-blue-600 hover:underline",children:"Add your first blog"})]}),A.length>0&&(0,s.jsx)("div",{className:"mt-4 text-sm text-gray-500",children:(0,s.jsxs)("p",{children:["Showing ",A.length," blog posts"]})})]})]})}},7948:function(e,t,a){"use strict";a.r(t),a.d(t,{Bounce:function(){return R},Flip:function(){return M},Icons:function(){return L},Slide:function(){return U},ToastContainer:function(){return O},Zoom:function(){return P},collapseToast:function(){return d},cssTransition:function(){return u},toast:function(){return _},useToast:function(){return w},useToastContainer:function(){return v}});var s=a(2265),r=function(){for(var e,t,a=0,s="",r=arguments.length;a<r;a++)(e=arguments[a])&&(t=function e(t){var a,s,r="";if("string"==typeof t||"number"==typeof t)r+=t;else if("object"==typeof t){if(Array.isArray(t)){var o=t.length;for(a=0;a<o;a++)t[a]&&(s=e(t[a]))&&(r&&(r+=" "),r+=s)}else for(s in t)t[s]&&(r&&(r+=" "),r+=s)}return r}(e))&&(s&&(s+=" "),s+=t);return s};let o=e=>"number"==typeof e&&!isNaN(e),n=e=>"string"==typeof e,l=e=>"function"==typeof e,i=e=>n(e)||l(e)?e:null,c=e=>(0,s.isValidElement)(e)||n(e)||l(e)||o(e);function d(e,t,a){void 0===a&&(a=300);let{scrollHeight:s,style:r}=e;requestAnimationFrame(()=>{r.minHeight="initial",r.height=s+"px",r.transition=`all ${a}ms`,requestAnimationFrame(()=>{r.height="0",r.padding="0",r.margin="0",setTimeout(t,a)})})}function u(e){let{enter:t,exit:a,appendPosition:r=!1,collapse:o=!0,collapseDuration:n=300}=e;return function(e){let{children:l,position:i,preventExitTransition:c,done:u,nodeRef:g,isIn:m,playToast:h}=e,p=r?`${t}--${i}`:t,f=r?`${a}--${i}`:a,A=(0,s.useRef)(0);return(0,s.useLayoutEffect)(()=>{let e=g.current,t=p.split(" "),a=s=>{s.target===g.current&&(h(),e.removeEventListener("animationend",a),e.removeEventListener("animationcancel",a),0===A.current&&"animationcancel"!==s.type&&e.classList.remove(...t))};e.classList.add(...t),e.addEventListener("animationend",a),e.addEventListener("animationcancel",a)},[]),(0,s.useEffect)(()=>{let e=g.current,t=()=>{e.removeEventListener("animationend",t),o?d(e,u,n):u()};m||(c?t():(A.current=1,e.className+=` ${f}`,e.addEventListener("animationend",t)))},[m]),s.createElement(s.Fragment,null,l)}}function g(e,t){return null!=e?{content:e.content,containerId:e.props.containerId,id:e.props.toastId,theme:e.props.theme,type:e.props.type,data:e.props.data||{},isLoading:e.props.isLoading,icon:e.props.icon,status:t}:{}}let m=new Map,h=[],p=new Set,f=e=>p.forEach(t=>t(e)),A=()=>m.size>0;function x(e,t){var a;if(t)return!(null==(a=m.get(t))||!a.isToastActive(e));let s=!1;return m.forEach(t=>{t.isToastActive(e)&&(s=!0)}),s}function b(e,t){c(e)&&(A()||h.push({content:e,options:t}),m.forEach(a=>{a.buildToast(e,t)}))}function y(e,t){m.forEach(a=>{null!=t&&null!=t&&t.containerId?(null==t?void 0:t.containerId)===a.id&&a.toggle(e,null==t?void 0:t.id):a.toggle(e,null==t?void 0:t.id)})}function v(e){let{subscribe:t,getSnapshot:a,setProps:r}=(0,s.useRef)(function(e){let t=e.containerId||1;return{subscribe(a){let r=function(e,t,a){let r=1,d=0,u=[],m=[],h=[],p=t,f=new Map,A=new Set,x=()=>{h=Array.from(f.values()),A.forEach(e=>e())},b=e=>{m=null==e?[]:m.filter(t=>t!==e),x()},y=e=>{let{toastId:t,onOpen:r,updateId:o,children:n}=e.props,i=null==o;e.staleId&&f.delete(e.staleId),f.set(t,e),m=[...m,e.props.toastId].filter(t=>t!==e.staleId),x(),a(g(e,i?"added":"updated")),i&&l(r)&&r((0,s.isValidElement)(n)&&n.props)};return{id:e,props:p,observe:e=>(A.add(e),()=>A.delete(e)),toggle:(e,t)=>{f.forEach(a=>{null!=t&&t!==a.props.toastId||l(a.toggle)&&a.toggle(e)})},removeToast:b,toasts:f,clearQueue:()=>{d-=u.length,u=[]},buildToast:(t,m)=>{var h,A;if((t=>{let{containerId:a,toastId:s,updateId:r}=t,o=f.has(s)&&null==r;return(a?a!==e:1!==e)||o})(m))return;let{toastId:v,updateId:w,data:N,staleId:j,delay:C}=m,E=()=>{b(v)},I=null==w;I&&d++;let k={...p,style:p.toastStyle,key:r++,...Object.fromEntries(Object.entries(m).filter(e=>{let[t,a]=e;return null!=a})),toastId:v,updateId:w,data:N,closeToast:E,isIn:!1,className:i(m.className||p.toastClassName),bodyClassName:i(m.bodyClassName||p.bodyClassName),progressClassName:i(m.progressClassName||p.progressClassName),autoClose:!m.isLoading&&(h=m.autoClose,A=p.autoClose,!1===h||o(h)&&h>0?h:A),deleteToast(){let e=f.get(v),{onClose:t,children:r}=e.props;l(t)&&t((0,s.isValidElement)(r)&&r.props),a(g(e,"removed")),f.delete(v),--d<0&&(d=0),u.length>0?y(u.shift()):x()}};k.closeButton=p.closeButton,!1===m.closeButton||c(m.closeButton)?k.closeButton=m.closeButton:!0===m.closeButton&&(k.closeButton=!c(p.closeButton)||p.closeButton);let _=t;(0,s.isValidElement)(t)&&!n(t.type)?_=(0,s.cloneElement)(t,{closeToast:E,toastProps:k,data:N}):l(t)&&(_=t({closeToast:E,toastProps:k,data:N}));let T={content:_,props:k,staleId:j};p.limit&&p.limit>0&&d>p.limit&&I?u.push(T):o(C)?setTimeout(()=>{y(T)},C):y(T)},setProps(e){p=e},setToggle:(e,t)=>{f.get(e).toggle=t},isToastActive:e=>m.some(t=>t===e),getSnapshot:()=>p.newestOnTop?h.reverse():h}}(t,e,f);m.set(t,r);let d=r.observe(a);return h.forEach(e=>b(e.content,e.options)),h=[],()=>{d(),m.delete(t)}},setProps(e){var a;null==(a=m.get(t))||a.setProps(e)},getSnapshot(){var e;return null==(e=m.get(t))?void 0:e.getSnapshot()}}}(e)).current;r(e);let d=(0,s.useSyncExternalStore)(t,a,a);return{getToastToRender:function(e){if(!d)return[];let t=new Map;return d.forEach(e=>{let{position:a}=e.props;t.has(a)||t.set(a,[]),t.get(a).push(e)}),Array.from(t,t=>e(t[0],t[1]))},isToastActive:x,count:null==d?void 0:d.length}}function w(e){var t,a;let[r,o]=(0,s.useState)(!1),[n,l]=(0,s.useState)(!1),i=(0,s.useRef)(null),c=(0,s.useRef)({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:d,pauseOnHover:u,closeToast:g,onClick:h,closeOnClick:p}=e;function f(){o(!0)}function A(){o(!1)}function x(t){let a=i.current;c.canDrag&&a&&(c.didMove=!0,r&&A(),c.delta="x"===e.draggableDirection?t.clientX-c.start:t.clientY-c.start,c.start!==t.clientX&&(c.canCloseOnClick=!1),a.style.transform=`translate3d(${"x"===e.draggableDirection?`${c.delta}px, var(--y)`:`0, calc(${c.delta}px + var(--y))`},0)`,a.style.opacity=""+(1-Math.abs(c.delta/c.removalDistance)))}function b(){document.removeEventListener("pointermove",x),document.removeEventListener("pointerup",b);let t=i.current;if(c.canDrag&&c.didMove&&t){if(c.canDrag=!1,Math.abs(c.delta)>c.removalDistance)return l(!0),e.closeToast(),void e.collapseAll();t.style.transition="transform 0.2s, opacity 0.2s",t.style.removeProperty("transform"),t.style.removeProperty("opacity")}}null==(a=m.get((t={id:e.toastId,containerId:e.containerId,fn:o}).containerId||1))||a.setToggle(t.id,t.fn),(0,s.useEffect)(()=>{if(e.pauseOnFocusLoss)return document.hasFocus()||A(),window.addEventListener("focus",f),window.addEventListener("blur",A),()=>{window.removeEventListener("focus",f),window.removeEventListener("blur",A)}},[e.pauseOnFocusLoss]);let y={onPointerDown:function(t){if(!0===e.draggable||e.draggable===t.pointerType){c.didMove=!1,document.addEventListener("pointermove",x),document.addEventListener("pointerup",b);let a=i.current;c.canCloseOnClick=!0,c.canDrag=!0,a.style.transition="none","x"===e.draggableDirection?(c.start=t.clientX,c.removalDistance=a.offsetWidth*(e.draggablePercent/100)):(c.start=t.clientY,c.removalDistance=a.offsetHeight*(80===e.draggablePercent?1.5*e.draggablePercent:e.draggablePercent)/100)}},onPointerUp:function(t){let{top:a,bottom:s,left:r,right:o}=i.current.getBoundingClientRect();"touchend"!==t.nativeEvent.type&&e.pauseOnHover&&t.clientX>=r&&t.clientX<=o&&t.clientY>=a&&t.clientY<=s?A():f()}};return d&&u&&(y.onMouseEnter=A,e.stacked||(y.onMouseLeave=f)),p&&(y.onClick=e=>{h&&h(e),c.canCloseOnClick&&g()}),{playToast:f,pauseToast:A,isRunning:r,preventExitTransition:n,toastRef:i,eventHandlers:y}}function N(e){let{delay:t,isRunning:a,closeToast:o,type:n="default",hide:i,className:c,style:d,controlledProgress:u,progress:g,rtl:m,isIn:h,theme:p}=e,f=i||u&&0===g,A={...d,animationDuration:`${t}ms`,animationPlayState:a?"running":"paused"};u&&(A.transform=`scaleX(${g})`);let x=r("Toastify__progress-bar",u?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${p}`,`Toastify__progress-bar--${n}`,{"Toastify__progress-bar--rtl":m}),b=l(c)?c({rtl:m,type:n,defaultClassName:x}):r(x,c);return s.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":f},s.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${p} Toastify__progress-bar--${n}`}),s.createElement("div",{role:"progressbar","aria-hidden":f?"true":"false","aria-label":"notification timer",className:b,style:A,[u&&g>=1?"onTransitionEnd":"onAnimationEnd"]:u&&g<1?null:()=>{h&&o()}}))}let j=1,C=()=>""+j++;function E(e,t){return b(e,t),t.toastId}function I(e,t){return{...t,type:t&&t.type||e,toastId:t&&(n(t.toastId)||o(t.toastId))?t.toastId:C()}}function k(e){return(t,a)=>E(t,I(e,a))}function _(e,t){return E(e,I("default",t))}_.loading=(e,t)=>E(e,I("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...t})),_.promise=function(e,t,a){let s,{pending:r,error:o,success:i}=t;r&&(s=n(r)?_.loading(r,a):_.loading(r.render,{...a,...r}));let c={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},d=(e,t,r)=>{if(null==t)return void _.dismiss(s);let o={type:e,...c,...a,data:r},l=n(t)?{render:t}:t;return s?_.update(s,{...o,...l}):_(l.render,{...o,...l}),r},u=l(e)?e():e;return u.then(e=>d("success",i,e)).catch(e=>d("error",o,e)),u},_.success=k("success"),_.info=k("info"),_.error=k("error"),_.warning=k("warning"),_.warn=_.warning,_.dark=(e,t)=>E(e,I("default",{theme:"dark",...t})),_.dismiss=function(e){var t,a;A()?null==e||n(t=e)||o(t)?m.forEach(t=>{t.removeToast(e)}):e&&("containerId"in e||"id"in e)&&((null==(a=m.get(e.containerId))?void 0:a.removeToast(e.id))||m.forEach(t=>{t.removeToast(e.id)})):h=h.filter(t=>null!=e&&t.options.toastId!==e)},_.clearWaitingQueue=function(e){void 0===e&&(e={}),m.forEach(t=>{!t.props.limit||e.containerId&&t.id!==e.containerId||t.clearQueue()})},_.isActive=x,_.update=function(e,t){void 0===t&&(t={});let a=((e,t)=>{var a;let{containerId:s}=t;return null==(a=m.get(s||1))?void 0:a.toasts.get(e)})(e,t);if(a){let{props:s,content:r}=a,o={delay:100,...s,...t,toastId:t.toastId||e,updateId:C()};o.toastId!==e&&(o.staleId=e);let n=o.render||r;delete o.render,E(n,o)}},_.done=e=>{_.update(e,{progress:1})},_.onChange=function(e){return p.add(e),()=>{p.delete(e)}},_.play=e=>y(!0,e),_.pause=e=>y(!1,e);let T="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,B=e=>{let{theme:t,type:a,isLoading:r,...o}=e;return s.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:"colored"===t?"currentColor":`var(--toastify-icon-color-${a})`,...o})},L={info:function(e){return s.createElement(B,{...e},s.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))},warning:function(e){return s.createElement(B,{...e},s.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))},success:function(e){return s.createElement(B,{...e},s.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))},error:function(e){return s.createElement(B,{...e},s.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))},spinner:function(){return s.createElement("div",{className:"Toastify__spinner"})}},S=e=>{let{isRunning:t,preventExitTransition:a,toastRef:o,eventHandlers:n,playToast:i}=w(e),{closeButton:c,children:d,autoClose:u,onClick:g,type:m,hideProgressBar:h,closeToast:p,transition:f,position:A,className:x,style:b,bodyClassName:y,bodyStyle:v,progressClassName:j,progressStyle:C,updateId:E,role:I,progress:k,rtl:_,toastId:T,deleteToast:B,isIn:S,isLoading:D,closeOnClick:R,theme:U}=e,P=r("Toastify__toast",`Toastify__toast-theme--${U}`,`Toastify__toast--${m}`,{"Toastify__toast--rtl":_},{"Toastify__toast--close-on-click":R}),M=l(x)?x({rtl:_,position:A,type:m,defaultClassName:P}):r(P,x),V=function(e){let{theme:t,type:a,isLoading:r,icon:o}=e,n=null,i={theme:t,type:a,isLoading:r};return!1===o||(l(o)?n=o(i):(0,s.isValidElement)(o)?n=(0,s.cloneElement)(o,i):r?n=L.spinner():a in L&&(n=L[a](i))),n}(e),O=!!k||!u,Q={closeToast:p,type:m,theme:U},F=null;return!1===c||(F=l(c)?c(Q):(0,s.isValidElement)(c)?(0,s.cloneElement)(c,Q):function(e){let{closeToast:t,theme:a,ariaLabel:r="close"}=e;return s.createElement("button",{className:`Toastify__close-button Toastify__close-button--${a}`,type:"button",onClick:e=>{e.stopPropagation(),t(e)},"aria-label":r},s.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},s.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}(Q)),s.createElement(f,{isIn:S,done:B,position:A,preventExitTransition:a,nodeRef:o,playToast:i},s.createElement("div",{id:T,onClick:g,"data-in":S,className:M,...n,style:b,ref:o},s.createElement("div",{...S&&{role:I},className:l(y)?y({type:m}):r("Toastify__toast-body",y),style:v},null!=V&&s.createElement("div",{className:r("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!D})},V),s.createElement("div",null,d)),F,s.createElement(N,{...E&&!O?{key:`pb-${E}`}:{},rtl:_,theme:U,delay:u,isRunning:t,isIn:S,closeToast:p,hide:h,type:m,style:C,className:j,controlledProgress:O,progress:k||0})))},D=function(e,t){return void 0===t&&(t=!1),{enter:`Toastify--animate Toastify__${e}-enter`,exit:`Toastify--animate Toastify__${e}-exit`,appendPosition:t}},R=u(D("bounce",!0)),U=u(D("slide",!0)),P=u(D("zoom")),M=u(D("flip")),V={position:"top-right",transition:R,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light"};function O(e){let t={...V,...e},a=e.stacked,[o,n]=(0,s.useState)(!0),c=(0,s.useRef)(null),{getToastToRender:d,isToastActive:u,count:g}=v(t),{className:m,style:h,rtl:p,containerId:f}=t;function A(){a&&(n(!0),_.play())}return T(()=>{if(a){var e;let a=c.current.querySelectorAll('[data-in="true"]'),s=null==(e=t.position)?void 0:e.includes("top"),r=0,n=0;Array.from(a).reverse().forEach((e,t)=>{e.classList.add("Toastify__toast--stacked"),t>0&&(e.dataset.collapsed=`${o}`),e.dataset.pos||(e.dataset.pos=s?"top":"bot");let a=r*(o?.2:1)+(o?0:12*t);e.style.setProperty("--y",`${s?a:-1*a}px`),e.style.setProperty("--g","12"),e.style.setProperty("--s",""+(1-(o?n:0))),r+=e.offsetHeight,n+=.025})}},[o,g,a]),s.createElement("div",{ref:c,className:"Toastify",id:f,onMouseEnter:()=>{a&&(n(!1),_.pause())},onMouseLeave:A},d((e,t)=>{let o=t.length?{...h}:{...h,pointerEvents:"none"};return s.createElement("div",{className:function(e){let t=r("Toastify__toast-container",`Toastify__toast-container--${e}`,{"Toastify__toast-container--rtl":p});return l(m)?m({position:e,rtl:p,defaultClassName:t}):r(t,i(m))}(e),style:o,key:`container-${e}`},t.map(e=>{let{content:t,props:r}=e;return s.createElement(S,{...r,stacked:a,collapseAll:A,isIn:u(r.toastId,r.containerId),style:r.style,key:`toast-${r.key}`},t)}))}))}}},function(e){e.O(0,[580,691,396,971,938,744],function(){return e(e.s=248)}),_N_E=e.O()}]);
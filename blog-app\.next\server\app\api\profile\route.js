"use strict";(()=>{var e={};e.id=442,e.ids=[442],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},57147:e=>{e.exports=require("fs")},73292:e=>{e.exports=require("fs/promises")},18214:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>S,originalPathname:()=>w,patchFetch:()=>b,requestAsyncStorage:()=>y,routeModule:()=>g,serverHooks:()=>h,staticGenerationAsyncStorage:()=>P,staticGenerationBailout:()=>v});var s={};t.r(s),t.d(s,{GET:()=>f,PUT:()=>m});var o=t(95419),i=t(69108),a=t(99678),n=t(91887),u=t(96488),l=t(78070),c=t(73292),p=t(57147),d=t.n(p);async function f(e){try{let r=e.nextUrl.searchParams.get("userId");if(!r)return l.Z.json({success:!1,message:"User ID is required"},{status:400});let t=await u.Z.findById(r,{password:0});if(!t)return l.Z.json({success:!1,message:"User not found"},{status:404});return l.Z.json({success:!0,user:{id:t._id,email:t.email,role:t.role,profilePicture:t.profilePicture,name:t.name}})}catch(e){return console.error("Profile fetch error:",e),l.Z.json({success:!1,message:"Server error"},{status:500})}}async function m(e){try{let r=await e.formData(),t=r.get("userId");if(!t)return l.Z.json({success:!1,message:"User ID is required"},{status:400});let s=await u.Z.findById(t);if(!s)return l.Z.json({success:!1,message:"User not found"},{status:404});let o=r.get("name");o&&(s.name=o);let i=r.get("profilePicture");if(i&&i.size>0){let e=await i.arrayBuffer(),r=Buffer.from(e),t=Date.now(),o=i.name.replace(/\s+/g,"_"),a=`./public/profiles/${t}_${o}`;d().existsSync("./public/profiles")||d().mkdirSync("./public/profiles",{recursive:!0}),await (0,c.writeFile)(a,r);let n=`/profiles/${t}_${o}`;if(s.profilePicture&&"/default_profile.png"!==s.profilePicture)try{d().unlink(`./public${s.profilePicture}`,()=>{})}catch(e){console.error("Error deleting old profile picture:",e)}s.profilePicture=n}return await s.save(),l.Z.json({success:!0,message:"Profile updated successfully",user:{id:s._id,email:s.email,role:s.role,profilePicture:s.profilePicture,name:s.name}})}catch(e){return console.error("Profile update error:",e),l.Z.json({success:!1,message:"Server error"},{status:500})}}(async()=>{await (0,n.n)()})();let g=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/profile/route",pathname:"/api/profile",filename:"route",bundlePath:"app/api/profile/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\profile\\route.js",nextConfigOutput:"",userland:s}),{requestAsyncStorage:y,staticGenerationAsyncStorage:P,serverHooks:h,headerHooks:S,staticGenerationBailout:v}=g,w="/api/profile/route";function b(){return(0,a.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:P})}},91887:(e,r,t)=>{t.d(r,{n:()=>i});var s=t(11185),o=t.n(s);let i=async()=>{try{if(o().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await o().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},96488:(e,r,t)=>{t.d(r,{Z:()=>a});var s=t(11185),o=t.n(s);let i=new(o()).Schema({email:{type:String,required:!0,unique:!0},password:{type:String,required:!0},role:{type:String,default:"user",enum:["user","admin"]},profilePicture:{type:String,default:"/default_profile.png"},name:{type:String,default:""},date:{type:Date,default:Date.now()}}),a=o().models.user||o().model("user",i)},78070:(e,r,t)=>{Object.defineProperty(r,"Z",{enumerable:!0,get:function(){return s.NextResponse}});let s=t(70457)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,2993],()=>t(18214));module.exports=s})();
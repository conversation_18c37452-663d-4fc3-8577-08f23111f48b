(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[57],{8699:function(e,t,n){Promise.resolve().then(n.bind(n,6254))},6254:function(e,t,n){"use strict";n.r(t);var o=n(7437),a=n(4257),s=n(3637),r=n(2173),i=n(6691),l=n.n(i),c=n(1396),d=n.n(c),u=n(4033),p=n(2265),f=n(7948),m=n(8347);t.default=()=>{let[e,t]=(0,p.useState)([]),[n,i]=(0,p.useState)(!0),[c,g]=(0,p.useState)(!1),h=(0,u.useRouter)();(0,p.useEffect)(()=>{if(!localStorage.getItem("authToken")){h.push("/");return}g(!0),y()},[h]);let y=async()=>{try{i(!0);let e=localStorage.getItem("authToken"),n=await r.Z.get("/api/favorites",{headers:{Authorization:"Bearer ".concat(e)}});n.data.success?t(n.data.favorites):f.toast.error("Failed to load favorites")}catch(e){console.error("Error fetching favorites:",e),f.toast.error("Failed to load favorites")}finally{i(!1)}},v=async n=>{try{let o=localStorage.getItem("authToken");(await r.Z.delete("/api/favorites?blogId=".concat(n),{headers:{Authorization:"Bearer ".concat(o)}})).data.success?(t(e.filter(e=>e._id.toString()!==n)),f.toast.success("Removed from favorites")):f.toast.error("Failed to remove from favorites")}catch(e){console.error("Error removing favorite:",e),f.toast.error("Failed to remove from favorites")}};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("div",{className:"bg-gray-200 py-5 px-5 md:px-12 lg:px-28",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)(d(),{href:"/",children:(0,o.jsx)(l(),{src:a.L.logo,width:180,alt:"",className:"w-[130px] sm:w-auto"})}),(0,o.jsx)(d(),{href:"/profile",children:(0,o.jsx)("button",{className:"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]",children:"Back to Profile"})})]}),(0,o.jsxs)("div",{className:"text-center my-16",children:[(0,o.jsx)("h1",{className:"text-3xl sm:text-5xl font-semibold",children:"My Favorite Blogs"}),(0,o.jsx)("p",{className:"mt-4 text-gray-600",children:"Your collection of starred articles"})]})]}),(0,o.jsx)("div",{className:"container mx-auto px-4 py-8 mb-16",children:n?(0,o.jsx)("div",{className:"flex justify-center items-center py-16",children:(0,o.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-black"})}):e.length>0?(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.map(e=>(0,o.jsxs)("div",{className:"bg-white rounded-lg overflow-hidden shadow-md border border-gray-200",children:[(0,o.jsx)("div",{className:"relative h-48",children:(0,o.jsx)(l(),{src:e.image,alt:e.title,fill:!0,className:"object-cover"})}),(0,o.jsxs)("div",{className:"p-6",children:[(0,o.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,o.jsx)("span",{className:"inline-block px-3 py-1 text-sm bg-gray-100 rounded-full",children:e.category}),(0,o.jsx)("button",{onClick:()=>v(e._id),className:"text-yellow-500 hover:text-yellow-700",children:(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,o.jsx)("polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"})})})]}),(0,o.jsx)("h2",{className:"text-xl font-semibold mb-3 line-clamp-2",children:e.title}),(0,o.jsx)("div",{className:"text-gray-600 mb-4 line-clamp-3",dangerouslySetInnerHTML:{__html:(0,m.A)(e.description,150)}}),(0,o.jsx)(d(),{href:"/blogs/".concat(e._id),className:"inline-block px-4 py-2 bg-black text-white rounded hover:bg-gray-800 transition",children:"Read Article"})]})]},e._id))}):(0,o.jsxs)("div",{className:"text-center py-16",children:[(0,o.jsx)("div",{className:"text-5xl mb-4",children:"⭐"}),(0,o.jsx)("h2",{className:"text-2xl font-semibold mb-2",children:"No favorites yet"}),(0,o.jsx)("p",{className:"text-gray-600 mb-6",children:"Start adding blogs to your favorites by clicking the star icon on blog posts."}),(0,o.jsx)(d(),{href:"/",className:"inline-block px-6 py-3 bg-black text-white rounded-md hover:bg-gray-800 transition",children:"Browse Blogs"})]})}),(0,o.jsx)(s.Z,{})]})}},8347:function(e,t,n){"use strict";n.d(t,{A:function(){return a},D:function(){return o}});let o=e=>{if(!e)return"";let t=e;return(t=(t=t.replace(/\{\{image:[^}]+\}\}/g,"")).replace(/\[\[[^\]]+\]\]/g,"")).replace(/\s+/g," ").trim()},a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:120,n=o(e);return n.length>t?n.substring(0,t)+"...":n}},4033:function(e,t,n){e.exports=n(5313)},7948:function(e,t,n){"use strict";n.r(t),n.d(t,{Bounce:function(){return S},Flip:function(){return R},Icons:function(){return $},Slide:function(){return M},ToastContainer:function(){return z},Zoom:function(){return O},collapseToast:function(){return d},cssTransition:function(){return u},toast:function(){return L},useToast:function(){return T},useToastContainer:function(){return E}});var o=n(2265),a=function(){for(var e,t,n=0,o="",a=arguments.length;n<a;n++)(e=arguments[n])&&(t=function e(t){var n,o,a="";if("string"==typeof t||"number"==typeof t)a+=t;else if("object"==typeof t){if(Array.isArray(t)){var s=t.length;for(n=0;n<s;n++)t[n]&&(o=e(t[n]))&&(a&&(a+=" "),a+=o)}else for(o in t)t[o]&&(a&&(a+=" "),a+=o)}return a}(e))&&(o&&(o+=" "),o+=t);return o};let s=e=>"number"==typeof e&&!isNaN(e),r=e=>"string"==typeof e,i=e=>"function"==typeof e,l=e=>r(e)||i(e)?e:null,c=e=>(0,o.isValidElement)(e)||r(e)||i(e)||s(e);function d(e,t,n){void 0===n&&(n=300);let{scrollHeight:o,style:a}=e;requestAnimationFrame(()=>{a.minHeight="initial",a.height=o+"px",a.transition=`all ${n}ms`,requestAnimationFrame(()=>{a.height="0",a.padding="0",a.margin="0",setTimeout(t,n)})})}function u(e){let{enter:t,exit:n,appendPosition:a=!1,collapse:s=!0,collapseDuration:r=300}=e;return function(e){let{children:i,position:l,preventExitTransition:c,done:u,nodeRef:p,isIn:f,playToast:m}=e,g=a?`${t}--${l}`:t,h=a?`${n}--${l}`:n,y=(0,o.useRef)(0);return(0,o.useLayoutEffect)(()=>{let e=p.current,t=g.split(" "),n=o=>{o.target===p.current&&(m(),e.removeEventListener("animationend",n),e.removeEventListener("animationcancel",n),0===y.current&&"animationcancel"!==o.type&&e.classList.remove(...t))};e.classList.add(...t),e.addEventListener("animationend",n),e.addEventListener("animationcancel",n)},[]),(0,o.useEffect)(()=>{let e=p.current,t=()=>{e.removeEventListener("animationend",t),s?d(e,u,r):u()};f||(c?t():(y.current=1,e.className+=` ${h}`,e.addEventListener("animationend",t)))},[f]),o.createElement(o.Fragment,null,i)}}function p(e,t){return null!=e?{content:e.content,containerId:e.props.containerId,id:e.props.toastId,theme:e.props.theme,type:e.props.type,data:e.props.data||{},isLoading:e.props.isLoading,icon:e.props.icon,status:t}:{}}let f=new Map,m=[],g=new Set,h=e=>g.forEach(t=>t(e)),y=()=>f.size>0;function v(e,t){var n;if(t)return!(null==(n=f.get(t))||!n.isToastActive(e));let o=!1;return f.forEach(t=>{t.isToastActive(e)&&(o=!0)}),o}function b(e,t){c(e)&&(y()||m.push({content:e,options:t}),f.forEach(n=>{n.buildToast(e,t)}))}function x(e,t){f.forEach(n=>{null!=t&&null!=t&&t.containerId?(null==t?void 0:t.containerId)===n.id&&n.toggle(e,null==t?void 0:t.id):n.toggle(e,null==t?void 0:t.id)})}function E(e){let{subscribe:t,getSnapshot:n,setProps:a}=(0,o.useRef)(function(e){let t=e.containerId||1;return{subscribe(n){let a=function(e,t,n){let a=1,d=0,u=[],f=[],m=[],g=t,h=new Map,y=new Set,v=()=>{m=Array.from(h.values()),y.forEach(e=>e())},b=e=>{f=null==e?[]:f.filter(t=>t!==e),v()},x=e=>{let{toastId:t,onOpen:a,updateId:s,children:r}=e.props,l=null==s;e.staleId&&h.delete(e.staleId),h.set(t,e),f=[...f,e.props.toastId].filter(t=>t!==e.staleId),v(),n(p(e,l?"added":"updated")),l&&i(a)&&a((0,o.isValidElement)(r)&&r.props)};return{id:e,props:g,observe:e=>(y.add(e),()=>y.delete(e)),toggle:(e,t)=>{h.forEach(n=>{null!=t&&t!==n.props.toastId||i(n.toggle)&&n.toggle(e)})},removeToast:b,toasts:h,clearQueue:()=>{d-=u.length,u=[]},buildToast:(t,f)=>{var m,y;if((t=>{let{containerId:n,toastId:o,updateId:a}=t,s=h.has(o)&&null==a;return(n?n!==e:1!==e)||s})(f))return;let{toastId:E,updateId:T,data:_,staleId:N,delay:I}=f,w=()=>{b(E)},C=null==T;C&&d++;let k={...g,style:g.toastStyle,key:a++,...Object.fromEntries(Object.entries(f).filter(e=>{let[t,n]=e;return null!=n})),toastId:E,updateId:T,data:_,closeToast:w,isIn:!1,className:l(f.className||g.toastClassName),bodyClassName:l(f.bodyClassName||g.bodyClassName),progressClassName:l(f.progressClassName||g.progressClassName),autoClose:!f.isLoading&&(m=f.autoClose,y=g.autoClose,!1===m||s(m)&&m>0?m:y),deleteToast(){let e=h.get(E),{onClose:t,children:a}=e.props;i(t)&&t((0,o.isValidElement)(a)&&a.props),n(p(e,"removed")),h.delete(E),--d<0&&(d=0),u.length>0?x(u.shift()):v()}};k.closeButton=g.closeButton,!1===f.closeButton||c(f.closeButton)?k.closeButton=f.closeButton:!0===f.closeButton&&(k.closeButton=!c(g.closeButton)||g.closeButton);let L=t;(0,o.isValidElement)(t)&&!r(t.type)?L=(0,o.cloneElement)(t,{closeToast:w,toastProps:k,data:_}):i(t)&&(L=t({closeToast:w,toastProps:k,data:_}));let j={content:L,props:k,staleId:N};g.limit&&g.limit>0&&d>g.limit&&C?u.push(j):s(I)?setTimeout(()=>{x(j)},I):x(j)},setProps(e){g=e},setToggle:(e,t)=>{h.get(e).toggle=t},isToastActive:e=>f.some(t=>t===e),getSnapshot:()=>g.newestOnTop?m.reverse():m}}(t,e,h);f.set(t,a);let d=a.observe(n);return m.forEach(e=>b(e.content,e.options)),m=[],()=>{d(),f.delete(t)}},setProps(e){var n;null==(n=f.get(t))||n.setProps(e)},getSnapshot(){var e;return null==(e=f.get(t))?void 0:e.getSnapshot()}}}(e)).current;a(e);let d=(0,o.useSyncExternalStore)(t,n,n);return{getToastToRender:function(e){if(!d)return[];let t=new Map;return d.forEach(e=>{let{position:n}=e.props;t.has(n)||t.set(n,[]),t.get(n).push(e)}),Array.from(t,t=>e(t[0],t[1]))},isToastActive:v,count:null==d?void 0:d.length}}function T(e){var t,n;let[a,s]=(0,o.useState)(!1),[r,i]=(0,o.useState)(!1),l=(0,o.useRef)(null),c=(0,o.useRef)({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:d,pauseOnHover:u,closeToast:p,onClick:m,closeOnClick:g}=e;function h(){s(!0)}function y(){s(!1)}function v(t){let n=l.current;c.canDrag&&n&&(c.didMove=!0,a&&y(),c.delta="x"===e.draggableDirection?t.clientX-c.start:t.clientY-c.start,c.start!==t.clientX&&(c.canCloseOnClick=!1),n.style.transform=`translate3d(${"x"===e.draggableDirection?`${c.delta}px, var(--y)`:`0, calc(${c.delta}px + var(--y))`},0)`,n.style.opacity=""+(1-Math.abs(c.delta/c.removalDistance)))}function b(){document.removeEventListener("pointermove",v),document.removeEventListener("pointerup",b);let t=l.current;if(c.canDrag&&c.didMove&&t){if(c.canDrag=!1,Math.abs(c.delta)>c.removalDistance)return i(!0),e.closeToast(),void e.collapseAll();t.style.transition="transform 0.2s, opacity 0.2s",t.style.removeProperty("transform"),t.style.removeProperty("opacity")}}null==(n=f.get((t={id:e.toastId,containerId:e.containerId,fn:s}).containerId||1))||n.setToggle(t.id,t.fn),(0,o.useEffect)(()=>{if(e.pauseOnFocusLoss)return document.hasFocus()||y(),window.addEventListener("focus",h),window.addEventListener("blur",y),()=>{window.removeEventListener("focus",h),window.removeEventListener("blur",y)}},[e.pauseOnFocusLoss]);let x={onPointerDown:function(t){if(!0===e.draggable||e.draggable===t.pointerType){c.didMove=!1,document.addEventListener("pointermove",v),document.addEventListener("pointerup",b);let n=l.current;c.canCloseOnClick=!0,c.canDrag=!0,n.style.transition="none","x"===e.draggableDirection?(c.start=t.clientX,c.removalDistance=n.offsetWidth*(e.draggablePercent/100)):(c.start=t.clientY,c.removalDistance=n.offsetHeight*(80===e.draggablePercent?1.5*e.draggablePercent:e.draggablePercent)/100)}},onPointerUp:function(t){let{top:n,bottom:o,left:a,right:s}=l.current.getBoundingClientRect();"touchend"!==t.nativeEvent.type&&e.pauseOnHover&&t.clientX>=a&&t.clientX<=s&&t.clientY>=n&&t.clientY<=o?y():h()}};return d&&u&&(x.onMouseEnter=y,e.stacked||(x.onMouseLeave=h)),g&&(x.onClick=e=>{m&&m(e),c.canCloseOnClick&&p()}),{playToast:h,pauseToast:y,isRunning:a,preventExitTransition:r,toastRef:l,eventHandlers:x}}function _(e){let{delay:t,isRunning:n,closeToast:s,type:r="default",hide:l,className:c,style:d,controlledProgress:u,progress:p,rtl:f,isIn:m,theme:g}=e,h=l||u&&0===p,y={...d,animationDuration:`${t}ms`,animationPlayState:n?"running":"paused"};u&&(y.transform=`scaleX(${p})`);let v=a("Toastify__progress-bar",u?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${g}`,`Toastify__progress-bar--${r}`,{"Toastify__progress-bar--rtl":f}),b=i(c)?c({rtl:f,type:r,defaultClassName:v}):a(v,c);return o.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":h},o.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${g} Toastify__progress-bar--${r}`}),o.createElement("div",{role:"progressbar","aria-hidden":h?"true":"false","aria-label":"notification timer",className:b,style:y,[u&&p>=1?"onTransitionEnd":"onAnimationEnd"]:u&&p<1?null:()=>{m&&s()}}))}let N=1,I=()=>""+N++;function w(e,t){return b(e,t),t.toastId}function C(e,t){return{...t,type:t&&t.type||e,toastId:t&&(r(t.toastId)||s(t.toastId))?t.toastId:I()}}function k(e){return(t,n)=>w(t,C(e,n))}function L(e,t){return w(e,C("default",t))}L.loading=(e,t)=>w(e,C("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...t})),L.promise=function(e,t,n){let o,{pending:a,error:s,success:l}=t;a&&(o=r(a)?L.loading(a,n):L.loading(a.render,{...n,...a}));let c={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},d=(e,t,a)=>{if(null==t)return void L.dismiss(o);let s={type:e,...c,...n,data:a},i=r(t)?{render:t}:t;return o?L.update(o,{...s,...i}):L(i.render,{...s,...i}),a},u=i(e)?e():e;return u.then(e=>d("success",l,e)).catch(e=>d("error",s,e)),u},L.success=k("success"),L.info=k("info"),L.error=k("error"),L.warning=k("warning"),L.warn=L.warning,L.dark=(e,t)=>w(e,C("default",{theme:"dark",...t})),L.dismiss=function(e){var t,n;y()?null==e||r(t=e)||s(t)?f.forEach(t=>{t.removeToast(e)}):e&&("containerId"in e||"id"in e)&&((null==(n=f.get(e.containerId))?void 0:n.removeToast(e.id))||f.forEach(t=>{t.removeToast(e.id)})):m=m.filter(t=>null!=e&&t.options.toastId!==e)},L.clearWaitingQueue=function(e){void 0===e&&(e={}),f.forEach(t=>{!t.props.limit||e.containerId&&t.id!==e.containerId||t.clearQueue()})},L.isActive=v,L.update=function(e,t){void 0===t&&(t={});let n=((e,t)=>{var n;let{containerId:o}=t;return null==(n=f.get(o||1))?void 0:n.toasts.get(e)})(e,t);if(n){let{props:o,content:a}=n,s={delay:100,...o,...t,toastId:t.toastId||e,updateId:I()};s.toastId!==e&&(s.staleId=e);let r=s.render||a;delete s.render,w(r,s)}},L.done=e=>{L.update(e,{progress:1})},L.onChange=function(e){return g.add(e),()=>{g.delete(e)}},L.play=e=>x(!0,e),L.pause=e=>x(!1,e);let j="undefined"!=typeof window?o.useLayoutEffect:o.useEffect,P=e=>{let{theme:t,type:n,isLoading:a,...s}=e;return o.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:"colored"===t?"currentColor":`var(--toastify-icon-color-${n})`,...s})},$={info:function(e){return o.createElement(P,{...e},o.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))},warning:function(e){return o.createElement(P,{...e},o.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))},success:function(e){return o.createElement(P,{...e},o.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))},error:function(e){return o.createElement(P,{...e},o.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))},spinner:function(){return o.createElement("div",{className:"Toastify__spinner"})}},A=e=>{let{isRunning:t,preventExitTransition:n,toastRef:s,eventHandlers:r,playToast:l}=T(e),{closeButton:c,children:d,autoClose:u,onClick:p,type:f,hideProgressBar:m,closeToast:g,transition:h,position:y,className:v,style:b,bodyClassName:x,bodyStyle:E,progressClassName:N,progressStyle:I,updateId:w,role:C,progress:k,rtl:L,toastId:j,deleteToast:P,isIn:A,isLoading:B,closeOnClick:S,theme:M}=e,O=a("Toastify__toast",`Toastify__toast-theme--${M}`,`Toastify__toast--${f}`,{"Toastify__toast--rtl":L},{"Toastify__toast--close-on-click":S}),R=i(v)?v({rtl:L,position:y,type:f,defaultClassName:O}):a(O,v),D=function(e){let{theme:t,type:n,isLoading:a,icon:s}=e,r=null,l={theme:t,type:n,isLoading:a};return!1===s||(i(s)?r=s(l):(0,o.isValidElement)(s)?r=(0,o.cloneElement)(s,l):a?r=$.spinner():n in $&&(r=$[n](l))),r}(e),z=!!k||!u,F={closeToast:g,type:f,theme:M},H=null;return!1===c||(H=i(c)?c(F):(0,o.isValidElement)(c)?(0,o.cloneElement)(c,F):function(e){let{closeToast:t,theme:n,ariaLabel:a="close"}=e;return o.createElement("button",{className:`Toastify__close-button Toastify__close-button--${n}`,type:"button",onClick:e=>{e.stopPropagation(),t(e)},"aria-label":a},o.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},o.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}(F)),o.createElement(h,{isIn:A,done:P,position:y,preventExitTransition:n,nodeRef:s,playToast:l},o.createElement("div",{id:j,onClick:p,"data-in":A,className:R,...r,style:b,ref:s},o.createElement("div",{...A&&{role:C},className:i(x)?x({type:f}):a("Toastify__toast-body",x),style:E},null!=D&&o.createElement("div",{className:a("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!B})},D),o.createElement("div",null,d)),H,o.createElement(_,{...w&&!z?{key:`pb-${w}`}:{},rtl:L,theme:M,delay:u,isRunning:t,isIn:A,closeToast:g,hide:m,type:f,style:I,className:N,controlledProgress:z,progress:k||0})))},B=function(e,t){return void 0===t&&(t=!1),{enter:`Toastify--animate Toastify__${e}-enter`,exit:`Toastify--animate Toastify__${e}-exit`,appendPosition:t}},S=u(B("bounce",!0)),M=u(B("slide",!0)),O=u(B("zoom")),R=u(B("flip")),D={position:"top-right",transition:S,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light"};function z(e){let t={...D,...e},n=e.stacked,[s,r]=(0,o.useState)(!0),c=(0,o.useRef)(null),{getToastToRender:d,isToastActive:u,count:p}=E(t),{className:f,style:m,rtl:g,containerId:h}=t;function y(){n&&(r(!0),L.play())}return j(()=>{if(n){var e;let n=c.current.querySelectorAll('[data-in="true"]'),o=null==(e=t.position)?void 0:e.includes("top"),a=0,r=0;Array.from(n).reverse().forEach((e,t)=>{e.classList.add("Toastify__toast--stacked"),t>0&&(e.dataset.collapsed=`${s}`),e.dataset.pos||(e.dataset.pos=o?"top":"bot");let n=a*(s?.2:1)+(s?0:12*t);e.style.setProperty("--y",`${o?n:-1*n}px`),e.style.setProperty("--g","12"),e.style.setProperty("--s",""+(1-(s?r:0))),a+=e.offsetHeight,r+=.025})}},[s,p,n]),o.createElement("div",{ref:c,className:"Toastify",id:h,onMouseEnter:()=>{n&&(r(!1),L.pause())},onMouseLeave:y},d((e,t)=>{let s=t.length?{...m}:{...m,pointerEvents:"none"};return o.createElement("div",{className:function(e){let t=a("Toastify__toast-container",`Toastify__toast-container--${e}`,{"Toastify__toast-container--rtl":g});return i(f)?f({position:e,rtl:g,defaultClassName:t}):a(t,l(f))}(e),style:s,key:`container-${e}`},t.map(e=>{let{content:t,props:a}=e;return o.createElement(A,{...a,stacked:n,collapseAll:y,isIn:u(a.toastId,a.containerId),style:a.style,key:`toast-${a.key}`},t)}))}))}}},function(e){e.O(0,[580,691,396,637,971,938,744],function(){return e(e.s=8699)}),_N_E=e.O()}]);
(()=>{var e={};e.id=5982,e.ids=[5982],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},96870:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>d,routeModule:()=>x,tree:()=>c});var s=r(50482),o=r(69108),i=r(62563),a=r.n(i),l=r(68300),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(t,n);let c=["",{children:["profile",{children:["favorites",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,62404)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\profile\\favorites\\page.jsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,39459)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\profile\\favorites\\page.jsx"],p="/profile/favorites/page",u={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/profile/favorites/page",pathname:"/profile/favorites",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},22640:(e,t,r)=>{Promise.resolve().then(r.bind(r,10491))},10491:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(95344),o=r(6880),i=r(53608),a=r(41223),l=r.n(a),n=r(20783),c=r.n(n),d=r(22254),p=r(3729),u=r(69697);let x=()=>{let[e,t]=(0,p.useState)([]),[r,a]=(0,p.useState)(!0),n=(0,d.useRouter)();(0,p.useEffect)(()=>{let e=localStorage.getItem("authToken"),t=localStorage.getItem("userId");if(!e||!t){n.push("/login");return}x(e)},[n]);let x=async e=>{try{a(!0);let r=await i.Z.get("/api/favorites",{headers:{Authorization:`Bearer ${e}`}});r.data.success?t(r.data.favorites||[]):u.toast.error("Failed to load favorites")}catch(e){console.error("Error fetching favorites:",e),u.toast.error("Failed to load favorites")}finally{a(!1)}},m=async r=>{try{let s=localStorage.getItem("authToken");(await i.Z.delete(`/api/favorites?blogId=${r}`,{headers:{Authorization:`Bearer ${s}`}})).data.success?(t(e.filter(e=>e._id.toString()!==r)),u.toast.success("Removed from favorites")):u.toast.error("Failed to remove from favorites")}catch(e){console.error("Error removing favorite:",e),u.toast.error("Failed to remove from favorites")}};return s.jsx("div",{className:"min-h-screen bg-gray-100",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[s.jsx("h1",{className:"text-2xl font-bold",children:"My Favorite Blogs"}),s.jsx(c(),{href:"/profile",className:"px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300",children:"Back to Profile"})]}),r?s.jsx("div",{className:"flex justify-center items-center py-16",children:s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-black"})}):e.length>0?s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,s.jsxs)("div",{className:"bg-white rounded-lg overflow-hidden shadow-md",children:[s.jsx("div",{className:"relative h-48",children:s.jsx(l(),{src:e.image||o.L.placeholder,alt:e.title,fill:!0,className:"object-cover"})}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[s.jsx("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 rounded-full",children:e.category}),s.jsx("button",{onClick:()=>m(e._id),className:"text-yellow-500 hover:text-yellow-700",title:"Remove from favorites",children:s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:s.jsx("polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"})})})]}),s.jsx("h2",{className:"text-lg font-semibold mb-2 line-clamp-2",children:e.title}),s.jsx("div",{className:"text-sm text-gray-600 mb-3 line-clamp-3",dangerouslySetInnerHTML:{__html:e.description.substring(0,120)+"..."}}),s.jsx(c(),{href:`/blogs/${e._id}`,className:"inline-block px-3 py-1 bg-black text-white text-sm rounded hover:bg-gray-800 transition",children:"Read Article"})]})]},e._id))}):(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[s.jsx("div",{className:"text-5xl mb-4",children:"⭐"}),s.jsx("h2",{className:"text-xl font-semibold mb-2",children:"No favorites yet"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"Start adding blogs to your favorites by clicking the star icon on blog posts."}),s.jsx(c(),{href:"/",className:"inline-block px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition",children:"Browse Blogs"})]})]})})}},62404:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>o,default:()=>a});let s=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\profile\favorites\page.jsx`),{__esModule:o,$$typeof:i}=s,a=s.default}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,3998,337,8468,5757],()=>r(96870));module.exports=s})();
"use strict";(()=>{var e={};e.id=1022,e.ids=[1022],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},57147:e=>{e.exports=require("fs")},71017:e=>{e.exports=require("path")},3513:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>b,originalPathname:()=>q,patchFetch:()=>w,requestAsyncStorage:()=>f,routeModule:()=>h,serverHooks:()=>v,staticGenerationAsyncStorage:()=>S,staticGenerationBailout:()=>x});var n={};r.r(n),r.d(n,{DELETE:()=>y,GET:()=>m});var a=r(95419),s=r(69108),o=r(99678),i=r(78070),u=r(91887),c=r(31833),d=r(57147),l=r.n(d),p=r(71017),g=r.n(p);async function m(e,{params:t}){try{await (0,u.n)();let e=t.id,r=await c.Z.findById(e);if(!r)return i.Z.json({success:!1,message:"Image not found"},{status:404});let n=new i.Z(r.data);return n.headers.set("Content-Type",r.contentType),n.headers.set("Content-Length",r.size.toString()),n.headers.set("Cache-Control","public, max-age=31536000"),n}catch(e){return console.error("Error retrieving image:",e),i.Z.json({success:!1,message:"Failed to retrieve image"},{status:500})}}async function y(e,{params:t}){try{await (0,u.n)();let e=t.id,r=await c.Z.findById(e);if(!r)return i.Z.json({success:!1,message:"Image not found"},{status:404});if(r.path){let e=g().join(process.cwd(),"public",r.path);try{l().existsSync(e)&&l().unlinkSync(e)}catch(e){console.error("Error deleting physical file:",e)}}return await c.Z.findByIdAndDelete(e),i.Z.json({success:!0,message:"Image deleted successfully"})}catch(e){return console.error("Error deleting image:",e),i.Z.json({success:!1,message:"Failed to delete image"},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/images/[id]/route",pathname:"/api/images/[id]",filename:"route",bundlePath:"app/api/images/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\images\\[id]\\route.js",nextConfigOutput:"",userland:n}),{requestAsyncStorage:f,staticGenerationAsyncStorage:S,serverHooks:v,headerHooks:b,staticGenerationBailout:x}=h,q="/api/images/[id]/route";function w(){return(0,o.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:S})}},91887:(e,t,r)=>{r.d(t,{n:()=>s});var n=r(11185),a=r.n(n);let s=async()=>{try{if(a().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await a().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},31833:(e,t,r)=>{r.d(t,{Z:()=>o});var n=r(11185),a=r.n(n);let s=new(a()).Schema({filename:{type:String,required:!0},path:{type:String,required:!0},url:{type:String,required:!0},contentType:{type:String,required:!0},size:{type:Number,required:!0},data:{type:Buffer,required:!0},blogId:{type:a().Schema.Types.Mixed,ref:"blog",default:null},uploadDate:{type:Date,default:Date.now}}),o=a().models.image||a().model("image",s)},78070:(e,t,r)=>{Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return n.NextResponse}});let n=r(70457)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[1638,2993],()=>r(3513));module.exports=n})();
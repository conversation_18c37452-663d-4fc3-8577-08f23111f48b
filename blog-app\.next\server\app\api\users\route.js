"use strict";(()=>{var e={};e.id=5701,e.ids=[5701],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},66170:(e,t,s)=>{s.r(t),s.d(t,{headerHooks:()=>y,originalPathname:()=>w,patchFetch:()=>S,requestAsyncStorage:()=>m,routeModule:()=>p,serverHooks:()=>f,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>h});var r={};s.r(r),s.d(r,{DELETE:()=>l,GET:()=>d});var n=s(95419),a=s(69108),o=s(99678),u=s(91887),i=s(96488),c=s(78070);async function d(){try{let e=await i.Z.find({},{password:0});return c.Z.json({success:!0,users:e})}catch(e){return console.error("Error fetching users:",e),c.Z.json({success:!1,message:"Failed to fetch users"},{status:500})}}async function l(e){try{let t=e.nextUrl.searchParams.get("id");if(!t)return c.Z.json({success:!1,message:"User ID is required"},{status:400});let s=await i.Z.findById(t);if(!s)return c.Z.json({success:!1,message:"User not found"},{status:404});if("admin"===s.role&&await i.Z.countDocuments({role:"admin"})<=1)return c.Z.json({success:!1,message:"Cannot delete the last admin account"},{status:403});await i.Z.findByIdAndDelete(t);let r=await i.Z.find({},{password:0});return c.Z.json({success:!0,message:"User deleted successfully",users:r})}catch(e){return console.error("Error deleting user:",e),c.Z.json({success:!1,message:"Failed to delete user"},{status:500})}}(async()=>{await (0,u.n)()})();let p=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/users/route",pathname:"/api/users",filename:"route",bundlePath:"app/api/users/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\users\\route.js",nextConfigOutput:"",userland:r}),{requestAsyncStorage:m,staticGenerationAsyncStorage:g,serverHooks:f,headerHooks:y,staticGenerationBailout:h}=p,w="/api/users/route";function S(){return(0,o.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:g})}},91887:(e,t,s)=>{s.d(t,{n:()=>a});var r=s(11185),n=s.n(r);let a=async()=>{try{if(n().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await n().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},96488:(e,t,s)=>{s.d(t,{Z:()=>o});var r=s(11185),n=s.n(r);let a=new(n()).Schema({email:{type:String,required:!0,unique:!0},password:{type:String,required:!0},role:{type:String,default:"user",enum:["user","admin"]},profilePicture:{type:String,default:"/default_profile.png"},name:{type:String,default:""},date:{type:Date,default:Date.now()}}),o=n().models.user||n().model("user",a)},78070:(e,t,s)=>{Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return r.NextResponse}});let r=s(70457)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,2993],()=>s(66170));module.exports=r})();
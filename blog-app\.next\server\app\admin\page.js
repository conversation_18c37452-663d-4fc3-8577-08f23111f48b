(()=>{var e={};e.id=3,e.ids=[3],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},13030:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=t(50482),a=t(69108),i=t(62563),l=t.n(i),n=t(68300),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let o=["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,68229)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\page.jsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,32143)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,39459)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\page.jsx"],x="/admin/page",p={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},4747:(e,s,t)=>{Promise.resolve().then(t.bind(t,58370))},58370:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(95344),a=t(3729),i=t(53608),l=t(69697);let n=()=>{let[e,s]=(0,a.useState)({blogs:0,users:0,subscriptions:0,loading:!0}),[t,n]=(0,a.useState)([]),[d,o]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{try{let e=(await i.Z.get("/api/blog")).data.blogs||[],t=(await i.Z.get("/api/users")).data.users||[],r=(await i.Z.get("/api/email")).data.emails||[];s({blogs:e.length,users:t.length,subscriptions:r.length,loading:!1})}catch(e){console.error("Error fetching dashboard stats:",e),l.toast.error("Failed to load dashboard statistics"),s(e=>({...e,loading:!1}))}})()},[]),(0,a.useEffect)(()=>{(async()=>{try{o(!0);let e=await i.Z.get("/api/activity");e.data.success?n(e.data.activities||[]):l.toast.error("Failed to load recent activity")}catch(e){console.error("Error fetching recent activity:",e),l.toast.error("Failed to load recent activity")}finally{o(!1)}})()},[]),(0,r.jsxs)("div",{className:"flex-1 pt-5 px-5 sm:pt-12 sm:pl-16",children:[r.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Dashboard Overview"}),e.loading?r.jsx("div",{className:"text-center py-10",children:"Loading dashboard data..."}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md",children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-700",children:"Total Blog Posts"}),r.jsx("p",{className:"text-3xl font-bold mt-2",children:e.blogs}),r.jsx("div",{className:"mt-2",children:r.jsx("a",{href:"/admin/addBlog?tab=manage",className:"text-blue-600 text-sm hover:underline",children:"View all posts →"})})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md",children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-700",children:"Registered Users"}),r.jsx("p",{className:"text-3xl font-bold mt-2",children:e.users}),r.jsx("div",{className:"mt-2",children:r.jsx("a",{href:"/admin/addUser",className:"text-blue-600 text-sm hover:underline",children:"Add new user →"})})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md",children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-700",children:"Email Subscriptions"}),r.jsx("p",{className:"text-3xl font-bold mt-2",children:e.subscriptions}),r.jsx("div",{className:"mt-2",children:r.jsx("a",{href:"/admin/subscriptions",className:"text-blue-600 text-sm hover:underline",children:"View all subscriptions →"})})]})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md mb-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-700",children:"Blog Reactions"}),r.jsx("a",{href:"/admin/reactions",className:"text-blue-600 text-sm hover:underline",children:"View all reactions →"})]}),e.loading?r.jsx("p",{children:"Loading reaction data..."}):(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",className:"text-red-500",children:r.jsx("path",{d:"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"})}),r.jsx("h3",{className:"font-medium",children:"Most Popular Posts"})]}),r.jsx("a",{href:"/admin/reactions?sort=most",className:"text-blue-600 text-sm hover:underline",children:"View most liked posts →"})]}),(0,r.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"text-gray-500",children:r.jsx("path",{d:"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"})}),r.jsx("h3",{className:"font-medium",children:"Least Popular Posts"})]}),r.jsx("a",{href:"/admin/reactions?sort=least",className:"text-blue-600 text-sm hover:underline",children:"View least liked posts →"})]})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Recent Activity"}),r.jsx("div",{className:"bg-white rounded-lg border border-gray-300 overflow-hidden",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[r.jsx("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Details"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Time"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:d?r.jsx("tr",{children:r.jsx("td",{colSpan:"3",className:"px-6 py-4 text-center",children:"Loading recent activity..."})}):t.length>0?t.map((e,s)=>(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("span",{className:"font-medium",children:e.type})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.message}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.timestamp).toLocaleString()})]},s)):r.jsx("tr",{children:r.jsx("td",{colSpan:"3",className:"px-6 py-4 text-center",children:"No recent activity found"})})})]})})]})]})]})}},68229:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>a,default:()=>l});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\admin\page.jsx`),{__esModule:a,$$typeof:i}=r,l=r.default}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[1638,3998,337,8468,5757,7388],()=>t(13030));module.exports=r})();
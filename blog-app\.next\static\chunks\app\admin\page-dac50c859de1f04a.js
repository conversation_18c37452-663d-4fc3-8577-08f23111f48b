(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3],{9317:function(e,t,s){Promise.resolve().then(s.bind(s,8209))},8209:function(e,t,s){"use strict";s.r(t);var n=s(7437),a=s(2265),r=s(2173),o=s(7948);t.default=()=>{let[e,t]=(0,a.useState)({blogs:0,users:0,subscriptions:0,loading:!0}),[s,i]=(0,a.useState)([]),[l,c]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{try{let e=(await r.Z.get("/api/blog")).data.blogs||[],s=(await r.Z.get("/api/users")).data.users||[],n=(await r.Z.get("/api/email")).data.emails||[];t({blogs:e.length,users:s.length,subscriptions:n.length,loading:!1})}catch(e){console.error("Error fetching dashboard stats:",e),o.toast.error("Failed to load dashboard statistics"),t(e=>({...e,loading:!1}))}})()},[]),(0,a.useEffect)(()=>{(async()=>{try{c(!0);let e=await r.Z.get("/api/activity");e.data.success?i(e.data.activities||[]):o.toast.error("Failed to load recent activity")}catch(e){console.error("Error fetching recent activity:",e),o.toast.error("Failed to load recent activity")}finally{c(!1)}})()},[]),(0,n.jsxs)("div",{className:"flex-1 pt-5 px-5 sm:pt-12 sm:pl-16",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Dashboard Overview"}),e.loading?(0,n.jsx)("div",{className:"text-center py-10",children:"Loading dashboard data..."}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,n.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-700",children:"Total Blog Posts"}),(0,n.jsx)("p",{className:"text-3xl font-bold mt-2",children:e.blogs}),(0,n.jsx)("div",{className:"mt-2",children:(0,n.jsx)("a",{href:"/admin/addBlog?tab=manage",className:"text-blue-600 text-sm hover:underline",children:"View all posts →"})})]}),(0,n.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-700",children:"Registered Users"}),(0,n.jsx)("p",{className:"text-3xl font-bold mt-2",children:e.users}),(0,n.jsx)("div",{className:"mt-2",children:(0,n.jsx)("a",{href:"/admin/addUser",className:"text-blue-600 text-sm hover:underline",children:"Add new user →"})})]}),(0,n.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-700",children:"Email Subscriptions"}),(0,n.jsx)("p",{className:"text-3xl font-bold mt-2",children:e.subscriptions}),(0,n.jsx)("div",{className:"mt-2",children:(0,n.jsx)("a",{href:"/admin/subscriptions",className:"text-blue-600 text-sm hover:underline",children:"View all subscriptions →"})})]})]}),(0,n.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md mb-8",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-700",children:"Blog Reactions"}),(0,n.jsx)("a",{href:"/admin/reactions",className:"text-blue-600 text-sm hover:underline",children:"View all reactions →"})]}),e.loading?(0,n.jsx)("p",{children:"Loading reaction data..."}):(0,n.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",className:"text-red-500",children:(0,n.jsx)("path",{d:"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"})}),(0,n.jsx)("h3",{className:"font-medium",children:"Most Popular Posts"})]}),(0,n.jsx)("a",{href:"/admin/reactions?sort=most",className:"text-blue-600 text-sm hover:underline",children:"View most liked posts →"})]}),(0,n.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"text-gray-500",children:(0,n.jsx)("path",{d:"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"})}),(0,n.jsx)("h3",{className:"font-medium",children:"Least Popular Posts"})]}),(0,n.jsx)("a",{href:"/admin/reactions?sort=least",className:"text-blue-600 text-sm hover:underline",children:"View least liked posts →"})]})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Recent Activity"}),(0,n.jsx)("div",{className:"bg-white rounded-lg border border-gray-300 overflow-hidden",children:(0,n.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,n.jsx)("thead",{className:"bg-gray-50",children:(0,n.jsxs)("tr",{children:[(0,n.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,n.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Details"}),(0,n.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Time"})]})}),(0,n.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:l?(0,n.jsx)("tr",{children:(0,n.jsx)("td",{colSpan:"3",className:"px-6 py-4 text-center",children:"Loading recent activity..."})}):s.length>0?s.map((e,t)=>(0,n.jsxs)("tr",{children:[(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,n.jsx)("span",{className:"font-medium",children:e.type})}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.message}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.timestamp).toLocaleString()})]},t)):(0,n.jsx)("tr",{children:(0,n.jsx)("td",{colSpan:"3",className:"px-6 py-4 text-center",children:"No recent activity found"})})})]})})]})]})]})}},7948:function(e,t,s){"use strict";s.r(t),s.d(t,{Bounce:function(){return A},Flip:function(){return D},Icons:function(){return $},Slide:function(){return M},ToastContainer:function(){return z},Zoom:function(){return O},collapseToast:function(){return d},cssTransition:function(){return u},toast:function(){return L},useToast:function(){return N},useToastContainer:function(){return E}});var n=s(2265),a=function(){for(var e,t,s=0,n="",a=arguments.length;s<a;s++)(e=arguments[s])&&(t=function e(t){var s,n,a="";if("string"==typeof t||"number"==typeof t)a+=t;else if("object"==typeof t){if(Array.isArray(t)){var r=t.length;for(s=0;s<r;s++)t[s]&&(n=e(t[s]))&&(a&&(a+=" "),a+=n)}else for(n in t)t[n]&&(a&&(a+=" "),a+=n)}return a}(e))&&(n&&(n+=" "),n+=t);return n};let r=e=>"number"==typeof e&&!isNaN(e),o=e=>"string"==typeof e,i=e=>"function"==typeof e,l=e=>o(e)||i(e)?e:null,c=e=>(0,n.isValidElement)(e)||o(e)||i(e)||r(e);function d(e,t,s){void 0===s&&(s=300);let{scrollHeight:n,style:a}=e;requestAnimationFrame(()=>{a.minHeight="initial",a.height=n+"px",a.transition=`all ${s}ms`,requestAnimationFrame(()=>{a.height="0",a.padding="0",a.margin="0",setTimeout(t,s)})})}function u(e){let{enter:t,exit:s,appendPosition:a=!1,collapse:r=!0,collapseDuration:o=300}=e;return function(e){let{children:i,position:l,preventExitTransition:c,done:u,nodeRef:m,isIn:p,playToast:f}=e,g=a?`${t}--${l}`:t,h=a?`${s}--${l}`:s,y=(0,n.useRef)(0);return(0,n.useLayoutEffect)(()=>{let e=m.current,t=g.split(" "),s=n=>{n.target===m.current&&(f(),e.removeEventListener("animationend",s),e.removeEventListener("animationcancel",s),0===y.current&&"animationcancel"!==n.type&&e.classList.remove(...t))};e.classList.add(...t),e.addEventListener("animationend",s),e.addEventListener("animationcancel",s)},[]),(0,n.useEffect)(()=>{let e=m.current,t=()=>{e.removeEventListener("animationend",t),r?d(e,u,o):u()};p||(c?t():(y.current=1,e.className+=` ${h}`,e.addEventListener("animationend",t)))},[p]),n.createElement(n.Fragment,null,i)}}function m(e,t){return null!=e?{content:e.content,containerId:e.props.containerId,id:e.props.toastId,theme:e.props.theme,type:e.props.type,data:e.props.data||{},isLoading:e.props.isLoading,icon:e.props.icon,status:t}:{}}let p=new Map,f=[],g=new Set,h=e=>g.forEach(t=>t(e)),y=()=>p.size>0;function v(e,t){var s;if(t)return!(null==(s=p.get(t))||!s.isToastActive(e));let n=!1;return p.forEach(t=>{t.isToastActive(e)&&(n=!0)}),n}function x(e,t){c(e)&&(y()||f.push({content:e,options:t}),p.forEach(s=>{s.buildToast(e,t)}))}function b(e,t){p.forEach(s=>{null!=t&&null!=t&&t.containerId?(null==t?void 0:t.containerId)===s.id&&s.toggle(e,null==t?void 0:t.id):s.toggle(e,null==t?void 0:t.id)})}function E(e){let{subscribe:t,getSnapshot:s,setProps:a}=(0,n.useRef)(function(e){let t=e.containerId||1;return{subscribe(s){let a=function(e,t,s){let a=1,d=0,u=[],p=[],f=[],g=t,h=new Map,y=new Set,v=()=>{f=Array.from(h.values()),y.forEach(e=>e())},x=e=>{p=null==e?[]:p.filter(t=>t!==e),v()},b=e=>{let{toastId:t,onOpen:a,updateId:r,children:o}=e.props,l=null==r;e.staleId&&h.delete(e.staleId),h.set(t,e),p=[...p,e.props.toastId].filter(t=>t!==e.staleId),v(),s(m(e,l?"added":"updated")),l&&i(a)&&a((0,n.isValidElement)(o)&&o.props)};return{id:e,props:g,observe:e=>(y.add(e),()=>y.delete(e)),toggle:(e,t)=>{h.forEach(s=>{null!=t&&t!==s.props.toastId||i(s.toggle)&&s.toggle(e)})},removeToast:x,toasts:h,clearQueue:()=>{d-=u.length,u=[]},buildToast:(t,p)=>{var f,y;if((t=>{let{containerId:s,toastId:n,updateId:a}=t,r=h.has(n)&&null==a;return(s?s!==e:1!==e)||r})(p))return;let{toastId:E,updateId:N,data:T,staleId:w,delay:j}=p,_=()=>{x(E)},I=null==N;I&&d++;let C={...g,style:g.toastStyle,key:a++,...Object.fromEntries(Object.entries(p).filter(e=>{let[t,s]=e;return null!=s})),toastId:E,updateId:N,data:T,closeToast:_,isIn:!1,className:l(p.className||g.toastClassName),bodyClassName:l(p.bodyClassName||g.bodyClassName),progressClassName:l(p.progressClassName||g.progressClassName),autoClose:!p.isLoading&&(f=p.autoClose,y=g.autoClose,!1===f||r(f)&&f>0?f:y),deleteToast(){let e=h.get(E),{onClose:t,children:a}=e.props;i(t)&&t((0,n.isValidElement)(a)&&a.props),s(m(e,"removed")),h.delete(E),--d<0&&(d=0),u.length>0?b(u.shift()):v()}};C.closeButton=g.closeButton,!1===p.closeButton||c(p.closeButton)?C.closeButton=p.closeButton:!0===p.closeButton&&(C.closeButton=!c(g.closeButton)||g.closeButton);let L=t;(0,n.isValidElement)(t)&&!o(t.type)?L=(0,n.cloneElement)(t,{closeToast:_,toastProps:C,data:T}):i(t)&&(L=t({closeToast:_,toastProps:C,data:T}));let k={content:L,props:C,staleId:w};g.limit&&g.limit>0&&d>g.limit&&I?u.push(k):r(j)?setTimeout(()=>{b(k)},j):b(k)},setProps(e){g=e},setToggle:(e,t)=>{h.get(e).toggle=t},isToastActive:e=>p.some(t=>t===e),getSnapshot:()=>g.newestOnTop?f.reverse():f}}(t,e,h);p.set(t,a);let d=a.observe(s);return f.forEach(e=>x(e.content,e.options)),f=[],()=>{d(),p.delete(t)}},setProps(e){var s;null==(s=p.get(t))||s.setProps(e)},getSnapshot(){var e;return null==(e=p.get(t))?void 0:e.getSnapshot()}}}(e)).current;a(e);let d=(0,n.useSyncExternalStore)(t,s,s);return{getToastToRender:function(e){if(!d)return[];let t=new Map;return d.forEach(e=>{let{position:s}=e.props;t.has(s)||t.set(s,[]),t.get(s).push(e)}),Array.from(t,t=>e(t[0],t[1]))},isToastActive:v,count:null==d?void 0:d.length}}function N(e){var t,s;let[a,r]=(0,n.useState)(!1),[o,i]=(0,n.useState)(!1),l=(0,n.useRef)(null),c=(0,n.useRef)({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:d,pauseOnHover:u,closeToast:m,onClick:f,closeOnClick:g}=e;function h(){r(!0)}function y(){r(!1)}function v(t){let s=l.current;c.canDrag&&s&&(c.didMove=!0,a&&y(),c.delta="x"===e.draggableDirection?t.clientX-c.start:t.clientY-c.start,c.start!==t.clientX&&(c.canCloseOnClick=!1),s.style.transform=`translate3d(${"x"===e.draggableDirection?`${c.delta}px, var(--y)`:`0, calc(${c.delta}px + var(--y))`},0)`,s.style.opacity=""+(1-Math.abs(c.delta/c.removalDistance)))}function x(){document.removeEventListener("pointermove",v),document.removeEventListener("pointerup",x);let t=l.current;if(c.canDrag&&c.didMove&&t){if(c.canDrag=!1,Math.abs(c.delta)>c.removalDistance)return i(!0),e.closeToast(),void e.collapseAll();t.style.transition="transform 0.2s, opacity 0.2s",t.style.removeProperty("transform"),t.style.removeProperty("opacity")}}null==(s=p.get((t={id:e.toastId,containerId:e.containerId,fn:r}).containerId||1))||s.setToggle(t.id,t.fn),(0,n.useEffect)(()=>{if(e.pauseOnFocusLoss)return document.hasFocus()||y(),window.addEventListener("focus",h),window.addEventListener("blur",y),()=>{window.removeEventListener("focus",h),window.removeEventListener("blur",y)}},[e.pauseOnFocusLoss]);let b={onPointerDown:function(t){if(!0===e.draggable||e.draggable===t.pointerType){c.didMove=!1,document.addEventListener("pointermove",v),document.addEventListener("pointerup",x);let s=l.current;c.canCloseOnClick=!0,c.canDrag=!0,s.style.transition="none","x"===e.draggableDirection?(c.start=t.clientX,c.removalDistance=s.offsetWidth*(e.draggablePercent/100)):(c.start=t.clientY,c.removalDistance=s.offsetHeight*(80===e.draggablePercent?1.5*e.draggablePercent:e.draggablePercent)/100)}},onPointerUp:function(t){let{top:s,bottom:n,left:a,right:r}=l.current.getBoundingClientRect();"touchend"!==t.nativeEvent.type&&e.pauseOnHover&&t.clientX>=a&&t.clientX<=r&&t.clientY>=s&&t.clientY<=n?y():h()}};return d&&u&&(b.onMouseEnter=y,e.stacked||(b.onMouseLeave=h)),g&&(b.onClick=e=>{f&&f(e),c.canCloseOnClick&&m()}),{playToast:h,pauseToast:y,isRunning:a,preventExitTransition:o,toastRef:l,eventHandlers:b}}function T(e){let{delay:t,isRunning:s,closeToast:r,type:o="default",hide:l,className:c,style:d,controlledProgress:u,progress:m,rtl:p,isIn:f,theme:g}=e,h=l||u&&0===m,y={...d,animationDuration:`${t}ms`,animationPlayState:s?"running":"paused"};u&&(y.transform=`scaleX(${m})`);let v=a("Toastify__progress-bar",u?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${g}`,`Toastify__progress-bar--${o}`,{"Toastify__progress-bar--rtl":p}),x=i(c)?c({rtl:p,type:o,defaultClassName:v}):a(v,c);return n.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":h},n.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${g} Toastify__progress-bar--${o}`}),n.createElement("div",{role:"progressbar","aria-hidden":h?"true":"false","aria-label":"notification timer",className:x,style:y,[u&&m>=1?"onTransitionEnd":"onAnimationEnd"]:u&&m<1?null:()=>{f&&r()}}))}let w=1,j=()=>""+w++;function _(e,t){return x(e,t),t.toastId}function I(e,t){return{...t,type:t&&t.type||e,toastId:t&&(o(t.toastId)||r(t.toastId))?t.toastId:j()}}function C(e){return(t,s)=>_(t,I(e,s))}function L(e,t){return _(e,I("default",t))}L.loading=(e,t)=>_(e,I("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...t})),L.promise=function(e,t,s){let n,{pending:a,error:r,success:l}=t;a&&(n=o(a)?L.loading(a,s):L.loading(a.render,{...s,...a}));let c={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},d=(e,t,a)=>{if(null==t)return void L.dismiss(n);let r={type:e,...c,...s,data:a},i=o(t)?{render:t}:t;return n?L.update(n,{...r,...i}):L(i.render,{...r,...i}),a},u=i(e)?e():e;return u.then(e=>d("success",l,e)).catch(e=>d("error",r,e)),u},L.success=C("success"),L.info=C("info"),L.error=C("error"),L.warning=C("warning"),L.warn=L.warning,L.dark=(e,t)=>_(e,I("default",{theme:"dark",...t})),L.dismiss=function(e){var t,s;y()?null==e||o(t=e)||r(t)?p.forEach(t=>{t.removeToast(e)}):e&&("containerId"in e||"id"in e)&&((null==(s=p.get(e.containerId))?void 0:s.removeToast(e.id))||p.forEach(t=>{t.removeToast(e.id)})):f=f.filter(t=>null!=e&&t.options.toastId!==e)},L.clearWaitingQueue=function(e){void 0===e&&(e={}),p.forEach(t=>{!t.props.limit||e.containerId&&t.id!==e.containerId||t.clearQueue()})},L.isActive=v,L.update=function(e,t){void 0===t&&(t={});let s=((e,t)=>{var s;let{containerId:n}=t;return null==(s=p.get(n||1))?void 0:s.toasts.get(e)})(e,t);if(s){let{props:n,content:a}=s,r={delay:100,...n,...t,toastId:t.toastId||e,updateId:j()};r.toastId!==e&&(r.staleId=e);let o=r.render||a;delete r.render,_(o,r)}},L.done=e=>{L.update(e,{progress:1})},L.onChange=function(e){return g.add(e),()=>{g.delete(e)}},L.play=e=>b(!0,e),L.pause=e=>b(!1,e);let k="undefined"!=typeof window?n.useLayoutEffect:n.useEffect,P=e=>{let{theme:t,type:s,isLoading:a,...r}=e;return n.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:"colored"===t?"currentColor":`var(--toastify-icon-color-${s})`,...r})},$={info:function(e){return n.createElement(P,{...e},n.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))},warning:function(e){return n.createElement(P,{...e},n.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))},success:function(e){return n.createElement(P,{...e},n.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))},error:function(e){return n.createElement(P,{...e},n.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))},spinner:function(){return n.createElement("div",{className:"Toastify__spinner"})}},B=e=>{let{isRunning:t,preventExitTransition:s,toastRef:r,eventHandlers:o,playToast:l}=N(e),{closeButton:c,children:d,autoClose:u,onClick:m,type:p,hideProgressBar:f,closeToast:g,transition:h,position:y,className:v,style:x,bodyClassName:b,bodyStyle:E,progressClassName:w,progressStyle:j,updateId:_,role:I,progress:C,rtl:L,toastId:k,deleteToast:P,isIn:B,isLoading:S,closeOnClick:A,theme:M}=e,O=a("Toastify__toast",`Toastify__toast-theme--${M}`,`Toastify__toast--${p}`,{"Toastify__toast--rtl":L},{"Toastify__toast--close-on-click":A}),D=i(v)?v({rtl:L,position:y,type:p,defaultClassName:O}):a(O,v),R=function(e){let{theme:t,type:s,isLoading:a,icon:r}=e,o=null,l={theme:t,type:s,isLoading:a};return!1===r||(i(r)?o=r(l):(0,n.isValidElement)(r)?o=(0,n.cloneElement)(r,l):a?o=$.spinner():s in $&&(o=$[s](l))),o}(e),z=!!C||!u,F={closeToast:g,type:p,theme:M},V=null;return!1===c||(V=i(c)?c(F):(0,n.isValidElement)(c)?(0,n.cloneElement)(c,F):function(e){let{closeToast:t,theme:s,ariaLabel:a="close"}=e;return n.createElement("button",{className:`Toastify__close-button Toastify__close-button--${s}`,type:"button",onClick:e=>{e.stopPropagation(),t(e)},"aria-label":a},n.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},n.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}(F)),n.createElement(h,{isIn:B,done:P,position:y,preventExitTransition:s,nodeRef:r,playToast:l},n.createElement("div",{id:k,onClick:m,"data-in":B,className:D,...o,style:x,ref:r},n.createElement("div",{...B&&{role:I},className:i(b)?b({type:p}):a("Toastify__toast-body",b),style:E},null!=R&&n.createElement("div",{className:a("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!S})},R),n.createElement("div",null,d)),V,n.createElement(T,{..._&&!z?{key:`pb-${_}`}:{},rtl:L,theme:M,delay:u,isRunning:t,isIn:B,closeToast:g,hide:f,type:p,style:j,className:w,controlledProgress:z,progress:C||0})))},S=function(e,t){return void 0===t&&(t=!1),{enter:`Toastify--animate Toastify__${e}-enter`,exit:`Toastify--animate Toastify__${e}-exit`,appendPosition:t}},A=u(S("bounce",!0)),M=u(S("slide",!0)),O=u(S("zoom")),D=u(S("flip")),R={position:"top-right",transition:A,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light"};function z(e){let t={...R,...e},s=e.stacked,[r,o]=(0,n.useState)(!0),c=(0,n.useRef)(null),{getToastToRender:d,isToastActive:u,count:m}=E(t),{className:p,style:f,rtl:g,containerId:h}=t;function y(){s&&(o(!0),L.play())}return k(()=>{if(s){var e;let s=c.current.querySelectorAll('[data-in="true"]'),n=null==(e=t.position)?void 0:e.includes("top"),a=0,o=0;Array.from(s).reverse().forEach((e,t)=>{e.classList.add("Toastify__toast--stacked"),t>0&&(e.dataset.collapsed=`${r}`),e.dataset.pos||(e.dataset.pos=n?"top":"bot");let s=a*(r?.2:1)+(r?0:12*t);e.style.setProperty("--y",`${n?s:-1*s}px`),e.style.setProperty("--g","12"),e.style.setProperty("--s",""+(1-(r?o:0))),a+=e.offsetHeight,o+=.025})}},[r,m,s]),n.createElement("div",{ref:c,className:"Toastify",id:h,onMouseEnter:()=>{s&&(o(!1),L.pause())},onMouseLeave:y},d((e,t)=>{let r=t.length?{...f}:{...f,pointerEvents:"none"};return n.createElement("div",{className:function(e){let t=a("Toastify__toast-container",`Toastify__toast-container--${e}`,{"Toastify__toast-container--rtl":g});return i(p)?p({position:e,rtl:g,defaultClassName:t}):a(t,l(p))}(e),style:r,key:`container-${e}`},t.map(e=>{let{content:t,props:a}=e;return n.createElement(B,{...a,stacked:s,collapseAll:y,isIn:u(a.toastId,a.containerId),style:a.style,key:`toast-${a.key}`},t)}))}))}}},function(e){e.O(0,[580,971,938,744],function(){return e(e.s=9317)}),_N_E=e.O()}]);
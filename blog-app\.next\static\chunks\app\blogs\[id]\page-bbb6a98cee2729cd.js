(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[429],{1707:function(e,t,a){Promise.resolve().then(a.bind(a,7386))},7386:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return x}});var s=a(7437),r=a(4257),o=a(3637),n=a(2173),l=a(6691),i=a.n(l),c=a(1396),d=a.n(c),u=a(2265),m=a(7948);a(8062);var g=a(4033),p=a(3799);let h=e=>(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",...e,children:[(0,s.jsx)("line",{x1:"5",y1:"12",x2:"19",y2:"12"}),(0,s.jsx)("polyline",{points:"12 5 19 12 12 19"})]});var f=e=>{let{blogs:t,currentBlogId:a}=e,r=t.filter(e=>e._id!==a).slice(0,12),[o,n]=(0,u.useState)(0),[l,c]=(0,u.useState)(!1),m=(0,u.useRef)(null),g=(0,u.useRef)(null),[p,f]=(0,u.useState)(4),[x,v]=(0,u.useState)(80);(0,u.useEffect)(()=>{let e=()=>{window.innerWidth<640?(f(1),v(40)):window.innerWidth<768?(f(2),v(60)):window.innerWidth<1024?(f(3),v(70)):(f(4),v(80))};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,u.useEffect)(()=>{if(!(r.length<=p))return g.current=setTimeout(()=>{l||b()},2e3),()=>{g.current&&clearTimeout(g.current)}},[o,r.length,p,l]);let y=()=>{if(!m.current)return"".concat(100/r.length,"%");let e=(m.current.offsetWidth-x)/p;return"".concat(e,"px")},b=()=>{l||r.length<=p||(c(!0),n(e=>e===r.length-p?0:e+1),setTimeout(()=>{c(!1)},300))},w=()=>{l||r.length<=p||(c(!0),n(e=>0===e?r.length-p:e-1),setTimeout(()=>{c(!1)},300))},j=e=>{g.current&&clearTimeout(g.current),e(),g.current=setTimeout(()=>{b()},2e3)};return 0===r.length?null:(0,s.jsxs)("div",{className:"my-12 border-t border-gray-200 pt-10",children:[(0,s.jsx)("h2",{className:"text-xl sm:text-2xl font-bold mb-6 text-center",children:"Trending Articles"}),(0,s.jsxs)("div",{className:"relative px-4",children:[r.length>p&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{onClick:()=>j(w),className:"absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full p-2 shadow-md","aria-label":"Previous",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),(0,s.jsx)("button",{onClick:()=>j(b),className:"absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full p-2 shadow-md","aria-label":"Next",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),(0,s.jsx)("div",{className:"overflow-hidden",ref:m,style:{paddingRight:"".concat(x,"px")},children:(0,s.jsx)("div",{className:"flex transition-transform duration-300 ease-in-out",style:{transform:"translateX(-".concat((()=>{if(!m.current)return"".concat(100/p*o,"%");let e=(m.current.offsetWidth-x)/p;return"".concat(o*e,"px")})(),")")},children:r.map(e=>(0,s.jsx)("div",{className:"pr-4 flex-shrink-0",style:{width:y()},children:(0,s.jsxs)("div",{className:"bg-white border border-gray-200 transition-all hover:shadow-md h-full",children:[(0,s.jsx)(d(),{href:"/blogs/".concat(e._id),children:(0,s.jsx)("div",{className:"relative h-36 border-b border-gray-200",children:(0,s.jsx)(i(),{src:e.image,alt:e.title,fill:!0,className:"object-cover"})})}),(0,s.jsxs)("div",{className:"p-3",children:[(0,s.jsx)("p",{className:"px-1.5 py-0.5 mb-1.5 inline-block bg-gray-100 text-gray-800 text-xs",children:e.category}),(0,s.jsx)("h3",{className:"text-sm font-medium mb-1.5 line-clamp-2",children:e.title}),(0,s.jsxs)(d(),{href:"/blogs/".concat(e._id),className:"inline-flex items-center text-xs font-semibold text-gray-700",children:["Read more ",(0,s.jsx)(h,{className:"ml-1 w-3 h-3"})]})]})]})},e._id))})}),r.length>p&&(0,s.jsx)("div",{className:"flex justify-center mt-4",children:Array.from({length:r.length-p+1}).map((e,t)=>(0,s.jsx)("button",{onClick:()=>j(()=>{c(!0),n(t),setTimeout(()=>c(!1),500)}),className:"h-2 w-2 mx-1 rounded-full ".concat(o===t?"bg-gray-800":"bg-gray-300"),"aria-label":"Go to slide ".concat(t+1)},t))})]})]})};function x(e){let{params:t}=e,a=(0,g.useRouter)(),[l,c]=(0,u.useState)(null),[h,x]=(0,u.useState)(!1),[v,y]=(0,u.useState)({email:"",password:""}),[b,w]=(0,u.useState)(!1),[j,N]=(0,u.useState)({email:"",password:"",confirmPassword:"",role:"user"}),[k,C]=(0,u.useState)(!1),[E,T]=(0,u.useState)(""),[I,L]=(0,u.useState)(!1),[_,S]=(0,u.useState)("/default_profile.png"),[P,A]=(0,u.useState)(""),[B,R]=(0,u.useState)(!1),[z,M]=(0,u.useState)(!1),[$,O]=(0,u.useState)(!1),[D,F]=(0,u.useState)(!1),[Z,H]=(0,u.useState)(!1),[W,V]=(0,u.useState)(!1),[q,X]=(0,u.useState)(!1),[Y,G]=(0,u.useState)(0),[Q,U]=(0,u.useState)([]),K=e=>{y({...v,[e.target.name]:e.target.value})},J=e=>{N({...j,[e.target.name]:e.target.value})},ee=async e=>{e.preventDefault();try{let e=await n.Z.post("/api/auth",{email:v.email,password:v.password});e.data.success?(localStorage.setItem("authToken",e.data.token||"dummy-token"),localStorage.setItem("userRole",e.data.user.role),localStorage.setItem("userId",e.data.user.id),Z?(localStorage.setItem("rememberedEmail",v.email),localStorage.setItem("rememberedPassword",v.password),localStorage.setItem("rememberMe","true")):(localStorage.removeItem("rememberedEmail"),localStorage.removeItem("rememberedPassword"),localStorage.removeItem("rememberMe")),"admin"===e.data.user.role?(m.toast.success("Login successful"),window.location.href="/admin"):(m.toast.success("Login successful"),window.location.href="/")):m.toast.error("Invalid credentials")}catch(e){var t,a;console.error("Login error:",e),m.toast.error((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"Login failed")}},et=async e=>{if(e.preventDefault(),j.password!==j.confirmPassword){m.toast.error("Passwords do not match");return}try{let e=await n.Z.post("/api/register",{email:j.email,password:j.password,role:j.role});e.data.success?(m.toast.success("Registration successful! Please login."),w(!1),y({...v,email:j.email}),N({email:"",password:"",confirmPassword:"",role:"user"})):m.toast.error(e.data.message||"Registration failed")}catch(e){var t,a;console.error("Registration error:",e),m.toast.error((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"Registration failed")}},ea=()=>{w(!b)},es=async()=>{c((await n.Z.get("/api/blog",{params:{id:t.id}})).data)};(0,u.useEffect)(()=>{es()},[]),(0,u.useEffect)(()=>{let e=localStorage.getItem("rememberedEmail"),t=localStorage.getItem("rememberedPassword"),a="true"===localStorage.getItem("rememberMe");e&&t&&a&&(y({email:e,password:t}),H(!0));let s=localStorage.getItem("authToken"),r=localStorage.getItem("userRole"),o=localStorage.getItem("userProfilePicture"),n=localStorage.getItem("userName");s&&(C(!0),T(r||"user"),o&&S(o),n&&A(n))},[]);let er=async e=>{try{let a=await n.Z.get("/api/favorites?blogId=".concat(t.id),{headers:{Authorization:"Bearer ".concat(e)}});a.data.success&&M(a.data.isFavorite)}catch(e){console.error("Error checking favorite status:",e)}},eo=async()=>{if(!k){x(!0);return}try{O(!0);let e=localStorage.getItem("authToken");console.log("Using token:",e),z?(await n.Z.delete("/api/favorites?blogId=".concat(t.id),{headers:{Authorization:"Bearer ".concat(e)}})).data.success&&(M(!1),m.toast.success("Removed from favorites")):(await n.Z.post("/api/favorites",{blogId:t.id},{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}})).data.success&&(M(!0),m.toast.success("Added to favorites"))}catch(t){var e;console.error("Error toggling favorite:",t),(null===(e=t.response)||void 0===e?void 0:e.status)===401?(m.toast.error("Please log in again to continue"),localStorage.removeItem("authToken"),C(!1),x(!0)):m.toast.error("Failed to update favorites")}finally{O(!1)}},en=async e=>{try{let a=await n.Z.get("/api/likes?blogId=".concat(t.id),{headers:{Authorization:"Bearer ".concat(e)}});a.data.success&&V(a.data.isLiked)}catch(e){console.error("Error checking like status:",e)}},el=async()=>{try{let e=await n.Z.get("/api/blog/likes?id=".concat(t.id));e.data.success&&G(e.data.count)}catch(e){console.error("Error fetching likes count:",e)}},ei=async()=>{try{let e=await n.Z.get("/api/blog/trending");e.data.success&&U(e.data.blogs)}catch(e){console.error("Error fetching trending blogs:",e)}},ec=async()=>{if(!k){x(!0);return}try{X(!0);let e=localStorage.getItem("authToken");W?(await n.Z.delete("/api/likes?blogId=".concat(t.id),{headers:{Authorization:"Bearer ".concat(e)}})).data.success&&(V(!1),G(e=>Math.max(0,e-1)),m.toast.success("Like removed")):(await n.Z.post("/api/likes",{blogId:t.id},{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}})).data.success&&(V(!0),G(e=>e+1),m.toast.success("Post liked"))}catch(t){var e;console.error("Error toggling like:",t),(null===(e=t.response)||void 0===e?void 0:e.status)===401?(m.toast.error("Please log in again to continue"),localStorage.removeItem("authToken"),C(!1),x(!0)):m.toast.error("Failed to update like")}finally{X(!1)}};return(0,u.useEffect)(()=>{let e=localStorage.getItem("authToken");e&&(C(!0),er(e),en(e)),el(),ei()},[]),(0,u.useEffect)(()=>{l&&l._id&&(0,p.Z0)("/blogs/".concat(t.id),"blog",l._id)},[l,t.id]),l?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(m.ToastContainer,{position:"top-center",autoClose:3e3}),(0,s.jsxs)("div",{className:"bg-gray-200 py-5 px-5 md:px-12 lg:px-28",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(d(),{href:"/",children:(0,s.jsx)(i(),{src:r.L.logo,width:180,alt:"",className:"w-[130px] sm:w-auto"})}),(0,s.jsx)("div",{className:"flex gap-3",children:k?(0,s.jsx)("div",{className:"relative",children:(0,s.jsxs)("button",{onClick:()=>a.push("/profile"),className:"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]",children:[(0,s.jsx)(i(),{src:_,width:24,height:24,alt:"Account",className:"w-6 h-6 rounded-full object-cover"}),(0,s.jsx)("span",{children:P||"Account"})]})}):(0,s.jsxs)("button",{onClick:()=>x(!0),className:"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]",children:["Get started ",(0,s.jsx)(i(),{src:r.L.arrow,alt:""})]})})]}),(0,s.jsxs)("div",{className:"text-center my-24",children:[(0,s.jsx)("h1",{className:"text-2xl sm:text-5xl font-semibold max-w-[700px] mx-auto",children:l.title}),(0,s.jsx)("div",{className:"flex justify-center mt-4",children:(0,s.jsxs)("button",{onClick:eo,disabled:$,className:"flex items-center gap-2 px-4 py-2 rounded-full ".concat(z?"bg-yellow-100 text-yellow-700 border border-yellow-300":"bg-gray-100 text-gray-700 border border-gray-300"),children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:z?"currentColor":"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,s.jsx)("polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"})}),z?"Favorited":"Add to Favorites"]})}),(0,s.jsx)(i(),{className:"mx-auto mt-6 rounded-full object-cover w-16 h-16 border-2 border-white",src:l.authorImg,width:60,height:60,alt:""}),(0,s.jsx)("p",{className:"mt-1 pb-2 text-lg max-w-[740px] mx-auto",children:l.author})]})]}),(0,s.jsxs)("div",{className:"mx-5 max-w-[800px] md:mx-auto mt-[-100px] mb-10",children:[(0,s.jsx)(i(),{className:"border-4 border-white",src:l.image,width:800,height:480,alt:""}),(0,s.jsx)("div",{className:"blog-content",dangerouslySetInnerHTML:{__html:(e=>{if(!e)return"";let t=e;return(t=t.replace(/\[\[([a-f\d]{24})\|([^\]]+)\]\]/g,(e,t,a)=>'<a href="/blogs/'.concat(t,'" class="text-blue-600 hover:underline">').concat(a,"</a>"))).replace(/\{\{image:([^|]+)\|([^}]+)\}\}/g,(e,t,a)=>'<div class="my-6 text-center">\n        <img src="'.concat(t,'" alt="').concat(a,'" class="max-w-full h-auto mx-auto rounded-lg shadow-md" style="max-height: 400px;" />\n      </div>'))})(l.description)}}),(0,s.jsx)("div",{className:"mt-8 flex items-center",children:(0,s.jsxs)("button",{onClick:ec,disabled:q,className:"flex items-center gap-2 px-4 py-2 rounded-full transition-all ".concat(W?"bg-red-100 text-red-600 border border-red-300":"bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200"),children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:W?"currentColor":"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,s.jsx)("path",{d:"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"})}),W?"Liked":"Like"]})}),(0,s.jsxs)("div",{className:"my-24",children:[(0,s.jsx)("p",{className:"text-black font font-semibold my-4",children:"Share this article on social media"}),(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("button",{onClick:()=>{window.location.href="/"},className:"flex items-center justify-center w-[40px] h-[40px] bg-white rounded-full mr-3 shadow-lg transition-all border border-gray-200 hover:shadow-2xl",title:"Share on Facebook",children:(0,s.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M22.675 0h-21.35C.595 0 0 .592 0 1.326v21.348C0 23.406.595 24 1.325 24h11.495v-9.294H9.692v-3.622h3.128V8.413c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.797.143v3.24l-1.918.001c-1.504 0-1.797.715-1.797 1.763v2.313h3.587l-.467 3.622h-3.12V24h6.116C23.406 24 24 23.406 24 22.674V1.326C24 .592 23.406 0 22.675 0"})})}),(0,s.jsx)("button",{onClick:()=>{window.location.href="/"},className:"flex items-center justify-center w-[40px] h-[40px] bg-white rounded-full mr-3 shadow-lg transition-all border border-gray-200 hover:shadow-2xl",title:"Share on Twitter",children:(0,s.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M24 4.557a9.83 9.83 0 0 1-2.828.775 4.932 4.932 0 0 0 2.165-2.724c-.951.564-2.005.974-3.127 1.195a4.916 4.916 0 0 0-8.38 4.482C7.691 8.095 4.066 6.13 1.64 3.161c-.542.929-.856 2.01-.857 3.17 0 2.188 1.115 4.116 2.823 5.247a4.904 4.904 0 0 1-2.229-.616c-.054 2.281 1.581 4.415 3.949 4.89a4.936 4.936 0 0 1-2.224.084c.627 1.956 2.444 3.377 4.6 3.417A9.867 9.867 0 0 1 0 21.543a13.94 13.94 0 0 0 7.548 2.209c9.057 0 14.009-7.496 14.009-13.986 0-.21 0-.423-.016-.634A9.936 9.936 0 0 0 24 4.557z"})})}),(0,s.jsx)("button",{onClick:()=>{window.location.href="/"},className:"flex items-center justify-center w-[40px] h-[40px] bg-white rounded-full shadow-lg transition-all border border-gray-200 hover:shadow-2xl",title:"Share on Google Plus",children:(0,s.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 48 48",fill:"black",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("circle",{cx:"24",cy:"24",r:"24",fill:"black"}),(0,s.jsx)("text",{x:"13",y:"32","font-size":"18","font-family":"Arial, Helvetica, sans-serif",fill:"white","font-weight":"bold",children:"G+"})]})}),(0,s.jsx)("button",{onClick:()=>{try{let e=document.createElement("input");e.value=window.location.href,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e),m.toast.success("Link copied to clipboard!",{position:"top-center",autoClose:3e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0,progress:void 0}),console.log("Link copied to clipboard!")}catch(e){console.error("Copy failed:",e),m.toast.error("Failed to copy link")}},className:"flex items-center justify-center w-[40px] h-[40px] bg-white rounded-full ml-3 shadow-lg transition-all border border-gray-200 hover:shadow-2xl",title:"Copy link to clipboard",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,s.jsx)("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"}),(0,s.jsx)("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"})]})})]})]})]}),(0,s.jsx)(f,{blogs:Q,currentBlogId:t.id}),(0,s.jsx)(o.Z,{}),B&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white p-6 rounded-md shadow-lg max-w-sm w-full",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Confirm Logout"}),(0,s.jsx)("p",{className:"mb-6",children:"Are you sure you want to log out? You will need to log in again to access your account."}),(0,s.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,s.jsx)("button",{onClick:()=>{R(!1)},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"}),(0,s.jsx)("button",{onClick:()=>{localStorage.removeItem("authToken"),localStorage.removeItem("userRole"),C(!1),T(""),R(!1),m.toast.success("Logged out successfully")},className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",children:"Logout"})]})]})}),h&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white p-8 rounded-md shadow-lg w-96",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold mb-4",children:b?"Register":"Login"}),b?(0,s.jsxs)("form",{onSubmit:et,children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-gray-700 mb-2",children:"Email"}),(0,s.jsx)("input",{type:"email",name:"email",value:j.email,onChange:J,className:"w-full px-3 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-gray-700 mb-2",children:"Password"}),(0,s.jsx)("input",{type:"password",name:"password",value:j.password,onChange:J,className:"w-full px-3 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-gray-700 mb-2",children:"Confirm Password"}),(0,s.jsx)("input",{type:"password",name:"confirmPassword",value:j.confirmPassword,onChange:J,className:"w-full px-3 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("button",{type:"submit",className:"bg-black text-white px-4 py-2 rounded-md",children:"Register"}),(0,s.jsx)("button",{type:"button",onClick:()=>x(!1),className:"text-gray-600 px-4 py-2",children:"Cancel"})]}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,s.jsx)("button",{type:"button",onClick:ea,className:"text-blue-600 hover:underline",children:"Login"})]})})]}):(0,s.jsxs)("form",{onSubmit:ee,children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-gray-700 mb-2",children:"Email"}),(0,s.jsx)("input",{type:"email",name:"email",value:v.email,onChange:K,className:"w-full px-3 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-gray-700 mb-2",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:D?"text":"password",name:"password",value:v.password,onChange:K,className:"w-full px-3 py-2 border border-gray-300 rounded-md pr-10",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>{F(!D)},className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600",children:D?(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,s.jsx)("path",{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}),(0,s.jsx)("path",{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"})]}):(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,s.jsx)("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),(0,s.jsx)("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]})})]})]}),(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:Z,onChange:e=>H(e.target.checked),className:"mr-2"}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"Remember me"})]})}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("button",{type:"submit",className:"bg-black text-white px-4 py-2 rounded-md",children:"Login"}),(0,s.jsx)("button",{type:"button",onClick:()=>x(!1),className:"text-gray-600 px-4 py-2",children:"Cancel"})]}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,s.jsx)("button",{type:"button",onClick:ea,className:"text-blue-600 hover:underline",children:"Register"})]})})]})]})})]}):null}},3799:function(e,t,a){"use strict";a.d(t,{Z0:function(){return r},Z5:function(){return n},uf:function(){return o}});var s=a(2173);let r=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"page",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;try{console.log("Tracking page view:",{path:e,contentType:t,blogId:a});let r=document.referrer||null,o=await s.Z.post("/api/analytics",{path:e,contentType:t,blogId:a,referrer:r});console.log("Analytics tracking response:",o.data)}catch(e){console.error("Analytics error:",e)}},o=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),n=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"7days",t=localStorage.getItem("authToken");if(!t)throw Error("Authentication required");return(await s.Z.get("/api/analytics?period=".concat(e),{headers:{Authorization:"Bearer ".concat(t)}})).data}},8062:function(){},4033:function(e,t,a){e.exports=a(5313)},7948:function(e,t,a){"use strict";a.r(t),a.d(t,{Bounce:function(){return R},Flip:function(){return $},Icons:function(){return P},Slide:function(){return z},ToastContainer:function(){return D},Zoom:function(){return M},collapseToast:function(){return d},cssTransition:function(){return u},toast:function(){return L},useToast:function(){return j},useToastContainer:function(){return w}});var s=a(2265),r=function(){for(var e,t,a=0,s="",r=arguments.length;a<r;a++)(e=arguments[a])&&(t=function e(t){var a,s,r="";if("string"==typeof t||"number"==typeof t)r+=t;else if("object"==typeof t){if(Array.isArray(t)){var o=t.length;for(a=0;a<o;a++)t[a]&&(s=e(t[a]))&&(r&&(r+=" "),r+=s)}else for(s in t)t[s]&&(r&&(r+=" "),r+=s)}return r}(e))&&(s&&(s+=" "),s+=t);return s};let o=e=>"number"==typeof e&&!isNaN(e),n=e=>"string"==typeof e,l=e=>"function"==typeof e,i=e=>n(e)||l(e)?e:null,c=e=>(0,s.isValidElement)(e)||n(e)||l(e)||o(e);function d(e,t,a){void 0===a&&(a=300);let{scrollHeight:s,style:r}=e;requestAnimationFrame(()=>{r.minHeight="initial",r.height=s+"px",r.transition=`all ${a}ms`,requestAnimationFrame(()=>{r.height="0",r.padding="0",r.margin="0",setTimeout(t,a)})})}function u(e){let{enter:t,exit:a,appendPosition:r=!1,collapse:o=!0,collapseDuration:n=300}=e;return function(e){let{children:l,position:i,preventExitTransition:c,done:u,nodeRef:m,isIn:g,playToast:p}=e,h=r?`${t}--${i}`:t,f=r?`${a}--${i}`:a,x=(0,s.useRef)(0);return(0,s.useLayoutEffect)(()=>{let e=m.current,t=h.split(" "),a=s=>{s.target===m.current&&(p(),e.removeEventListener("animationend",a),e.removeEventListener("animationcancel",a),0===x.current&&"animationcancel"!==s.type&&e.classList.remove(...t))};e.classList.add(...t),e.addEventListener("animationend",a),e.addEventListener("animationcancel",a)},[]),(0,s.useEffect)(()=>{let e=m.current,t=()=>{e.removeEventListener("animationend",t),o?d(e,u,n):u()};g||(c?t():(x.current=1,e.className+=` ${f}`,e.addEventListener("animationend",t)))},[g]),s.createElement(s.Fragment,null,l)}}function m(e,t){return null!=e?{content:e.content,containerId:e.props.containerId,id:e.props.toastId,theme:e.props.theme,type:e.props.type,data:e.props.data||{},isLoading:e.props.isLoading,icon:e.props.icon,status:t}:{}}let g=new Map,p=[],h=new Set,f=e=>h.forEach(t=>t(e)),x=()=>g.size>0;function v(e,t){var a;if(t)return!(null==(a=g.get(t))||!a.isToastActive(e));let s=!1;return g.forEach(t=>{t.isToastActive(e)&&(s=!0)}),s}function y(e,t){c(e)&&(x()||p.push({content:e,options:t}),g.forEach(a=>{a.buildToast(e,t)}))}function b(e,t){g.forEach(a=>{null!=t&&null!=t&&t.containerId?(null==t?void 0:t.containerId)===a.id&&a.toggle(e,null==t?void 0:t.id):a.toggle(e,null==t?void 0:t.id)})}function w(e){let{subscribe:t,getSnapshot:a,setProps:r}=(0,s.useRef)(function(e){let t=e.containerId||1;return{subscribe(a){let r=function(e,t,a){let r=1,d=0,u=[],g=[],p=[],h=t,f=new Map,x=new Set,v=()=>{p=Array.from(f.values()),x.forEach(e=>e())},y=e=>{g=null==e?[]:g.filter(t=>t!==e),v()},b=e=>{let{toastId:t,onOpen:r,updateId:o,children:n}=e.props,i=null==o;e.staleId&&f.delete(e.staleId),f.set(t,e),g=[...g,e.props.toastId].filter(t=>t!==e.staleId),v(),a(m(e,i?"added":"updated")),i&&l(r)&&r((0,s.isValidElement)(n)&&n.props)};return{id:e,props:h,observe:e=>(x.add(e),()=>x.delete(e)),toggle:(e,t)=>{f.forEach(a=>{null!=t&&t!==a.props.toastId||l(a.toggle)&&a.toggle(e)})},removeToast:y,toasts:f,clearQueue:()=>{d-=u.length,u=[]},buildToast:(t,g)=>{var p,x;if((t=>{let{containerId:a,toastId:s,updateId:r}=t,o=f.has(s)&&null==r;return(a?a!==e:1!==e)||o})(g))return;let{toastId:w,updateId:j,data:N,staleId:k,delay:C}=g,E=()=>{y(w)},T=null==j;T&&d++;let I={...h,style:h.toastStyle,key:r++,...Object.fromEntries(Object.entries(g).filter(e=>{let[t,a]=e;return null!=a})),toastId:w,updateId:j,data:N,closeToast:E,isIn:!1,className:i(g.className||h.toastClassName),bodyClassName:i(g.bodyClassName||h.bodyClassName),progressClassName:i(g.progressClassName||h.progressClassName),autoClose:!g.isLoading&&(p=g.autoClose,x=h.autoClose,!1===p||o(p)&&p>0?p:x),deleteToast(){let e=f.get(w),{onClose:t,children:r}=e.props;l(t)&&t((0,s.isValidElement)(r)&&r.props),a(m(e,"removed")),f.delete(w),--d<0&&(d=0),u.length>0?b(u.shift()):v()}};I.closeButton=h.closeButton,!1===g.closeButton||c(g.closeButton)?I.closeButton=g.closeButton:!0===g.closeButton&&(I.closeButton=!c(h.closeButton)||h.closeButton);let L=t;(0,s.isValidElement)(t)&&!n(t.type)?L=(0,s.cloneElement)(t,{closeToast:E,toastProps:I,data:N}):l(t)&&(L=t({closeToast:E,toastProps:I,data:N}));let _={content:L,props:I,staleId:k};h.limit&&h.limit>0&&d>h.limit&&T?u.push(_):o(C)?setTimeout(()=>{b(_)},C):b(_)},setProps(e){h=e},setToggle:(e,t)=>{f.get(e).toggle=t},isToastActive:e=>g.some(t=>t===e),getSnapshot:()=>h.newestOnTop?p.reverse():p}}(t,e,f);g.set(t,r);let d=r.observe(a);return p.forEach(e=>y(e.content,e.options)),p=[],()=>{d(),g.delete(t)}},setProps(e){var a;null==(a=g.get(t))||a.setProps(e)},getSnapshot(){var e;return null==(e=g.get(t))?void 0:e.getSnapshot()}}}(e)).current;r(e);let d=(0,s.useSyncExternalStore)(t,a,a);return{getToastToRender:function(e){if(!d)return[];let t=new Map;return d.forEach(e=>{let{position:a}=e.props;t.has(a)||t.set(a,[]),t.get(a).push(e)}),Array.from(t,t=>e(t[0],t[1]))},isToastActive:v,count:null==d?void 0:d.length}}function j(e){var t,a;let[r,o]=(0,s.useState)(!1),[n,l]=(0,s.useState)(!1),i=(0,s.useRef)(null),c=(0,s.useRef)({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:d,pauseOnHover:u,closeToast:m,onClick:p,closeOnClick:h}=e;function f(){o(!0)}function x(){o(!1)}function v(t){let a=i.current;c.canDrag&&a&&(c.didMove=!0,r&&x(),c.delta="x"===e.draggableDirection?t.clientX-c.start:t.clientY-c.start,c.start!==t.clientX&&(c.canCloseOnClick=!1),a.style.transform=`translate3d(${"x"===e.draggableDirection?`${c.delta}px, var(--y)`:`0, calc(${c.delta}px + var(--y))`},0)`,a.style.opacity=""+(1-Math.abs(c.delta/c.removalDistance)))}function y(){document.removeEventListener("pointermove",v),document.removeEventListener("pointerup",y);let t=i.current;if(c.canDrag&&c.didMove&&t){if(c.canDrag=!1,Math.abs(c.delta)>c.removalDistance)return l(!0),e.closeToast(),void e.collapseAll();t.style.transition="transform 0.2s, opacity 0.2s",t.style.removeProperty("transform"),t.style.removeProperty("opacity")}}null==(a=g.get((t={id:e.toastId,containerId:e.containerId,fn:o}).containerId||1))||a.setToggle(t.id,t.fn),(0,s.useEffect)(()=>{if(e.pauseOnFocusLoss)return document.hasFocus()||x(),window.addEventListener("focus",f),window.addEventListener("blur",x),()=>{window.removeEventListener("focus",f),window.removeEventListener("blur",x)}},[e.pauseOnFocusLoss]);let b={onPointerDown:function(t){if(!0===e.draggable||e.draggable===t.pointerType){c.didMove=!1,document.addEventListener("pointermove",v),document.addEventListener("pointerup",y);let a=i.current;c.canCloseOnClick=!0,c.canDrag=!0,a.style.transition="none","x"===e.draggableDirection?(c.start=t.clientX,c.removalDistance=a.offsetWidth*(e.draggablePercent/100)):(c.start=t.clientY,c.removalDistance=a.offsetHeight*(80===e.draggablePercent?1.5*e.draggablePercent:e.draggablePercent)/100)}},onPointerUp:function(t){let{top:a,bottom:s,left:r,right:o}=i.current.getBoundingClientRect();"touchend"!==t.nativeEvent.type&&e.pauseOnHover&&t.clientX>=r&&t.clientX<=o&&t.clientY>=a&&t.clientY<=s?x():f()}};return d&&u&&(b.onMouseEnter=x,e.stacked||(b.onMouseLeave=f)),h&&(b.onClick=e=>{p&&p(e),c.canCloseOnClick&&m()}),{playToast:f,pauseToast:x,isRunning:r,preventExitTransition:n,toastRef:i,eventHandlers:b}}function N(e){let{delay:t,isRunning:a,closeToast:o,type:n="default",hide:i,className:c,style:d,controlledProgress:u,progress:m,rtl:g,isIn:p,theme:h}=e,f=i||u&&0===m,x={...d,animationDuration:`${t}ms`,animationPlayState:a?"running":"paused"};u&&(x.transform=`scaleX(${m})`);let v=r("Toastify__progress-bar",u?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${h}`,`Toastify__progress-bar--${n}`,{"Toastify__progress-bar--rtl":g}),y=l(c)?c({rtl:g,type:n,defaultClassName:v}):r(v,c);return s.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":f},s.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${h} Toastify__progress-bar--${n}`}),s.createElement("div",{role:"progressbar","aria-hidden":f?"true":"false","aria-label":"notification timer",className:y,style:x,[u&&m>=1?"onTransitionEnd":"onAnimationEnd"]:u&&m<1?null:()=>{p&&o()}}))}let k=1,C=()=>""+k++;function E(e,t){return y(e,t),t.toastId}function T(e,t){return{...t,type:t&&t.type||e,toastId:t&&(n(t.toastId)||o(t.toastId))?t.toastId:C()}}function I(e){return(t,a)=>E(t,T(e,a))}function L(e,t){return E(e,T("default",t))}L.loading=(e,t)=>E(e,T("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...t})),L.promise=function(e,t,a){let s,{pending:r,error:o,success:i}=t;r&&(s=n(r)?L.loading(r,a):L.loading(r.render,{...a,...r}));let c={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},d=(e,t,r)=>{if(null==t)return void L.dismiss(s);let o={type:e,...c,...a,data:r},l=n(t)?{render:t}:t;return s?L.update(s,{...o,...l}):L(l.render,{...o,...l}),r},u=l(e)?e():e;return u.then(e=>d("success",i,e)).catch(e=>d("error",o,e)),u},L.success=I("success"),L.info=I("info"),L.error=I("error"),L.warning=I("warning"),L.warn=L.warning,L.dark=(e,t)=>E(e,T("default",{theme:"dark",...t})),L.dismiss=function(e){var t,a;x()?null==e||n(t=e)||o(t)?g.forEach(t=>{t.removeToast(e)}):e&&("containerId"in e||"id"in e)&&((null==(a=g.get(e.containerId))?void 0:a.removeToast(e.id))||g.forEach(t=>{t.removeToast(e.id)})):p=p.filter(t=>null!=e&&t.options.toastId!==e)},L.clearWaitingQueue=function(e){void 0===e&&(e={}),g.forEach(t=>{!t.props.limit||e.containerId&&t.id!==e.containerId||t.clearQueue()})},L.isActive=v,L.update=function(e,t){void 0===t&&(t={});let a=((e,t)=>{var a;let{containerId:s}=t;return null==(a=g.get(s||1))?void 0:a.toasts.get(e)})(e,t);if(a){let{props:s,content:r}=a,o={delay:100,...s,...t,toastId:t.toastId||e,updateId:C()};o.toastId!==e&&(o.staleId=e);let n=o.render||r;delete o.render,E(n,o)}},L.done=e=>{L.update(e,{progress:1})},L.onChange=function(e){return h.add(e),()=>{h.delete(e)}},L.play=e=>b(!0,e),L.pause=e=>b(!1,e);let _="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,S=e=>{let{theme:t,type:a,isLoading:r,...o}=e;return s.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:"colored"===t?"currentColor":`var(--toastify-icon-color-${a})`,...o})},P={info:function(e){return s.createElement(S,{...e},s.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))},warning:function(e){return s.createElement(S,{...e},s.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))},success:function(e){return s.createElement(S,{...e},s.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))},error:function(e){return s.createElement(S,{...e},s.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))},spinner:function(){return s.createElement("div",{className:"Toastify__spinner"})}},A=e=>{let{isRunning:t,preventExitTransition:a,toastRef:o,eventHandlers:n,playToast:i}=j(e),{closeButton:c,children:d,autoClose:u,onClick:m,type:g,hideProgressBar:p,closeToast:h,transition:f,position:x,className:v,style:y,bodyClassName:b,bodyStyle:w,progressClassName:k,progressStyle:C,updateId:E,role:T,progress:I,rtl:L,toastId:_,deleteToast:S,isIn:A,isLoading:B,closeOnClick:R,theme:z}=e,M=r("Toastify__toast",`Toastify__toast-theme--${z}`,`Toastify__toast--${g}`,{"Toastify__toast--rtl":L},{"Toastify__toast--close-on-click":R}),$=l(v)?v({rtl:L,position:x,type:g,defaultClassName:M}):r(M,v),O=function(e){let{theme:t,type:a,isLoading:r,icon:o}=e,n=null,i={theme:t,type:a,isLoading:r};return!1===o||(l(o)?n=o(i):(0,s.isValidElement)(o)?n=(0,s.cloneElement)(o,i):r?n=P.spinner():a in P&&(n=P[a](i))),n}(e),D=!!I||!u,F={closeToast:h,type:g,theme:z},Z=null;return!1===c||(Z=l(c)?c(F):(0,s.isValidElement)(c)?(0,s.cloneElement)(c,F):function(e){let{closeToast:t,theme:a,ariaLabel:r="close"}=e;return s.createElement("button",{className:`Toastify__close-button Toastify__close-button--${a}`,type:"button",onClick:e=>{e.stopPropagation(),t(e)},"aria-label":r},s.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},s.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}(F)),s.createElement(f,{isIn:A,done:S,position:x,preventExitTransition:a,nodeRef:o,playToast:i},s.createElement("div",{id:_,onClick:m,"data-in":A,className:$,...n,style:y,ref:o},s.createElement("div",{...A&&{role:T},className:l(b)?b({type:g}):r("Toastify__toast-body",b),style:w},null!=O&&s.createElement("div",{className:r("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!B})},O),s.createElement("div",null,d)),Z,s.createElement(N,{...E&&!D?{key:`pb-${E}`}:{},rtl:L,theme:z,delay:u,isRunning:t,isIn:A,closeToast:h,hide:p,type:g,style:C,className:k,controlledProgress:D,progress:I||0})))},B=function(e,t){return void 0===t&&(t=!1),{enter:`Toastify--animate Toastify__${e}-enter`,exit:`Toastify--animate Toastify__${e}-exit`,appendPosition:t}},R=u(B("bounce",!0)),z=u(B("slide",!0)),M=u(B("zoom")),$=u(B("flip")),O={position:"top-right",transition:R,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light"};function D(e){let t={...O,...e},a=e.stacked,[o,n]=(0,s.useState)(!0),c=(0,s.useRef)(null),{getToastToRender:d,isToastActive:u,count:m}=w(t),{className:g,style:p,rtl:h,containerId:f}=t;function x(){a&&(n(!0),L.play())}return _(()=>{if(a){var e;let a=c.current.querySelectorAll('[data-in="true"]'),s=null==(e=t.position)?void 0:e.includes("top"),r=0,n=0;Array.from(a).reverse().forEach((e,t)=>{e.classList.add("Toastify__toast--stacked"),t>0&&(e.dataset.collapsed=`${o}`),e.dataset.pos||(e.dataset.pos=s?"top":"bot");let a=r*(o?.2:1)+(o?0:12*t);e.style.setProperty("--y",`${s?a:-1*a}px`),e.style.setProperty("--g","12"),e.style.setProperty("--s",""+(1-(o?n:0))),r+=e.offsetHeight,n+=.025})}},[o,m,a]),s.createElement("div",{ref:c,className:"Toastify",id:f,onMouseEnter:()=>{a&&(n(!1),L.pause())},onMouseLeave:x},d((e,t)=>{let o=t.length?{...p}:{...p,pointerEvents:"none"};return s.createElement("div",{className:function(e){let t=r("Toastify__toast-container",`Toastify__toast-container--${e}`,{"Toastify__toast-container--rtl":h});return l(g)?g({position:e,rtl:h,defaultClassName:t}):r(t,i(g))}(e),style:o,key:`container-${e}`},t.map(e=>{let{content:t,props:r}=e;return s.createElement(A,{...r,stacked:a,collapseAll:x,isIn:u(r.toastId,r.containerId),style:r.style,key:`toast-${r.key}`},t)}))}))}}},function(e){e.O(0,[580,691,396,637,971,938,744],function(){return e(e.s=1707)}),_N_E=e.O()}]);
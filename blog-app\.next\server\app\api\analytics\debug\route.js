"use strict";(()=>{var e={};e.id=4868,e.ids=[4868],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5872:(e,t,n)=>{n.r(t),n.d(t,{headerHooks:()=>y,originalPathname:()=>b,patchFetch:()=>x,requestAsyncStorage:()=>l,routeModule:()=>p,serverHooks:()=>m,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>h});var r={};n.r(r),n.d(r,{GET:()=>d});var a=n(95419),o=n(69108),s=n(99678),i=n(78070),c=n(91887),u=n(2299);async function d(){try{await (0,c.n)();let e=await u.Z.countDocuments(),t=await u.Z.find().sort({timestamp:-1}).limit(5),n=await u.Z.aggregate([{$group:{_id:"$contentType",count:{$sum:1}}}]);return i.Z.json({success:!0,totalRecords:e,recentRecords:t,countByType:n})}catch(e){return console.error("Error in analytics debug:",e),i.Z.json({success:!1,message:"Error checking analytics data"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/analytics/debug/route",pathname:"/api/analytics/debug",filename:"route",bundlePath:"app/api/analytics/debug/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\analytics\\debug\\route.js",nextConfigOutput:"",userland:r}),{requestAsyncStorage:l,staticGenerationAsyncStorage:g,serverHooks:m,headerHooks:y,staticGenerationBailout:h}=p,b="/api/analytics/debug/route";function x(){return(0,s.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:g})}},91887:(e,t,n)=>{n.d(t,{n:()=>o});var r=n(11185),a=n.n(r);let o=async()=>{try{if(a().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await a().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},2299:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(11185),a=n.n(r);let o=new(a()).Schema({path:{type:String,required:!0,index:!0},contentType:{type:String,required:!0,enum:["blog","page","home","other"],default:"other"},blogId:{type:a().Schema.Types.ObjectId,ref:"blogs",index:!0},userId:{type:a().Schema.Types.ObjectId,ref:"users",index:!0},ipHash:{type:String,index:!0},userAgent:String,referrer:String,timestamp:{type:Date,default:Date.now,index:!0}});o.index({path:1,timestamp:1}),o.index({contentType:1,timestamp:1}),o.index({blogId:1,timestamp:1});let s=a().models.analytics||a().model("analytics",o)},78070:(e,t,n)=>{Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return r.NextResponse}});let r=n(70457)}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[1638,2993],()=>n(5872));module.exports=r})();
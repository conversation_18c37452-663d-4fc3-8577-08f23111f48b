(()=>{var e={};e.id=3909,e.ids=[3909],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},62150:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=s(50482),a=s(69108),o=s(62563),l=s.n(o),i=s(68300),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);s.d(t,n);let c=["",{children:["login-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,35432)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\login-test\\page.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,39459)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\login-test\\page.jsx"],u="/login-test/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/login-test/page",pathname:"/login-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},83338:(e,t,s)=>{Promise.resolve().then(s.bind(s,97211)),Promise.resolve().then(s.bind(s,98419)),Promise.resolve().then(s.bind(s,69697))},80305:(e,t,s)=>{Promise.resolve().then(s.bind(s,2764))},21416:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},97211:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(95344),a=s(3729),o=s(69697),l=s(8014);let i=(e,t,s={})=>{l.Z.set(e,t,s)},n=e=>l.Z.get(e),c=()=>"accepted"===l.Z.get("cookie-consent"),d=()=>{c()},u=()=>{c()},m=()=>{let[e,t]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{let e=n("cookie-consent");if(e)"accepted"===e&&(d(),u());else{let e=setTimeout(()=>{t(!0)},1e4);return()=>clearTimeout(e)}},[]),e)?r.jsx("div",{className:"fixed bottom-0 left-0 right-0 bg-white shadow-lg z-50 border-t border-gray-200 p-4 md:p-6 animate-fade-in",children:r.jsx("div",{className:"max-w-6xl mx-auto",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("h3",{className:"text-lg font-semibold mb-2",children:"We use cookies"}),(0,r.jsxs)("p",{className:"text-gray-600 text-sm md:text-base",children:['We use cookies to enhance your browsing experience, serve personalized ads or content, and analyze our traffic. By clicking "Accept All", you consent to our use of cookies.',r.jsx("a",{href:"/privacy-policy",className:"text-blue-600 hover:underline ml-1",children:"Read our Cookie Policy"})]})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[r.jsx("button",{onClick:()=>{i("cookie-consent","declined",{expires:365}),t(!1),o.toast.info("Cookies declined. Some features may be limited.")},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 text-sm md:text-base",children:"Decline"}),r.jsx("button",{onClick:()=>{i("cookie-consent","accepted",{expires:365}),d(),u(),t(!1),o.toast.success("Cookie preferences saved")},className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 text-sm md:text-base",children:"Accept All"})]})]})})}):null}},98419:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(95344),a=s(3729),o=s(53608),l=s(69697),i=s(22254);let n=()=>{let[e,t]=(0,a.useState)(!1),[s,n]=(0,a.useState)(""),[c,d]=(0,a.useState)(!1),u=(0,i.usePathname)();(0,a.useEffect)(()=>{if(u&&u.startsWith("/admin")||"true"===localStorage.getItem("emailSubscribed")||"true"===localStorage.getItem("emailPopupPermanentlyDismissed"))return;let e=localStorage.getItem("emailPopupLastClosed"),s=parseInt(localStorage.getItem("emailPopupCloseCount")||"0"),r=Date.now();if(e&&s>=1&&r-parseInt(e)<3e5)return;let a=setTimeout(()=>{t(!0)},0===s?12e4:Math.max(0,3e5-(r-parseInt(e||"0"))));return()=>clearTimeout(a)},[u]);let m=async e=>{if(e.preventDefault(),!s){l.toast.error("Please enter your email address");return}try{d(!0);let e=new FormData;e.append("email",s),(await o.Z.post("/api/email",e)).data.success?(l.toast.success("Successfully subscribed to our newsletter!"),t(!1),n(""),localStorage.setItem("emailSubscribed","true"),localStorage.removeItem("emailPopupCloseCount"),localStorage.removeItem("emailPopupLastClosed"),localStorage.removeItem("emailPopupPermanentlyDismissed")):l.toast.error("Subscription failed. Please try again.")}catch(e){console.error("Subscription error:",e),l.toast.error("An error occurred. Please try again.")}finally{d(!1)}};return e?r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-2xl max-w-md w-full relative overflow-hidden",children:[r.jsx("button",{onClick:()=>{t(!1);let e=parseInt(localStorage.getItem("emailPopupCloseCount")||"0")+1;localStorage.setItem("emailPopupCloseCount",e.toString()),localStorage.setItem("emailPopupLastClosed",Date.now().toString()),e>=2&&localStorage.setItem("emailPopupPermanentlyDismissed","true")},className:"absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10","aria-label":"Close popup",children:r.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,r.jsxs)("div",{className:"p-8",children:[(0,r.jsxs)("div",{className:"text-center mb-6",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"SUBSCRIBE NOW"}),(0,r.jsxs)("p",{className:"text-gray-600 text-sm",children:["DON'T MISS OUT ON THE LATEST BLOG POSTS",r.jsx("br",{}),"AND OFFERS."]}),r.jsx("p",{className:"text-xs text-gray-500 mt-2",children:"Be the first to get notified."})]}),(0,r.jsxs)("form",{onSubmit:m,className:"space-y-4",children:[r.jsx("div",{children:r.jsx("input",{type:"email",value:s,onChange:e=>n(e.target.value),placeholder:"Email address",className:"w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent",required:!0})}),r.jsx("button",{type:"submit",disabled:c,className:"w-full bg-black text-white py-3 px-6 rounded-md hover:bg-gray-800 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:c?"SUBSCRIBING...":"SUBSCRIBE"})]}),r.jsx("p",{className:"text-xs text-gray-500 text-center mt-4",children:"You can unsubscribe at any time. We respect your privacy."})]})]})}):null}},2764:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(95344),a=s(3729),o=s(53608);let l=()=>{let[e,t]=(0,a.useState)(""),[s,l]=(0,a.useState)(""),[i,n]=(0,a.useState)(null),[c,d]=(0,a.useState)(!1),[u,m]=(0,a.useState)(!1),[p,x]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=localStorage.getItem("rememberedEmail"),s=localStorage.getItem("rememberedPassword"),r="true"===localStorage.getItem("rememberMe");e&&s&&r&&(t(e),l(s),x(!0))},[]);let g=async t=>{t.preventDefault();try{d(!0);let t=await o.Z.post("/api/auth",{email:e,password:s});n(t.data),t.data.success&&(localStorage.setItem("authToken",t.data.token),localStorage.setItem("userRole",t.data.user.role),localStorage.setItem("userId",t.data.user.id),p?(localStorage.setItem("rememberedEmail",e),localStorage.setItem("rememberedPassword",s),localStorage.setItem("rememberMe","true")):(localStorage.removeItem("rememberedEmail"),localStorage.removeItem("rememberedPassword"),localStorage.removeItem("rememberMe")),console.log("Stored token:",t.data.token))}catch(e){console.error("Login error:",e),n({success:!1,error:e.response?.data?.message||"Login failed"})}finally{d(!1)}};return(0,r.jsxs)("div",{className:"container mx-auto p-8",children:[r.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Login Test Page"}),(0,r.jsxs)("form",{onSubmit:g,className:"mb-8",children:[(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{className:"block text-gray-700 mb-2",children:"Email"}),r.jsx("input",{type:"email",value:e,onChange:e=>t(e.target.value),className:"w-full px-3 py-2 border rounded",required:!0})]}),(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{className:"block text-gray-700 mb-2",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("input",{type:u?"text":"password",value:s,onChange:e=>l(e.target.value),className:"w-full px-3 py-2 border rounded pr-10",required:!0}),r.jsx("button",{type:"button",onClick:()=>{m(!u)},className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600",children:u?(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[r.jsx("path",{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}),r.jsx("path",{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"})]}):(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[r.jsx("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),r.jsx("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]})})]})]}),r.jsx("div",{className:"mb-4",children:(0,r.jsxs)("label",{className:"flex items-center",children:[r.jsx("input",{type:"checkbox",checked:p,onChange:e=>x(e.target.checked),className:"mr-2"}),r.jsx("span",{className:"text-sm text-gray-700",children:"Remember me"})]})}),r.jsx("button",{type:"submit",disabled:c,className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded",children:c?"Logging in...":"Login"})]}),i&&(0,r.jsxs)("div",{className:`p-4 rounded ${i.success?"bg-green-100":"bg-red-100"}`,children:[r.jsx("h2",{className:"font-bold mb-2",children:i.success?"Login Successful":"Login Failed"}),r.jsx("pre",{className:"bg-gray-100 p-4 rounded overflow-auto",children:JSON.stringify(i,null,2)})]}),(0,r.jsxs)("div",{className:"mt-8",children:[r.jsx("h2",{className:"text-xl font-bold mb-2",children:"Current Status"}),r.jsx("p",{className:"text-gray-600",children:"Use this page to test login functionality."})]})]})}},39459:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h,metadata:()=>b});var r=s(25036),a=s(57495),o=s.n(a);s(67272);var l=s(86843);let i=(0,l.createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\Components\CookieConsent.jsx`),{__esModule:n,$$typeof:c}=i,d=i.default,u=(0,l.createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\Components\EmailSubscriptionPopup.jsx`),{__esModule:m,$$typeof:p}=u,x=u.default;var g=s(23222);s(97001);let b={title:"Mr.Blogger",description:"A blog platform by Mr.Blogger"};function h({children:e}){return(0,r.jsxs)("html",{lang:"en",children:[r.jsx("head",{children:r.jsx("link",{rel:"stylesheet",href:"/build/tailwind.css"})}),(0,r.jsxs)("body",{className:o().className,children:[e,r.jsx(d,{}),r.jsx(x,{}),r.jsx(g.Ix,{position:"top-center",autoClose:3e3})]})]})}},35432:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>o,__esModule:()=>a,default:()=>l});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\login-test\page.jsx`),{__esModule:a,$$typeof:o}=r,l=r.default},57481:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(70337);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},67272:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,3998,337],()=>s(62150));module.exports=r})();
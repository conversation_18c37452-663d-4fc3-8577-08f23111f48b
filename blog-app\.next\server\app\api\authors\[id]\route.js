"use strict";(()=>{var e={};e.id=4818,e.ids=[4818],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},57147:e=>{e.exports=require("fs")},73292:e=>{e.exports=require("fs/promises")},90278:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>f,originalPathname:()=>b,patchFetch:()=>v,requestAsyncStorage:()=>m,routeModule:()=>p,serverHooks:()=>g,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>y});var s={};r.r(s),r.d(s,{GET:()=>d,PUT:()=>l});var a=r(95419),o=r(69108),n=r(99678),u=r(78070),i=r(91887),c=r(92984);async function d(e,{params:t}){try{await (0,i.n)();let e=await c.Z.findById(t.id);if(!e)return u.Z.json({success:!1,message:"Author not found"},{status:404});return u.Z.json({success:!0,author:e})}catch(e){return console.error("Error fetching author:",e),u.Z.json({success:!1,message:"Failed to fetch author"},{status:500})}}async function l(e,{params:t}){try{await (0,i.n)();let s=await e.formData(),a=s.get("name"),o=s.get("bio"),n=s.get("image");if(!a)return u.Z.json({success:!1,message:"Author name is required"},{status:400});let d=await c.Z.findById(t.id);if(!d)return u.Z.json({success:!1,message:"Author not found"},{status:404});if(d.name=a,d.bio=o,n&&n.size>0){let e=await n.arrayBuffer(),t=Buffer.from(e),s=Date.now(),a=`./public/authors/${s}_${n.name}`,o=r(57147);o.existsSync("./public/authors")||o.mkdirSync("./public/authors",{recursive:!0});let{writeFile:u}=r(73292);await u(a,t),d.image=`/authors/${s}_${n.name}`}return await d.save(),u.Z.json({success:!0,message:"Author updated successfully",author:d})}catch(e){return console.error("Error updating author:",e),u.Z.json({success:!1,message:"Failed to update author"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/authors/[id]/route",pathname:"/api/authors/[id]",filename:"route",bundlePath:"app/api/authors/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\authors\\[id]\\route.js",nextConfigOutput:"",userland:s}),{requestAsyncStorage:m,staticGenerationAsyncStorage:h,serverHooks:g,headerHooks:f,staticGenerationBailout:y}=p,b="/api/authors/[id]/route";function v(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:h})}},91887:(e,t,r)=>{r.d(t,{n:()=>o});var s=r(11185),a=r.n(s);let o=async()=>{try{if(a().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await a().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},92984:(e,t,r)=>{r.d(t,{Z:()=>n});var s=r(11185),a=r.n(s);let o=new(a()).Schema({name:{type:String,required:[!0,"Author name is required"],unique:!0,trim:!0},image:{type:String,default:"/author_img.png"},bio:{type:String,default:""},createdAt:{type:Date,default:Date.now}}),n=a().models.Author||a().model("Author",o)},78070:(e,t,r)=>{Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return s.NextResponse}});let s=r(70457)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,2993],()=>r(90278));module.exports=s})();
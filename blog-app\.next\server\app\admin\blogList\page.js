(()=>{var e={};e.id=380,e.ids=[380],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},76322:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>c,originalPathname:()=>d,pages:()=>u,routeModule:()=>x,tree:()=>l});var s=r(50482),i=r(69108),o=r(62563),a=r.n(o),n=r(68300),p={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>n[e]);r.d(t,p);let l=["",{children:["admin",{children:["blogList",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5412)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\blogList\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,32143)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,39459)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\blogList\\page.jsx"],d="/admin/blogList/page",c={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/admin/blogList/page",pathname:"/admin/blogList",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},12377:(e,t,r)=>{Promise.resolve().then(r.bind(r,69217))},69217:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(95344),i=r(3729),o=r(22254);let a=()=>{let e=(0,o.useRouter)();return(0,i.useEffect)(()=>{e.push("/admin/addBlog?tab=manage")},[e]),(0,s.jsxs)("div",{className:"flex-1 pt-5 px-5 sm:pt-12 sm:pl-16",children:[s.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Redirecting..."}),s.jsx("p",{children:"Please wait while we redirect you to the new Blog Management page."})]})}},5412:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>o,__esModule:()=>i,default:()=>a});let s=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\admin\blogList\page.jsx`),{__esModule:i,$$typeof:o}=s,a=s.default}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,3998,337,8468,5757,7388],()=>r(76322));module.exports=s})();
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[380],{5841:function(e,t,r){Promise.resolve().then(r.bind(r,3369))},3369:function(e,t,r){"use strict";r.r(t);var n=r(7437),o=r(2265),s=r(4033);t.default=()=>{let e=(0,s.useRouter)();return(0,o.useEffect)(()=>{e.push("/admin/addBlog?tab=manage")},[e]),(0,n.jsxs)("div",{className:"flex-1 pt-5 px-5 sm:pt-12 sm:pl-16",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Redirecting..."}),(0,n.jsx)("p",{children:"Please wait while we redirect you to the new Blog Management page."})]})}},622:function(e,t,r){"use strict";var n=r(2265),o=Symbol.for("react.element"),s=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,i=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,r){var n,s={},u=null,f=null;for(n in void 0!==r&&(u=""+r),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(f=t.ref),t)a.call(t,n)&&!l.hasOwnProperty(n)&&(s[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===s[n]&&(s[n]=t[n]);return{$$typeof:o,type:e,key:u,ref:f,props:s,_owner:i.current}}t.Fragment=s,t.jsx=u,t.jsxs=u},7437:function(e,t,r){"use strict";e.exports=r(622)},4033:function(e,t,r){e.exports=r(5313)}},function(e){e.O(0,[971,938,744],function(){return e(e.s=5841)}),_N_E=e.O()}]);
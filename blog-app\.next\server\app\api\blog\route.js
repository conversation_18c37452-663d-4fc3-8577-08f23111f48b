(()=>{var e={};e.id=8342,e.ids=[8342],e.modules={11185:e=>{"use strict";e.exports=require("mongoose")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},57147:e=>{"use strict";e.exports=require("fs")},73292:e=>{"use strict";e.exports=require("fs/promises")},95451:(e,i,t)=>{"use strict";t.r(i),t.d(i,{headerHooks:()=>y,originalPathname:()=>S,patchFetch:()=>q,requestAsyncStorage:()=>h,routeModule:()=>f,serverHooks:()=>x,staticGenerationAsyncStorage:()=>v,staticGenerationBailout:()=>k});var r={};t.r(r),t.d(r,{DELETE:()=>p,GET:()=>w,POST:()=>g,PUT:()=>m});var o=t(95419),a=t(69108),n=t(99678),s=t(91887),u=t(42244),l=t(31833),d=t(73292);let{NextResponse:c}=t(80406),b=t(57147);async function w(e){try{let i=e.nextUrl.searchParams.get("id");if(i){let e=await u.Z.findById(i);return c.json(e)}{let e=await u.Z.find({});return c.json({blogs:e})}}catch(e){return console.error("Error in GET /api/blog:",e.message),c.json({success:!1,message:"Database connection error. Please try again later.",blogs:[]},{status:503})}}async function g(e){let i=await e.formData(),t=Date.now(),r=i.get("image"),o=await r.arrayBuffer(),a=Buffer.from(o),n=`./public/${t}_${r.name}`;await (0,d.writeFile)(n,a);let s=`/${t}_${r.name}`,b={title:`${i.get("title")}`,description:`${i.get("description")}`,category:`${i.get("category")}`,author:`${i.get("author")}`,image:`${s}`,authorImg:`${i.get("authorImg")}`},w=await u.Z.create(b);console.log("Blog Saved");let g=i.get("tempBlogId");if(g)try{await l.Z.updateMany({blogId:g},{blogId:w._id.toString()}),console.log(`Transferred images from ${g} to ${w._id}`)}catch(e){console.error("Error transferring images:",e)}return c.json({success:!0,msg:"Blog Added"})}async function p(e){try{let i=await e.nextUrl.searchParams.get("id"),t=await u.Z.findById(i);if(!t)return c.json({success:!1,msg:"Blog not found"},{status:404});return t.image&&b.unlink(`./public${t.image}`,()=>{}),await u.Z.findByIdAndDelete(i),c.json({success:!0,msg:"Blog Deleted"})}catch(e){return console.error("Error in DELETE /api/blog:",e.message),c.json({success:!1,msg:"Database connection error. Please try again later."},{status:503})}}async function m(e){try{let i=await e.formData(),t=i.get("id"),r=await u.Z.findById(t);if(!r)return c.json({success:!1,msg:"Blog not found"},{status:404});r.title=i.get("title"),r.description=i.get("description"),r.category=i.get("category"),r.author=i.get("author"),r.authorImg=i.get("authorImg");let o=i.get("image");if(o&&o.name){let e=await o.arrayBuffer(),i=Buffer.from(e),t=Date.now(),a=`./public/${t}_${o.name}`;await (0,d.writeFile)(a,i);let n=`/${t}_${o.name}`;if(r.image)try{b.unlink(`./public${r.image}`,()=>{})}catch(e){console.error("Error deleting old image:",e)}r.image=n}return await r.save(),c.json({success:!0,msg:"Blog Updated Successfully"})}catch(e){return console.error("Error updating blog:",e),c.json({success:!1,msg:"Error updating blog"},{status:500})}}(async()=>{await (0,s.n)()})();let f=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/blog/route",pathname:"/api/blog",filename:"route",bundlePath:"app/api/blog/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\blog\\route.js",nextConfigOutput:"",userland:r}),{requestAsyncStorage:h,staticGenerationAsyncStorage:v,serverHooks:x,headerHooks:y,staticGenerationBailout:k}=f,S="/api/blog/route";function q(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:v})}},91887:(e,i,t)=>{"use strict";t.d(i,{n:()=>a});var r=t(11185),o=t.n(r);let a=async()=>{try{if(o().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await o().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},42244:(e,i,t)=>{"use strict";t.d(i,{Z:()=>n});var r=t(11185),o=t.n(r);let a=new(o()).Schema({title:{type:String,required:!0},description:{type:String,required:!0},category:{type:String,required:!0},author:{type:String,required:!0},image:{type:String,required:!0},authorImg:{type:String,required:!0},date:{type:Date,default:Date.now()}}),n=o().models.blog||o().model("blog",a)},31833:(e,i,t)=>{"use strict";t.d(i,{Z:()=>n});var r=t(11185),o=t.n(r);let a=new(o()).Schema({filename:{type:String,required:!0},path:{type:String,required:!0},url:{type:String,required:!0},contentType:{type:String,required:!0},size:{type:Number,required:!0},data:{type:Buffer,required:!0},blogId:{type:o().Schema.Types.Mixed,ref:"blog",default:null},uploadDate:{type:Date,default:Date.now}}),n=o().models.image||o().model("image",a)},42881:(e,i,t)=>{var r;(()=>{var o={226:function(o,a){!function(n,s){"use strict";var u="function",l="undefined",d="object",c="string",b="major",w="model",g="name",p="type",m="vendor",f="version",h="architecture",v="console",x="mobile",y="tablet",k="smarttv",S="wearable",q="embedded",_="Amazon",R="Apple",T="ASUS",A="BlackBerry",P="Browser",E="Chrome",B="Firefox",N="Google",U="Huawei",j="Microsoft",D="Motorola",O="Opera",I="Samsung",C="Sharp",M="Sony",z="Xiaomi",$="Zebra",L="Facebook",G="Chromium OS",F="Mac OS",Z=function(e,i){var t={};for(var r in e)i[r]&&i[r].length%2==0?t[r]=i[r].concat(e[r]):t[r]=e[r];return t},V=function(e){for(var i={},t=0;t<e.length;t++)i[e[t].toUpperCase()]=e[t];return i},H=function(e,i){return typeof e===c&&-1!==W(i).indexOf(W(e))},W=function(e){return e.toLowerCase()},X=function(e,i){if(typeof e===c)return e=e.replace(/^\s\s*/,""),typeof i===l?e:e.substring(0,350)},Y=function(e,i){for(var t,r,o,a,n,l,c=0;c<i.length&&!n;){var b=i[c],w=i[c+1];for(t=r=0;t<b.length&&!n&&b[t];)if(n=b[t++].exec(e))for(o=0;o<w.length;o++)l=n[++r],typeof(a=w[o])===d&&a.length>0?2===a.length?typeof a[1]==u?this[a[0]]=a[1].call(this,l):this[a[0]]=a[1]:3===a.length?typeof a[1]!==u||a[1].exec&&a[1].test?this[a[0]]=l?l.replace(a[1],a[2]):s:this[a[0]]=l?a[1].call(this,l,a[2]):s:4===a.length&&(this[a[0]]=l?a[3].call(this,l.replace(a[1],a[2])):s):this[a]=l||s;c+=2}},K=function(e,i){for(var t in i)if(typeof i[t]===d&&i[t].length>0){for(var r=0;r<i[t].length;r++)if(H(i[t][r],e))return"?"===t?s:t}else if(H(i[t],e))return"?"===t?s:t;return e},Q={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},J={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[f,[g,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[f,[g,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[g,f],[/opios[\/ ]+([\w\.]+)/i],[f,[g,O+" Mini"]],[/\bopr\/([\w\.]+)/i],[f,[g,O]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[g,f],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[f,[g,"UC"+P]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[f,[g,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[f,[g,"WeChat"]],[/konqueror\/([\w\.]+)/i],[f,[g,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[f,[g,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[f,[g,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[g,/(.+)/,"$1 Secure "+P],f],[/\bfocus\/([\w\.]+)/i],[f,[g,B+" Focus"]],[/\bopt\/([\w\.]+)/i],[f,[g,O+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[f,[g,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[f,[g,"Dolphin"]],[/coast\/([\w\.]+)/i],[f,[g,O+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[f,[g,"MIUI "+P]],[/fxios\/([-\w\.]+)/i],[f,[g,B]],[/\bqihu|(qi?ho?o?|360)browser/i],[[g,"360 "+P]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[g,/(.+)/,"$1 "+P],f],[/(comodo_dragon)\/([\w\.]+)/i],[[g,/_/g," "],f],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[g,f],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[g],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[g,L],f],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[g,f],[/\bgsa\/([\w\.]+) .*safari\//i],[f,[g,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[f,[g,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[f,[g,E+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[g,E+" WebView"],f],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[f,[g,"Android "+P]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[g,f],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[f,[g,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[f,g],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[g,[f,K,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[g,f],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[g,"Netscape"],f],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[f,[g,B+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[g,f],[/(cobalt)\/([\w\.]+)/i],[g,[f,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[h,"amd64"]],[/(ia32(?=;))/i],[[h,W]],[/((?:i[346]|x)86)[;\)]/i],[[h,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[h,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[h,"armhf"]],[/windows (ce|mobile); ppc;/i],[[h,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[h,/ower/,"",W]],[/(sun4\w)[;\)]/i],[[h,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[h,W]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[w,[m,I],[p,y]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[w,[m,I],[p,x]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[w,[m,R],[p,x]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[w,[m,R],[p,y]],[/(macintosh);/i],[w,[m,R]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[w,[m,C],[p,x]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[w,[m,U],[p,y]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[w,[m,U],[p,x]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[w,/_/g," "],[m,z],[p,x]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[w,/_/g," "],[m,z],[p,y]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[w,[m,"OPPO"],[p,x]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[w,[m,"Vivo"],[p,x]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[w,[m,"Realme"],[p,x]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[w,[m,D],[p,x]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[w,[m,D],[p,y]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[w,[m,"LG"],[p,y]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[w,[m,"LG"],[p,x]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[w,[m,"Lenovo"],[p,y]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[w,/_/g," "],[m,"Nokia"],[p,x]],[/(pixel c)\b/i],[w,[m,N],[p,y]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[w,[m,N],[p,x]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[w,[m,M],[p,x]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[w,"Xperia Tablet"],[m,M],[p,y]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[w,[m,"OnePlus"],[p,x]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[w,[m,_],[p,y]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[w,/(.+)/g,"Fire Phone $1"],[m,_],[p,x]],[/(playbook);[-\w\),; ]+(rim)/i],[w,m,[p,y]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[w,[m,A],[p,x]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[w,[m,T],[p,y]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[w,[m,T],[p,x]],[/(nexus 9)/i],[w,[m,"HTC"],[p,y]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[m,[w,/_/g," "],[p,x]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[w,[m,"Acer"],[p,y]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[w,[m,"Meizu"],[p,x]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[m,w,[p,x]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[m,w,[p,y]],[/(surface duo)/i],[w,[m,j],[p,y]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[w,[m,"Fairphone"],[p,x]],[/(u304aa)/i],[w,[m,"AT&T"],[p,x]],[/\bsie-(\w*)/i],[w,[m,"Siemens"],[p,x]],[/\b(rct\w+) b/i],[w,[m,"RCA"],[p,y]],[/\b(venue[\d ]{2,7}) b/i],[w,[m,"Dell"],[p,y]],[/\b(q(?:mv|ta)\w+) b/i],[w,[m,"Verizon"],[p,y]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[w,[m,"Barnes & Noble"],[p,y]],[/\b(tm\d{3}\w+) b/i],[w,[m,"NuVision"],[p,y]],[/\b(k88) b/i],[w,[m,"ZTE"],[p,y]],[/\b(nx\d{3}j) b/i],[w,[m,"ZTE"],[p,x]],[/\b(gen\d{3}) b.+49h/i],[w,[m,"Swiss"],[p,x]],[/\b(zur\d{3}) b/i],[w,[m,"Swiss"],[p,y]],[/\b((zeki)?tb.*\b) b/i],[w,[m,"Zeki"],[p,y]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[m,"Dragon Touch"],w,[p,y]],[/\b(ns-?\w{0,9}) b/i],[w,[m,"Insignia"],[p,y]],[/\b((nxa|next)-?\w{0,9}) b/i],[w,[m,"NextBook"],[p,y]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[m,"Voice"],w,[p,x]],[/\b(lvtel\-)?(v1[12]) b/i],[[m,"LvTel"],w,[p,x]],[/\b(ph-1) /i],[w,[m,"Essential"],[p,x]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[w,[m,"Envizen"],[p,y]],[/\b(trio[-\w\. ]+) b/i],[w,[m,"MachSpeed"],[p,y]],[/\btu_(1491) b/i],[w,[m,"Rotor"],[p,y]],[/(shield[\w ]+) b/i],[w,[m,"Nvidia"],[p,y]],[/(sprint) (\w+)/i],[m,w,[p,x]],[/(kin\.[onetw]{3})/i],[[w,/\./g," "],[m,j],[p,x]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[w,[m,$],[p,y]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[w,[m,$],[p,x]],[/smart-tv.+(samsung)/i],[m,[p,k]],[/hbbtv.+maple;(\d+)/i],[[w,/^/,"SmartTV"],[m,I],[p,k]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[m,"LG"],[p,k]],[/(apple) ?tv/i],[m,[w,R+" TV"],[p,k]],[/crkey/i],[[w,E+"cast"],[m,N],[p,k]],[/droid.+aft(\w)( bui|\))/i],[w,[m,_],[p,k]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[w,[m,C],[p,k]],[/(bravia[\w ]+)( bui|\))/i],[w,[m,M],[p,k]],[/(mitv-\w{5}) bui/i],[w,[m,z],[p,k]],[/Hbbtv.*(technisat) (.*);/i],[m,w,[p,k]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[m,X],[w,X],[p,k]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[p,k]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[m,w,[p,v]],[/droid.+; (shield) bui/i],[w,[m,"Nvidia"],[p,v]],[/(playstation [345portablevi]+)/i],[w,[m,M],[p,v]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[w,[m,j],[p,v]],[/((pebble))app/i],[m,w,[p,S]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[w,[m,R],[p,S]],[/droid.+; (glass) \d/i],[w,[m,N],[p,S]],[/droid.+; (wt63?0{2,3})\)/i],[w,[m,$],[p,S]],[/(quest( 2| pro)?)/i],[w,[m,L],[p,S]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[m,[p,q]],[/(aeobc)\b/i],[w,[m,_],[p,q]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[w,[p,x]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[w,[p,y]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[p,y]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[p,x]],[/(android[-\w\. ]{0,9});.+buil/i],[w,[m,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[f,[g,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[f,[g,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[g,f],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[f,g]],os:[[/microsoft (windows) (vista|xp)/i],[g,f],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[g,[f,K,Q]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[g,"Windows"],[f,K,Q]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[f,/_/g,"."],[g,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[g,F],[f,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[f,g],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[g,f],[/\(bb(10);/i],[f,[g,A]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[f,[g,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[f,[g,B+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[f,[g,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[f,[g,"watchOS"]],[/crkey\/([\d\.]+)/i],[f,[g,E+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[g,G],f],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[g,f],[/(sunos) ?([\w\.\d]*)/i],[[g,"Solaris"],f],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[g,f]]},ee=function(e,i){if(typeof e===d&&(i=e,e=s),!(this instanceof ee))return new ee(e,i).getResult();var t=typeof n!==l&&n.navigator?n.navigator:s,r=e||(t&&t.userAgent?t.userAgent:""),o=t&&t.userAgentData?t.userAgentData:s,a=i?Z(J,i):J,v=t&&t.userAgent==r;return this.getBrowser=function(){var e,i={};return i[g]=s,i[f]=s,Y.call(i,r,a.browser),i[b]=typeof(e=i[f])===c?e.replace(/[^\d\.]/g,"").split(".")[0]:s,v&&t&&t.brave&&typeof t.brave.isBrave==u&&(i[g]="Brave"),i},this.getCPU=function(){var e={};return e[h]=s,Y.call(e,r,a.cpu),e},this.getDevice=function(){var e={};return e[m]=s,e[w]=s,e[p]=s,Y.call(e,r,a.device),v&&!e[p]&&o&&o.mobile&&(e[p]=x),v&&"Macintosh"==e[w]&&t&&typeof t.standalone!==l&&t.maxTouchPoints&&t.maxTouchPoints>2&&(e[w]="iPad",e[p]=y),e},this.getEngine=function(){var e={};return e[g]=s,e[f]=s,Y.call(e,r,a.engine),e},this.getOS=function(){var e={};return e[g]=s,e[f]=s,Y.call(e,r,a.os),v&&!e[g]&&o&&"Unknown"!=o.platform&&(e[g]=o.platform.replace(/chrome os/i,G).replace(/macos/i,F)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return r},this.setUA=function(e){return r=typeof e===c&&e.length>350?X(e,350):e,this},this.setUA(r),this};ee.VERSION="1.0.35",ee.BROWSER=V([g,f,b]),ee.CPU=V([h]),ee.DEVICE=V([w,m,p,v,x,k,y,S,q]),ee.ENGINE=ee.OS=V([g,f]),typeof a!==l?(o.exports&&(a=o.exports=ee),a.UAParser=ee):t.amdO?void 0!==(r=(function(){return ee}).call(i,t,i,e))&&(e.exports=r):typeof n!==l&&(n.UAParser=ee);var ei=typeof n!==l&&(n.jQuery||n.Zepto);if(ei&&!ei.ua){var et=new ee;ei.ua=et.getResult(),ei.ua.get=function(){return et.getUA()},ei.ua.set=function(e){et.setUA(e);var i=et.getResult();for(var t in i)ei.ua[t]=i[t]}}}(this)}},a={};function n(e){var i=a[e];if(void 0!==i)return i.exports;var t=a[e]={exports:{}},r=!0;try{o[e].call(t.exports,t,t.exports,n),r=!1}finally{r&&delete a[e]}return t.exports}n.ab=__dirname+"/";var s=n(226);e.exports=s})()},8397:(e,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),function(e,i){for(var t in i)Object.defineProperty(e,t,{enumerable:!0,get:i[t]})}(i,{PageSignatureError:function(){return t},RemovedPageError:function(){return r},RemovedUAError:function(){return o}});class t extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class r extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class o extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}},78496:(e,i)=>{"use strict";function t(){throw Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead')}Object.defineProperty(i,"E",{enumerable:!0,get:function(){return t}})},71868:(e,i,t)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),function(e,i){for(var t in i)Object.defineProperty(e,t,{enumerable:!0,get:i[t]})}(i,{INTERNALS:function(){return s},NextRequest:function(){return u}});let r=t(10514),o=t(68670),a=t(8397),n=t(63608),s=Symbol("internal request");class u extends Request{constructor(e,i={}){let t="string"!=typeof e&&"url"in e?e.url:String(e);(0,o.validateURL)(t),e instanceof Request?super(e,i):super(t,i);let a=new r.NextURL(t,{headers:(0,o.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:i.nextConfig});this[s]={cookies:new n.RequestCookies(this.headers),geo:i.geo||{},ip:i.ip,nextUrl:a,url:a.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,geo:this.geo,ip:this.ip,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[s].cookies}get geo(){return this[s].geo}get ip(){return this[s].ip}get nextUrl(){return this[s].nextUrl}get page(){throw new a.RemovedPageError}get ua(){throw new a.RemovedUAError}get url(){return this[s].url}}},44214:(e,i,t)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),function(e,i){for(var t in i)Object.defineProperty(e,t,{enumerable:!0,get:i[t]})}(i,{isBot:function(){return o},userAgentFromString:function(){return a},userAgent:function(){return n}});let r=function(e){return e&&e.__esModule?e:{default:e}}(t(42881));function o(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}function a(e){return{...(0,r.default)(e),isBot:void 0!==e&&o(e)}}function n({headers:e}){return a(e.get("user-agent")||void 0)}},80406:(e,i,t)=>{let r={NextRequest:t(71868).NextRequest,NextResponse:t(70457).NextResponse,ImageResponse:t(78496).E,userAgentFromString:t(44214).userAgentFromString,userAgent:t(44214).userAgent};"undefined"!=typeof URLPattern&&(r.URLPattern=URLPattern),e.exports=r,i.NextRequest=r.NextRequest,i.NextResponse=r.NextResponse,i.ImageResponse=r.ImageResponse,i.userAgentFromString=r.userAgentFromString,i.userAgent=r.userAgent,i.URLPattern=r.URLPattern}};var i=require("../../../webpack-runtime.js");i.C(e);var t=e=>i(i.s=e),r=i.X(0,[1638,2993],()=>t(95451));module.exports=r})();
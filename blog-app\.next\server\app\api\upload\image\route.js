"use strict";(()=>{var e={};e.id=8374,e.ids=[8374],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},57147:e=>{e.exports=require("fs")},73292:e=>{e.exports=require("fs/promises")},71017:e=>{e.exports=require("path")},64481:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>y,originalPathname:()=>h,patchFetch:()=>S,requestAsyncStorage:()=>g,routeModule:()=>c,serverHooks:()=>f,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>b});var a={};r.r(a),r.d(a,{POST:()=>d});var o=r(95419),i=r(69108),n=r(99678),s=r(78070),u=r(73292);r(71017);var l=r(91887),p=r(31833);async function d(e){try{await (0,l.n)();let t=await e.formData(),a=t.get("image"),o=t.get("blogId")||null;if(!a)return s.Z.json({success:!1,message:"No image provided"},{status:400});let i=await a.arrayBuffer(),n=Buffer.from(i),d=Date.now(),c=`${d}_${a.name.replace(/\s+/g,"_")}`,g=r(57147);g.existsSync("./public/blog-images")||g.mkdirSync("./public/blog-images",{recursive:!0});let m=`./public/blog-images/${c}`;await (0,u.writeFile)(m,n);let f=`/blog-images/${c}`,y=await p.Z.create({filename:a.name,path:m,url:f,contentType:a.type,size:n.length,data:n,blogId:"new"===o?null:o});return s.Z.json({success:!0,imageUrl:f,imageId:y._id})}catch(e){return console.error("Error uploading image:",e),s.Z.json({success:!1,message:"Failed to upload image"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/upload/image/route",pathname:"/api/upload/image",filename:"route",bundlePath:"app/api/upload/image/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\upload\\image\\route.js",nextConfigOutput:"",userland:a}),{requestAsyncStorage:g,staticGenerationAsyncStorage:m,serverHooks:f,headerHooks:y,staticGenerationBailout:b}=c,h="/api/upload/image/route";function S(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:m})}},91887:(e,t,r)=>{r.d(t,{n:()=>i});var a=r(11185),o=r.n(a);let i=async()=>{try{if(o().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await o().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},31833:(e,t,r)=>{r.d(t,{Z:()=>n});var a=r(11185),o=r.n(a);let i=new(o()).Schema({filename:{type:String,required:!0},path:{type:String,required:!0},url:{type:String,required:!0},contentType:{type:String,required:!0},size:{type:Number,required:!0},data:{type:Buffer,required:!0},blogId:{type:o().Schema.Types.Mixed,ref:"blog",default:null},uploadDate:{type:Date,default:Date.now}}),n=o().models.image||o().model("image",i)},78070:(e,t,r)=>{Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return a.NextResponse}});let a=r(70457)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,2993],()=>r(64481));module.exports=a})();
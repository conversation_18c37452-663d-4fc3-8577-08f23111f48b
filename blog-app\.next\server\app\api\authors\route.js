"use strict";(()=>{var e={};e.id=1456,e.ids=[1456],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},57147:e=>{e.exports=require("fs")},73292:e=>{e.exports=require("fs/promises")},93911:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>y,originalPathname:()=>A,patchFetch:()=>Z,requestAsyncStorage:()=>p,routeModule:()=>m,serverHooks:()=>f,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>w});var s={};r.r(s),r.d(s,{DELETE:()=>h,GET:()=>l,POST:()=>d});var a=r(95419),o=r(69108),n=r(99678),u=r(78070),i=r(91887),c=r(92984);async function l(){try{await (0,i.n)();let e=await c.Z.find({}).sort({name:1});return u.Z.json({success:!0,authors:e})}catch(e){return console.error("Error fetching authors:",e),u.Z.json({success:!1,message:"Failed to fetch authors"},{status:500})}}async function d(e){try{await (0,i.n)();let t=await e.formData(),s=t.get("name"),a=t.get("bio"),o=t.get("image");if(!s)return u.Z.json({success:!1,message:"Author name is required"},{status:400});if(await c.Z.findOne({name:s}))return u.Z.json({success:!1,message:"Author already exists"},{status:409});let n={name:s,bio:a||""};if(o&&o.size>0){let e=await o.arrayBuffer(),t=Buffer.from(e),s=Date.now(),a=`./public/authors/${s}_${o.name}`,u=r(57147);u.existsSync("./public/authors")||u.mkdirSync("./public/authors",{recursive:!0});let{writeFile:i}=r(73292);await i(a,t),n.image=`/authors/${s}_${o.name}`}let l=new c.Z(n);return await l.save(),u.Z.json({success:!0,message:"Author added successfully",author:l})}catch(e){return console.error("Error creating author:",e),u.Z.json({success:!1,message:"Failed to create author"},{status:500})}}async function h(e){try{await (0,i.n)();let{searchParams:t}=new URL(e.url),r=t.get("id");if(!r)return u.Z.json({success:!1,message:"Author ID is required"},{status:400});if(!await c.Z.findByIdAndDelete(r))return u.Z.json({success:!1,message:"Author not found"},{status:404});return u.Z.json({success:!0,message:"Author deleted successfully"})}catch(e){return console.error("Error deleting author:",e),u.Z.json({success:!1,message:"Failed to delete author"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/authors/route",pathname:"/api/authors",filename:"route",bundlePath:"app/api/authors/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\authors\\route.js",nextConfigOutput:"",userland:s}),{requestAsyncStorage:p,staticGenerationAsyncStorage:g,serverHooks:f,headerHooks:y,staticGenerationBailout:w}=m,A="/api/authors/route";function Z(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:g})}},91887:(e,t,r)=>{r.d(t,{n:()=>o});var s=r(11185),a=r.n(s);let o=async()=>{try{if(a().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await a().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},92984:(e,t,r)=>{r.d(t,{Z:()=>n});var s=r(11185),a=r.n(s);let o=new(a()).Schema({name:{type:String,required:[!0,"Author name is required"],unique:!0,trim:!0},image:{type:String,default:"/author_img.png"},bio:{type:String,default:""},createdAt:{type:Date,default:Date.now}}),n=a().models.Author||a().model("Author",o)},78070:(e,t,r)=>{Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return s.NextResponse}});let s=r(70457)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,2993],()=>r(93911));module.exports=s})();
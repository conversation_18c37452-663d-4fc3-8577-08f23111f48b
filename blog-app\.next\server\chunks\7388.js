exports.id=7388,exports.ids=[7388],exports.modules={15395:(e,s,l)=>{Promise.resolve().then(l.bind(l,29101))},29101:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>x});var t=l(95344),r=l(6880),i=l(41223),a=l.n(i),n=l(20783),d=l.n(n),c=l(22254),o=l(3729),h=l(69697);let m=()=>{(0,c.useRouter)();let e=(0,c.usePathname)(),[s,l]=(0,o.useState)(!0),[i,n]=(0,o.useState)(!1),[h,m]=(0,o.useState)(!0);return(0,t.jsxs)("div",{className:`flex flex-col bg-slate-100 ${s?"w-28 sm:w-80":"w-20"} transition-all duration-300 min-h-screen`,children:[(0,t.jsxs)("div",{className:`py-3 border border-black flex items-center transition-all duration-300 ${s?"px-2 sm:pl-14 justify-between":"px-0 justify-center"} w-full`,children:[s&&t.jsx(d(),{href:"/",children:t.jsx("div",{className:"flex items-center",children:t.jsx(a(),{src:r.L.logo,width:120,alt:"Mr.Blogger",className:"cursor-pointer transition-all duration-300"})})}),t.jsx("button",{onClick:()=>{n(!0),s?(m(!1),l(!1)):(l(!0),setTimeout(()=>{m(!0)},150)),setTimeout(()=>{n(!1)},300)},className:"p-2 hover:bg-slate-200 rounded-full transition-all ml-auto",children:t.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:`transform transition-transform ${s?"":"rotate-180"}`,children:t.jsx("path",{d:"M15 18l-6-6 6-6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),t.jsx("div",{className:"h-full relative py-12 border border-black border-t-0 transition-all duration-300 w-full",children:(0,t.jsxs)("div",{className:`${s?"w-[50%] sm:w-[80%]":"w-[90%]"} absolute right-0 transition-all duration-300`,children:[(0,t.jsxs)(d(),{href:"/admin",className:`flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${"/admin"===e?"bg-gray-100":""}`,children:[t.jsx("div",{className:"flex-shrink-0 w-7 h-7 flex items-center justify-center",children:t.jsx("svg",{width:"28",height:"28",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z",fill:"currentColor"})})}),s&&t.jsx("div",{className:"w-full overflow-hidden",children:h&&t.jsx("span",{className:"hidden sm:block sidebar-text-animate whitespace-nowrap",children:"Overview"})})]}),(0,t.jsxs)(d(),{href:"/admin/addBlog",className:`mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${"/admin/addBlog"===e?"bg-gray-100":""}`,children:[t.jsx("div",{className:"flex-shrink-0 w-7 h-7 flex items-center justify-center",children:t.jsx(a(),{src:r.L.blog_icon,alt:"",width:28})}),s&&t.jsx("div",{className:"w-full overflow-hidden",children:h&&t.jsx("span",{className:"hidden sm:block sidebar-text-animate whitespace-nowrap",children:"Blog Management"})})]}),(0,t.jsxs)(d(),{href:"/admin/addUser",className:`mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${"/admin/addUser"===e?"bg-gray-100":""}`,children:[t.jsx("div",{className:"flex-shrink-0 w-7 h-7 flex items-center justify-center",children:t.jsx("svg",{width:"28",height:"28",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M15 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm-9-2V7H4v3H1v2h3v3h2v-3h3v-2H6zm9 4c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z",fill:"currentColor"})})}),s&&t.jsx("div",{className:"w-full overflow-hidden",children:h&&t.jsx("span",{className:"hidden sm:block sidebar-text-animate whitespace-nowrap",children:"User Management"})})]}),(0,t.jsxs)(d(),{href:"/admin/settings",className:`mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${"/admin/settings"===e?"bg-gray-100":""}`,children:[t.jsx("div",{className:"flex-shrink-0 w-7 h-7 flex items-center justify-center",children:t.jsx("svg",{width:"28",height:"28",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z",fill:"currentColor"})})}),s&&t.jsx("div",{className:"w-full overflow-hidden",children:h&&t.jsx("span",{className:"hidden sm:block sidebar-text-animate whitespace-nowrap",children:"Settings"})})]}),(0,t.jsxs)(d(),{href:"/admin/feedback",className:`mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${"/admin/feedback"===e?"bg-gray-100":""}`,children:[t.jsx("div",{className:"flex-shrink-0 w-7 h-7 flex items-center justify-center",children:t.jsx("svg",{width:"28",height:"28",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 12h-2v-2h2v2zm0-4h-2V6h2v4z",fill:"currentColor"})})}),s&&t.jsx("div",{className:"w-full overflow-hidden",children:h&&t.jsx("span",{className:"hidden sm:block sidebar-text-animate whitespace-nowrap",children:"Feedback"})})]}),(0,t.jsxs)(d(),{href:"/admin/subscriptions",className:`mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${"/admin/subscriptions"===e?"bg-gray-100":""}`,children:[t.jsx("div",{className:"flex-shrink-0 w-7 h-7 flex items-center justify-center",children:t.jsx(a(),{src:r.L.email_icon,alt:"",width:28})}),s&&t.jsx("div",{className:"w-full overflow-hidden",children:h&&t.jsx("span",{className:"hidden sm:block sidebar-text-animate whitespace-nowrap",children:"Subscriptions"})})]}),(0,t.jsxs)(d(),{href:"/admin/reactions",className:`mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${"/admin/reactions"===e?"bg-gray-100":""}`,children:[t.jsx("div",{className:"flex-shrink-0 w-7 h-7 flex items-center justify-center",children:t.jsx("svg",{width:"28",height:"28",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z",fill:"currentColor"})})}),s&&t.jsx("div",{className:"w-full overflow-hidden",children:h&&t.jsx("span",{className:"hidden sm:block sidebar-text-animate whitespace-nowrap",children:"Reactions"})})]}),(0,t.jsxs)(d(),{href:"/admin/traffic",className:`mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${"/admin/traffic"===e?"bg-gray-100":""}`,children:[t.jsx("div",{className:"flex-shrink-0 w-7 h-7 flex items-center justify-center",children:t.jsx("svg",{width:"28",height:"28",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M3 13h2v7H3v-7zm4-7h2v14H7V6zm4 3h2v11h-2V9zm4 4h2v7h-2v-7zm4-7h2v14h-2V6z",fill:"currentColor"})})}),s&&t.jsx("div",{className:"w-full overflow-hidden",children:h&&t.jsx("span",{className:"hidden sm:block sidebar-text-animate whitespace-nowrap",children:"Traffic Analytics"})})]})]})})]})};function x({children:e}){let s=(0,c.useRouter)(),[l,r]=(0,o.useState)(!1),[i,n]=(0,o.useState)(!0),[x,f]=(0,o.useState)("/default_profile.png"),[p,w]=(0,o.useState)("Admin");return((0,o.useEffect)(()=>{let e=localStorage.getItem("authToken"),l=localStorage.getItem("userRole"),t=localStorage.getItem("userProfilePicture"),i=localStorage.getItem("userName");e&&"admin"===l?(r(!0),t&&f(t),i&&w(i)):(h.toast.error("You must be logged in as an admin to access this page"),s.push("/")),n(!1)},[s]),(0,o.useEffect)(()=>{let e=()=>{let e=localStorage.getItem("userProfilePicture"),s=localStorage.getItem("userName");e&&f(e),s&&w(s)},s=e=>{let{name:s,profilePicture:l}=e.detail;s&&w(s),l&&f(l)};window.addEventListener("storage",e),window.addEventListener("profileUpdate",s);let l=localStorage.getItem("userProfilePicture"),t=localStorage.getItem("userName");return l&&f(l),t&&w(t),()=>{window.removeEventListener("storage",e),window.removeEventListener("profileUpdate",s)}},[]),i)?t.jsx("div",{className:"flex items-center justify-center h-screen",children:t.jsx("p",{children:"Loading..."})}):l?t.jsx(t.Fragment,{children:(0,t.jsxs)("div",{className:"flex",children:[t.jsx(h.ToastContainer,{theme:"dark"}),t.jsx(m,{}),(0,t.jsxs)("div",{className:"flex flex-col w-full",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between w-full py-3 max-h-[60px] px-12 border-b border-black",children:[t.jsx("h3",{className:"font-medium",children:"Admin Panel"}),t.jsx("div",{className:"flex items-center gap-4",children:(0,t.jsxs)(d(),{href:"/admin/profile",className:"flex items-center gap-2 text-sm py-1 px-3 border border-black shadow-[-3px_3px_0px_#000000] hover:bg-gray-100",children:[t.jsx("span",{children:p}),t.jsx(a(),{src:x,width:24,height:24,alt:"Profile",className:"rounded-full object-cover w-6 h-6"})]})})]}),e]})]})}):null}l(45996)},32143:(e,s,l)=>{"use strict";l.r(s),l.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>a});let t=(0,l(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\admin\layout.jsx`),{__esModule:r,$$typeof:i}=t,a=t.default},45996:()=>{}};
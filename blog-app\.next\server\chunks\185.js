exports.id=185,exports.ids=[185],exports.modules={69756:(e,t,r)=>{"use strict";var i=r(14300).Buffer,n=r(14300).SlowBuffer;function s(e,t){if(!i.isBuffer(e)||!i.isBuffer(t)||e.length!==t.length)return!1;for(var r=0,n=0;n<e.length;n++)r|=e[n]^t[n];return 0===r}e.exports=s,s.install=function(){i.prototype.equal=n.prototype.equal=function(e){return s(this,e)}};var o=i.prototype.equal,a=n.prototype.equal;s.restore=function(){i.prototype.equal=o,n.prototype.equal=a}},28516:(e,t,r)=>{"use strict";var i=r(59026).Buffer,n=r(63447);function s(e){if(i.isBuffer(e))return e;if("string"==typeof e)return i.from(e,"base64");throw TypeError("ECDSA signature must be a Base64 string or a Buffer")}function o(e,t,r){for(var i=0;t+i<r&&0===e[t+i];)++i;return e[t+i]>=128&&--i,i}e.exports={derToJose:function(e,t){e=s(e);var r=n(t),o=r+1,a=e.length,l=0;if(48!==e[l++])throw Error('Could not find expected "seq"');var u=e[l++];if(129===u&&(u=e[l++]),a-l<u)throw Error('"seq" specified length of "'+u+'", only "'+(a-l)+'" remaining');if(2!==e[l++])throw Error('Could not find expected "int" for "r"');var h=e[l++];if(a-l-2<h)throw Error('"r" specified length of "'+h+'", only "'+(a-l-2)+'" available');if(o<h)throw Error('"r" specified length of "'+h+'", max of "'+o+'" is acceptable');var c=l;if(l+=h,2!==e[l++])throw Error('Could not find expected "int" for "s"');var p=e[l++];if(a-l!==p)throw Error('"s" specified length of "'+p+'", expected "'+(a-l)+'"');if(o<p)throw Error('"s" specified length of "'+p+'", max of "'+o+'" is acceptable');var f=l;if((l+=p)!==a)throw Error('Expected to consume entire buffer, but "'+(a-l)+'" bytes remain');var m=r-h,d=r-p,y=i.allocUnsafe(m+h+d+p);for(l=0;l<m;++l)y[l]=0;e.copy(y,l,c+Math.max(-m,0),c+h),l=r;for(var v=l;l<v+d;++l)y[l]=0;return e.copy(y,l,f+Math.max(-d,0),f+p),y=(y=y.toString("base64")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},joseToDer:function(e,t){e=s(e);var r=n(t),a=e.length;if(a!==2*r)throw TypeError('"'+t+'" signatures must be "'+2*r+'" bytes, saw "'+a+'"');var l=o(e,0,r),u=o(e,r,e.length),h=r-l,c=r-u,p=2+h+1+1+c,f=p<128,m=i.allocUnsafe((f?2:3)+p),d=0;return m[d++]=48,f?m[d++]=p:(m[d++]=129,m[d++]=255&p),m[d++]=2,m[d++]=h,l<0?(m[d++]=0,d+=e.copy(m,d,0,r)):d+=e.copy(m,d,l,r),m[d++]=2,m[d++]=c,u<0?(m[d++]=0,e.copy(m,d,r)):e.copy(m,d,r+u),m}}},63447:e=>{"use strict";function t(e){return(e/8|0)+(e%8==0?0:1)}var r={ES256:t(256),ES384:t(384),ES512:t(521)};e.exports=function(e){var t=r[e];if(t)return t;throw Error('Unknown algorithm "'+e+'"')}},96778:(e,t,r)=>{var i=r(4305);e.exports=function(e,t){t=t||{};var r=i.decode(e,t);if(!r)return null;var n=r.payload;if("string"==typeof n)try{var s=JSON.parse(n);null!==s&&"object"==typeof s&&(n=s)}catch(e){}return!0===t.complete?{header:r.header,payload:n,signature:r.signature}:n}},46082:(e,t,r)=>{e.exports={decode:r(96778),verify:r(44588),sign:r(65741),JsonWebTokenError:r(5417),NotBeforeError:r(24865),TokenExpiredError:r(36287)}},5417:e=>{var t=function(e,t){Error.call(this,e),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor),this.name="JsonWebTokenError",this.message=e,t&&(this.inner=t)};t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,e.exports=t},24865:(e,t,r)=>{var i=r(5417),n=function(e,t){i.call(this,e),this.name="NotBeforeError",this.date=t};n.prototype=Object.create(i.prototype),n.prototype.constructor=n,e.exports=n},36287:(e,t,r)=>{var i=r(5417),n=function(e,t){i.call(this,e),this.name="TokenExpiredError",this.expiredAt=t};n.prototype=Object.create(i.prototype),n.prototype.constructor=n,e.exports=n},73601:(e,t,r)=>{let i=r(9743);e.exports=i.satisfies(process.version,">=15.7.0")},27562:(e,t,r)=>{var i=r(9743);e.exports=i.satisfies(process.version,"^6.12.0 || >=8.0.0")},61157:(e,t,r)=>{let i=r(9743);e.exports=i.satisfies(process.version,">=16.9.0")},84130:(e,t,r)=>{var i=r(87553);e.exports=function(e,t){var r=t||Math.floor(Date.now()/1e3);if("string"==typeof e){var n=i(e);if(void 0===n)return;return Math.floor(r+n/1e3)}if("number"==typeof e)return r+e}},72877:(e,t,r)=>{let i=r(73601),n=r(61157),s={ec:["ES256","ES384","ES512"],rsa:["RS256","PS256","RS384","PS384","RS512","PS512"],"rsa-pss":["PS256","PS384","PS512"]},o={ES256:"prime256v1",ES384:"secp384r1",ES512:"secp521r1"};e.exports=function(e,t){if(!e||!t)return;let r=t.asymmetricKeyType;if(!r)return;let a=s[r];if(!a)throw Error(`Unknown key type "${r}".`);if(!a.includes(e))throw Error(`"alg" parameter for "${r}" key type must be one of: ${a.join(", ")}.`);if(i)switch(r){case"ec":let l=t.asymmetricKeyDetails.namedCurve,u=o[e];if(l!==u)throw Error(`"alg" parameter "${e}" requires curve "${u}".`);break;case"rsa-pss":if(n){let r=parseInt(e.slice(-3),10),{hashAlgorithm:i,mgf1HashAlgorithm:n,saltLength:s}=t.asymmetricKeyDetails;if(i!==`sha${r}`||n!==i)throw Error(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of "alg" ${e}.`);if(void 0!==s&&s>r>>3)throw Error(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of "alg" ${e}.`)}}}},65741:(e,t,r)=>{let i=r(84130),n=r(27562),s=r(72877),o=r(4305),a=r(97871),l=r(74709),u=r(593),h=r(13794),c=r(7461),p=r(12841),f=r(43624),{KeyObject:m,createSecretKey:d,createPrivateKey:y}=r(6113),v=["RS256","RS384","RS512","ES256","ES384","ES512","HS256","HS384","HS512","none"];n&&v.splice(3,0,"PS256","PS384","PS512");let g={expiresIn:{isValid:function(e){return u(e)||p(e)&&e},message:'"expiresIn" should be a number of seconds or string representing a timespan'},notBefore:{isValid:function(e){return u(e)||p(e)&&e},message:'"notBefore" should be a number of seconds or string representing a timespan'},audience:{isValid:function(e){return p(e)||Array.isArray(e)},message:'"audience" must be a string or array'},algorithm:{isValid:a.bind(null,v),message:'"algorithm" must be a valid string enum value'},header:{isValid:c,message:'"header" must be an object'},encoding:{isValid:p,message:'"encoding" must be a string'},issuer:{isValid:p,message:'"issuer" must be a string'},subject:{isValid:p,message:'"subject" must be a string'},jwtid:{isValid:p,message:'"jwtid" must be a string'},noTimestamp:{isValid:l,message:'"noTimestamp" must be a boolean'},keyid:{isValid:p,message:'"keyid" must be a string'},mutatePayload:{isValid:l,message:'"mutatePayload" must be a boolean'},allowInsecureKeySizes:{isValid:l,message:'"allowInsecureKeySizes" must be a boolean'},allowInvalidAsymmetricKeyTypes:{isValid:l,message:'"allowInvalidAsymmetricKeyTypes" must be a boolean'}},E={iat:{isValid:h,message:'"iat" should be a number of seconds'},exp:{isValid:h,message:'"exp" should be a number of seconds'},nbf:{isValid:h,message:'"nbf" should be a number of seconds'}};function b(e,t,r,i){if(!c(r))throw Error('Expected "'+i+'" to be a plain object.');Object.keys(r).forEach(function(n){let s=e[n];if(!s){if(!t)throw Error('"'+n+'" is not allowed in "'+i+'"');return}if(!s.isValid(r[n]))throw Error(s.message)})}let S={audience:"aud",issuer:"iss",subject:"sub",jwtid:"jti"},w=["expiresIn","notBefore","noTimestamp","audience","issuer","subject","jwtid"];e.exports=function(e,t,r,n){var a,l;"function"==typeof r?(n=r,r={}):r=r||{};let u="object"==typeof e&&!Buffer.isBuffer(e),h=Object.assign({alg:r.algorithm||"HS256",typ:u?"JWT":void 0,kid:r.keyid},r.header);function c(e){if(n)return n(e);throw e}if(!t&&"none"!==r.algorithm)return c(Error("secretOrPrivateKey must have a value"));if(null!=t&&!(t instanceof m))try{t=y(t)}catch(e){try{t=d("string"==typeof t?Buffer.from(t):t)}catch(e){return c(Error("secretOrPrivateKey is not valid key material"))}}if(h.alg.startsWith("HS")&&"secret"!==t.type)return c(Error(`secretOrPrivateKey must be a symmetric key when using ${h.alg}`));if(/^(?:RS|PS|ES)/.test(h.alg)){if("private"!==t.type)return c(Error(`secretOrPrivateKey must be an asymmetric key when using ${h.alg}`));if(!r.allowInsecureKeySizes&&!h.alg.startsWith("ES")&&void 0!==t.asymmetricKeyDetails&&t.asymmetricKeyDetails.modulusLength<2048)return c(Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${h.alg}`))}if(void 0===e)return c(Error("payload is required"));if(u){try{a=e,b(E,!0,a,"payload")}catch(e){return c(e)}r.mutatePayload||(e=Object.assign({},e))}else{let t=w.filter(function(e){return void 0!==r[e]});if(t.length>0)return c(Error("invalid "+t.join(",")+" option for "+typeof e+" payload"))}if(void 0!==e.exp&&void 0!==r.expiresIn)return c(Error('Bad "options.expiresIn" option the payload already has an "exp" property.'));if(void 0!==e.nbf&&void 0!==r.notBefore)return c(Error('Bad "options.notBefore" option the payload already has an "nbf" property.'));try{l=r,b(g,!1,l,"options")}catch(e){return c(e)}if(!r.allowInvalidAsymmetricKeyTypes)try{s(h.alg,t)}catch(e){return c(e)}let p=e.iat||Math.floor(Date.now()/1e3);if(r.noTimestamp?delete e.iat:u&&(e.iat=p),void 0!==r.notBefore){try{e.nbf=i(r.notBefore,p)}catch(e){return c(e)}if(void 0===e.nbf)return c(Error('"notBefore" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'))}if(void 0!==r.expiresIn&&"object"==typeof e){try{e.exp=i(r.expiresIn,p)}catch(e){return c(e)}if(void 0===e.exp)return c(Error('"expiresIn" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'))}Object.keys(S).forEach(function(t){let i=S[t];if(void 0!==r[t]){if(void 0!==e[i])return c(Error('Bad "options.'+t+'" option. The payload already has an "'+i+'" property.'));e[i]=r[t]}});let v=r.encoding||"utf8";if("function"==typeof n)n=n&&f(n),o.createSign({header:h,privateKey:t,payload:e,encoding:v}).once("error",n).once("done",function(e){if(!r.allowInsecureKeySizes&&/^(?:RS|PS)/.test(h.alg)&&e.length<256)return n(Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${h.alg}`));n(null,e)});else{let i=o.sign({header:h,payload:e,secret:t,encoding:v});if(!r.allowInsecureKeySizes&&/^(?:RS|PS)/.test(h.alg)&&i.length<256)throw Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${h.alg}`);return i}}},44588:(e,t,r)=>{let i=r(5417),n=r(24865),s=r(36287),o=r(96778),a=r(84130),l=r(72877),u=r(27562),h=r(4305),{KeyObject:c,createSecretKey:p,createPublicKey:f}=r(6113),m=["RS256","RS384","RS512"],d=["ES256","ES384","ES512"],y=["RS256","RS384","RS512"],v=["HS256","HS384","HS512"];u&&(m.splice(m.length,0,"PS256","PS384","PS512"),y.splice(y.length,0,"PS256","PS384","PS512")),e.exports=function(e,t,r,u){let g,E,b;if("function"!=typeof r||u||(u=r,r={}),r||(r={}),r=Object.assign({},r),g=u||function(e,t){if(e)throw e;return t},r.clockTimestamp&&"number"!=typeof r.clockTimestamp)return g(new i("clockTimestamp must be a number"));if(void 0!==r.nonce&&("string"!=typeof r.nonce||""===r.nonce.trim()))return g(new i("nonce must be a non-empty string"));if(void 0!==r.allowInvalidAsymmetricKeyTypes&&"boolean"!=typeof r.allowInvalidAsymmetricKeyTypes)return g(new i("allowInvalidAsymmetricKeyTypes must be a boolean"));let S=r.clockTimestamp||Math.floor(Date.now()/1e3);if(!e)return g(new i("jwt must be provided"));if("string"!=typeof e)return g(new i("jwt must be a string"));let w=e.split(".");if(3!==w.length)return g(new i("jwt malformed"));try{E=o(e,{complete:!0})}catch(e){return g(e)}if(!E)return g(new i("invalid token"));let $=E.header;if("function"==typeof t){if(!u)return g(new i("verify must be called asynchronous if secret or public key is provided as a callback"));b=t}else b=function(e,r){return r(null,t)};return b($,function(t,o){let u;if(t)return g(new i("error in secret or public key callback: "+t.message));let b=""!==w[2].trim();if(!b&&o)return g(new i("jwt signature is required"));if(b&&!o)return g(new i("secret or public key must be provided"));if(!b&&!r.algorithms)return g(new i('please specify "none" in "algorithms" to verify unsigned tokens'));if(null!=o&&!(o instanceof c))try{o=f(o)}catch(e){try{o=p("string"==typeof o?Buffer.from(o):o)}catch(e){return g(new i("secretOrPublicKey is not valid key material"))}}if(r.algorithms||("secret"===o.type?r.algorithms=v:["rsa","rsa-pss"].includes(o.asymmetricKeyType)?r.algorithms=y:"ec"===o.asymmetricKeyType?r.algorithms=d:r.algorithms=m),-1===r.algorithms.indexOf(E.header.alg))return g(new i("invalid algorithm"));if($.alg.startsWith("HS")&&"secret"!==o.type)return g(new i(`secretOrPublicKey must be a symmetric key when using ${$.alg}`));if(/^(?:RS|PS|ES)/.test($.alg)&&"public"!==o.type)return g(new i(`secretOrPublicKey must be an asymmetric key when using ${$.alg}`));if(!r.allowInvalidAsymmetricKeyTypes)try{l($.alg,o)}catch(e){return g(e)}try{u=h.verify(e,E.header.alg,o)}catch(e){return g(e)}if(!u)return g(new i("invalid signature"));let R=E.payload;if(void 0!==R.nbf&&!r.ignoreNotBefore){if("number"!=typeof R.nbf)return g(new i("invalid nbf value"));if(R.nbf>S+(r.clockTolerance||0))return g(new n("jwt not active",new Date(1e3*R.nbf)))}if(void 0!==R.exp&&!r.ignoreExpiration){if("number"!=typeof R.exp)return g(new i("invalid exp value"));if(S>=R.exp+(r.clockTolerance||0))return g(new s("jwt expired",new Date(1e3*R.exp)))}if(r.audience){let e=Array.isArray(r.audience)?r.audience:[r.audience];if(!(Array.isArray(R.aud)?R.aud:[R.aud]).some(function(t){return e.some(function(e){return e instanceof RegExp?e.test(t):e===t})}))return g(new i("jwt audience invalid. expected: "+e.join(" or ")))}if(r.issuer&&("string"==typeof r.issuer&&R.iss!==r.issuer||Array.isArray(r.issuer)&&-1===r.issuer.indexOf(R.iss)))return g(new i("jwt issuer invalid. expected: "+r.issuer));if(r.subject&&R.sub!==r.subject)return g(new i("jwt subject invalid. expected: "+r.subject));if(r.jwtid&&R.jti!==r.jwtid)return g(new i("jwt jwtid invalid. expected: "+r.jwtid));if(r.nonce&&R.nonce!==r.nonce)return g(new i("jwt nonce invalid. expected: "+r.nonce));if(r.maxAge){if("number"!=typeof R.iat)return g(new i("iat required when maxAge is specified"));let e=a(r.maxAge,R.iat);if(void 0===e)return g(new i('"maxAge" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'));if(S>=e+(r.clockTolerance||0))return g(new s("maxAge exceeded",new Date(1e3*e)))}return!0===r.complete?g(null,{header:$,payload:R,signature:E.signature}):g(null,R)})}},72323:(e,t,r)=>{var i,n=r(59026).Buffer,s=r(6113),o=r(28516),a=r(73837),l="secret must be a string or buffer",u="key must be a string or a buffer",h="function"==typeof s.createPublicKey;function c(e){if(!n.isBuffer(e)&&"string"!=typeof e&&(!h||"object"!=typeof e||"string"!=typeof e.type||"string"!=typeof e.asymmetricKeyType||"function"!=typeof e.export))throw d(u)}function p(e){if(!n.isBuffer(e)&&"string"!=typeof e&&"object"!=typeof e)throw d("key must be a string, a buffer or an object")}function f(e){return e.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function m(e){var t=4-(e=e.toString()).length%4;if(4!==t)for(var r=0;r<t;++r)e+="=";return e.replace(/\-/g,"+").replace(/_/g,"/")}function d(e){var t=[].slice.call(arguments,1);return TypeError(a.format.bind(a,e).apply(null,t))}function y(e){var t;return t=e,n.isBuffer(t)||"string"==typeof t||(e=JSON.stringify(e)),e}function v(e){return function(t,r){(function(e){if(!n.isBuffer(e)&&"string"!=typeof e&&(!h||"object"!=typeof e||"secret"!==e.type||"function"!=typeof e.export))throw d(l)})(r),t=y(t);var i=s.createHmac("sha"+e,r);return f((i.update(t),i.digest("base64")))}}h&&(u+=" or a KeyObject",l+="or a KeyObject");var g="timingSafeEqual"in s?function(e,t){return e.byteLength===t.byteLength&&s.timingSafeEqual(e,t)}:function(e,t){return i||(i=r(69756)),i(e,t)};function E(e){return function(t,r,i){var s=v(e)(t,i);return g(n.from(r),n.from(s))}}function b(e){return function(t,r){p(r),t=y(t);var i=s.createSign("RSA-SHA"+e);return f((i.update(t),i.sign(r,"base64")))}}function S(e){return function(t,r,i){c(i),t=y(t),r=m(r);var n=s.createVerify("RSA-SHA"+e);return n.update(t),n.verify(i,r,"base64")}}function w(e){return function(t,r){p(r),t=y(t);var i=s.createSign("RSA-SHA"+e);return f((i.update(t),i.sign({key:r,padding:s.constants.RSA_PKCS1_PSS_PADDING,saltLength:s.constants.RSA_PSS_SALTLEN_DIGEST},"base64")))}}function $(e){return function(t,r,i){c(i),t=y(t),r=m(r);var n=s.createVerify("RSA-SHA"+e);return n.update(t),n.verify({key:i,padding:s.constants.RSA_PKCS1_PSS_PADDING,saltLength:s.constants.RSA_PSS_SALTLEN_DIGEST},r,"base64")}}function R(e){var t=b(e);return function(){var r=t.apply(null,arguments);return o.derToJose(r,"ES"+e)}}function I(e){var t=S(e);return function(r,i,n){return t(r,i=o.joseToDer(i,"ES"+e).toString("base64"),n)}}function x(){return function(){return""}}function O(){return function(e,t){return""===t}}e.exports=function(e){var t=e.match(/^(RS|PS|ES|HS)(256|384|512)$|^(none)$/i);if(!t)throw d('"%s" is not a valid algorithm.\n  Supported algorithms are:\n  "HS256", "HS384", "HS512", "RS256", "RS384", "RS512", "PS256", "PS384", "PS512", "ES256", "ES384", "ES512" and "none".',e);var r=(t[1]||t[3]).toLowerCase(),i=t[2];return{sign:({hs:v,rs:b,ps:w,es:R,none:x})[r](i),verify:({hs:E,rs:S,ps:$,es:I,none:O})[r](i)}}},4305:(e,t,r)=>{var i=r(7661),n=r(66282);t.ALGORITHMS=["HS256","HS384","HS512","RS256","RS384","RS512","PS256","PS384","PS512","ES256","ES384","ES512"],t.sign=i.sign,t.verify=n.verify,t.decode=n.decode,t.isValid=n.isValid,t.createSign=function(e){return new i(e)},t.createVerify=function(e){return new n(e)}},21498:(e,t,r)=>{var i=r(59026).Buffer,n=r(12781);function s(e){if(this.buffer=null,this.writable=!0,this.readable=!0,!e)return this.buffer=i.alloc(0),this;if("function"==typeof e.pipe)return this.buffer=i.alloc(0),e.pipe(this),this;if(e.length||"object"==typeof e)return this.buffer=e,this.writable=!1,process.nextTick((function(){this.emit("end",e),this.readable=!1,this.emit("close")}).bind(this)),this;throw TypeError("Unexpected data type ("+typeof e+")")}r(73837).inherits(s,n),s.prototype.write=function(e){this.buffer=i.concat([this.buffer,i.from(e)]),this.emit("data",e)},s.prototype.end=function(e){e&&this.write(e),this.emit("end",e),this.emit("close"),this.writable=!1,this.readable=!1},e.exports=s},7661:(e,t,r)=>{var i=r(59026).Buffer,n=r(21498),s=r(72323),o=r(12781),a=r(58190),l=r(73837);function u(e,t){return i.from(e,t).toString("base64").replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function h(e){var t,r,i,n=e.header,o=e.payload,h=e.secret||e.privateKey,c=e.encoding,p=s(n.alg),f=(t=(t=c)||"utf8",r=u(a(n),"binary"),i=u(a(o),t),l.format("%s.%s",r,i)),m=p.sign(f,h);return l.format("%s.%s",f,m)}function c(e){var t=new n(e.secret||e.privateKey||e.key);this.readable=!0,this.header=e.header,this.encoding=e.encoding,this.secret=this.privateKey=this.key=t,this.payload=new n(e.payload),this.secret.once("close",(function(){!this.payload.writable&&this.readable&&this.sign()}).bind(this)),this.payload.once("close",(function(){!this.secret.writable&&this.readable&&this.sign()}).bind(this))}l.inherits(c,o),c.prototype.sign=function(){try{var e=h({header:this.header,payload:this.payload.buffer,secret:this.secret.buffer,encoding:this.encoding});return this.emit("done",e),this.emit("data",e),this.emit("end"),this.readable=!1,e}catch(e){this.readable=!1,this.emit("error",e),this.emit("close")}},c.sign=h,e.exports=c},58190:(e,t,r)=>{var i=r(14300).Buffer;e.exports=function(e){return"string"==typeof e?e:"number"==typeof e||i.isBuffer(e)?e.toString():JSON.stringify(e)}},66282:(e,t,r)=>{var i=r(59026).Buffer,n=r(21498),s=r(72323),o=r(12781),a=r(58190),l=r(73837),u=/^[a-zA-Z0-9\-_]+?\.[a-zA-Z0-9\-_]+?\.([a-zA-Z0-9\-_]+)?$/;function h(e){var t=e.split(".",1)[0];return function(e){if("[object Object]"===Object.prototype.toString.call(e))return e;try{return JSON.parse(e)}catch(e){return}}(i.from(t,"base64").toString("binary"))}function c(e){return e.split(".")[2]}function p(e){return u.test(e)&&!!h(e)}function f(e,t,r){if(!t){var i=Error("Missing algorithm parameter for jws.verify");throw i.code="MISSING_ALGORITHM",i}var n=c(e=a(e)),o=e.split(".",2).join(".");return s(t).verify(o,n,r)}function m(e,t){if(t=t||{},!p(e=a(e)))return null;var r,n,s=h(e);if(!s)return null;var o=(r=r||"utf8",n=e.split(".")[1],i.from(n,"base64").toString(r));return("JWT"===s.typ||t.json)&&(o=JSON.parse(o,t.encoding)),{header:s,payload:o,signature:c(e)}}function d(e){var t=new n((e=e||{}).secret||e.publicKey||e.key);this.readable=!0,this.algorithm=e.algorithm,this.encoding=e.encoding,this.secret=this.publicKey=this.key=t,this.signature=new n(e.signature),this.secret.once("close",(function(){!this.signature.writable&&this.readable&&this.verify()}).bind(this)),this.signature.once("close",(function(){!this.secret.writable&&this.readable&&this.verify()}).bind(this))}l.inherits(d,o),d.prototype.verify=function(){try{var e=f(this.signature.buffer,this.algorithm,this.key.buffer),t=m(this.signature.buffer,this.encoding);return this.emit("done",e,t),this.emit("data",e),this.emit("end"),this.readable=!1,e}catch(e){this.readable=!1,this.emit("error",e),this.emit("close")}},d.decode=m,d.isValid=p,d.verify=f,e.exports=d},97871:e=>{var t,r,i=1/0,n=0/0,s=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,l=/^0o[0-7]+$/i,u=/^(?:0|[1-9]\d*)$/,h=parseInt;function c(e){return e!=e}var p=Object.prototype,f=p.hasOwnProperty,m=p.toString,d=p.propertyIsEnumerable,y=(t=Object.keys,r=Object,function(e){return t(r(e))}),v=Math.max,g=Array.isArray;function E(e){var t,r;return null!=e&&"number"==typeof(t=e.length)&&t>-1&&t%1==0&&t<=9007199254740991&&!("[object Function]"==(r=b(e)?m.call(e):"")||"[object GeneratorFunction]"==r)}function b(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function S(e){return!!e&&"object"==typeof e}e.exports=function(e,t,r,w){e=E(e)?e:($=e)?function(e,t){for(var r=-1,i=e?e.length:0,n=Array(i);++r<i;)n[r]=t(e[r],r,e);return n}(E($)?function(e,t){var r,i=g(e)||S(e)&&E(e)&&f.call(e,"callee")&&(!d.call(e,"callee")||"[object Arguments]"==m.call(e))?function(e,t){for(var r=-1,i=Array(e);++r<e;)i[r]=t(r);return i}(e.length,String):[],n=i.length,s=!!n;for(var o in e)f.call(e,o)&&!(s&&("length"==o||(r=null==(r=n)?9007199254740991:r)&&("number"==typeof o||u.test(o))&&o>-1&&o%1==0&&o<r))&&i.push(o);return i}($):function(e){if(t=e&&e.constructor,e!==("function"==typeof t&&t.prototype||p))return y(e);var t,r=[];for(var i in Object(e))f.call(e,i)&&"constructor"!=i&&r.push(i);return r}($),function(e){return $[e]}):[],r=r&&!w?(x=(I=(R=r)?(R=function(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||S(t)&&"[object Symbol]"==m.call(t))return n;if(b(e)){var t,r="function"==typeof e.valueOf?e.valueOf():e;e=b(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(s,"");var i=a.test(e);return i||l.test(e)?h(e.slice(2),i?2:8):o.test(e)?n:+e}(R))===i||R===-i?(R<0?-1:1)*17976931348623157e292:R==R?R:0:0===R?R:0)%1,I==I?x?I-x:I:0):0;var $,R,I,x,O,A=e.length;return r<0&&(r=v(A+r,0)),"string"==typeof(O=e)||!g(O)&&S(O)&&"[object String]"==m.call(O)?r<=A&&e.indexOf(t,r)>-1:!!A&&function(e,t,r){if(t!=t)return function(e,t,r,i){for(var n=e.length,s=r+(i?1:-1);i?s--:++s<n;)if(t(e[s],s,e))return s;return -1}(e,c,r);for(var i=r-1,n=e.length;++i<n;)if(e[i]===t)return i;return -1}(e,t,r)>-1}},74709:e=>{var t=Object.prototype.toString;e.exports=function(e){return!0===e||!1===e||!!e&&"object"==typeof e&&"[object Boolean]"==t.call(e)}},593:e=>{var t=1/0,r=0/0,i=/^\s+|\s+$/g,n=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,o=/^0o[0-7]+$/i,a=parseInt,l=Object.prototype.toString;function u(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}e.exports=function(e){var h,c,p;return"number"==typeof e&&e==(p=(c=(h=e)?(h=function(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==l.call(t))return r;if(u(e)){var t,h="function"==typeof e.valueOf?e.valueOf():e;e=u(h)?h+"":h}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(i,"");var c=s.test(e);return c||o.test(e)?a(e.slice(2),c?2:8):n.test(e)?r:+e}(h))===t||h===-t?(h<0?-1:1)*17976931348623157e292:h==h?h:0:0===h?h:0)%1,c==c?p?c-p:c:0)}},13794:e=>{var t=Object.prototype.toString;e.exports=function(e){return"number"==typeof e||!!e&&"object"==typeof e&&"[object Number]"==t.call(e)}},7461:e=>{var t,r,i=Object.prototype,n=Function.prototype.toString,s=i.hasOwnProperty,o=n.call(Object),a=i.toString,l=(t=Object.getPrototypeOf,r=Object,function(e){return t(r(e))});e.exports=function(e){if(!(e&&"object"==typeof e)||"[object Object]"!=a.call(e)||function(e){var t=!1;if(null!=e&&"function"!=typeof e.toString)try{t=!!(e+"")}catch(e){}return t}(e))return!1;var t=l(e);if(null===t)return!0;var r=s.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&n.call(r)==o}},12841:e=>{var t=Object.prototype.toString,r=Array.isArray;e.exports=function(e){var i;return"string"==typeof e||!r(e)&&!!(i=e)&&"object"==typeof i&&"[object String]"==t.call(e)}},43624:e=>{var t=1/0,r=0/0,i=/^\s+|\s+$/g,n=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,o=/^0o[0-7]+$/i,a=parseInt,l=Object.prototype.toString;function u(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}e.exports=function(e){return function(e,h){var c,p,f,m;if("function"!=typeof h)throw TypeError("Expected a function");return m=(f=(p=e)?(p=function(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==l.call(t))return r;if(u(e)){var t,h="function"==typeof e.valueOf?e.valueOf():e;e=u(h)?h+"":h}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(i,"");var c=s.test(e);return c||o.test(e)?a(e.slice(2),c?2:8):n.test(e)?r:+e}(p))===t||p===-t?(p<0?-1:1)*17976931348623157e292:p==p?p:0:0===p?p:0)%1,e=f==f?m?f-m:f:0,function(){return--e>0&&(c=h.apply(this,arguments)),e<=1&&(h=void 0),c}}(2,e)}},22680:(e,t,r)=>{"use strict";let i=r(50826),n=Symbol("max"),s=Symbol("length"),o=Symbol("lengthCalculator"),a=Symbol("allowStale"),l=Symbol("maxAge"),u=Symbol("dispose"),h=Symbol("noDisposeOnSet"),c=Symbol("lruList"),p=Symbol("cache"),f=Symbol("updateAgeOnGet"),m=()=>1;class d{constructor(e){if("number"==typeof e&&(e={max:e}),e||(e={}),e.max&&("number"!=typeof e.max||e.max<0))throw TypeError("max must be a non-negative number");this[n]=e.max||1/0;let t=e.length||m;if(this[o]="function"!=typeof t?m:t,this[a]=e.stale||!1,e.maxAge&&"number"!=typeof e.maxAge)throw TypeError("maxAge must be a number");this[l]=e.maxAge||0,this[u]=e.dispose,this[h]=e.noDisposeOnSet||!1,this[f]=e.updateAgeOnGet||!1,this.reset()}set max(e){if("number"!=typeof e||e<0)throw TypeError("max must be a non-negative number");this[n]=e||1/0,g(this)}get max(){return this[n]}set allowStale(e){this[a]=!!e}get allowStale(){return this[a]}set maxAge(e){if("number"!=typeof e)throw TypeError("maxAge must be a non-negative number");this[l]=e,g(this)}get maxAge(){return this[l]}set lengthCalculator(e){"function"!=typeof e&&(e=m),e!==this[o]&&(this[o]=e,this[s]=0,this[c].forEach(e=>{e.length=this[o](e.value,e.key),this[s]+=e.length})),g(this)}get lengthCalculator(){return this[o]}get length(){return this[s]}get itemCount(){return this[c].length}rforEach(e,t){t=t||this;for(let r=this[c].tail;null!==r;){let i=r.prev;S(this,e,r,t),r=i}}forEach(e,t){t=t||this;for(let r=this[c].head;null!==r;){let i=r.next;S(this,e,r,t),r=i}}keys(){return this[c].toArray().map(e=>e.key)}values(){return this[c].toArray().map(e=>e.value)}reset(){this[u]&&this[c]&&this[c].length&&this[c].forEach(e=>this[u](e.key,e.value)),this[p]=new Map,this[c]=new i,this[s]=0}dump(){return this[c].map(e=>!v(this,e)&&{k:e.key,v:e.value,e:e.now+(e.maxAge||0)}).toArray().filter(e=>e)}dumpLru(){return this[c]}set(e,t,r){if((r=r||this[l])&&"number"!=typeof r)throw TypeError("maxAge must be a number");let i=r?Date.now():0,a=this[o](t,e);if(this[p].has(e)){if(a>this[n])return E(this,this[p].get(e)),!1;let o=this[p].get(e).value;return this[u]&&!this[h]&&this[u](e,o.value),o.now=i,o.maxAge=r,o.value=t,this[s]+=a-o.length,o.length=a,this.get(e),g(this),!0}let f=new b(e,t,a,i,r);return f.length>this[n]?(this[u]&&this[u](e,t),!1):(this[s]+=f.length,this[c].unshift(f),this[p].set(e,this[c].head),g(this),!0)}has(e){return!!this[p].has(e)&&!v(this,this[p].get(e).value)}get(e){return y(this,e,!0)}peek(e){return y(this,e,!1)}pop(){let e=this[c].tail;return e?(E(this,e),e.value):null}del(e){E(this,this[p].get(e))}load(e){this.reset();let t=Date.now();for(let r=e.length-1;r>=0;r--){let i=e[r],n=i.e||0;if(0===n)this.set(i.k,i.v);else{let e=n-t;e>0&&this.set(i.k,i.v,e)}}}prune(){this[p].forEach((e,t)=>y(this,t,!1))}}let y=(e,t,r)=>{let i=e[p].get(t);if(i){let t=i.value;if(v(e,t)){if(E(e,i),!e[a])return}else r&&(e[f]&&(i.value.now=Date.now()),e[c].unshiftNode(i));return t.value}},v=(e,t)=>{if(!t||!t.maxAge&&!e[l])return!1;let r=Date.now()-t.now;return t.maxAge?r>t.maxAge:e[l]&&r>e[l]},g=e=>{if(e[s]>e[n])for(let t=e[c].tail;e[s]>e[n]&&null!==t;){let r=t.prev;E(e,t),t=r}},E=(e,t)=>{if(t){let r=t.value;e[u]&&e[u](r.key,r.value),e[s]-=r.length,e[p].delete(r.key),e[c].removeNode(t)}};class b{constructor(e,t,r,i,n){this.key=e,this.value=t,this.length=r,this.now=i,this.maxAge=n||0}}let S=(e,t,r,i)=>{let n=r.value;v(e,n)&&(E(e,r),e[a]||(n=void 0)),n&&t.call(i,n.value,n.key,e)};e.exports=d},87553:e=>{function t(e,t,r,i){return Math.round(e/r)+" "+i+(t>=1.5*r?"s":"")}e.exports=function(e,r){r=r||{};var i,n,s=typeof e;if("string"===s&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var r=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*r;case"weeks":case"week":case"w":return 6048e5*r;case"days":case"day":case"d":return 864e5*r;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*r;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*r;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*r;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}(e);if("number"===s&&isFinite(e))return r.long?(i=Math.abs(e))>=864e5?t(e,i,864e5,"day"):i>=36e5?t(e,i,36e5,"hour"):i>=6e4?t(e,i,6e4,"minute"):i>=1e3?t(e,i,1e3,"second"):e+" ms":(n=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":n>=36e5?Math.round(e/36e5)+"h":n>=6e4?Math.round(e/6e4)+"m":n>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},78070:(e,t,r)=>{"use strict";Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return i.NextResponse}});let i=r(70457)},59026:(e,t,r)=>{/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */var i=r(14300),n=i.Buffer;function s(e,t){for(var r in e)t[r]=e[r]}function o(e,t,r){return n(e,t,r)}n.from&&n.alloc&&n.allocUnsafe&&n.allocUnsafeSlow?e.exports=i:(s(i,t),t.Buffer=o),o.prototype=Object.create(n.prototype),s(n,o),o.from=function(e,t,r){if("number"==typeof e)throw TypeError("Argument must not be a number");return n(e,t,r)},o.alloc=function(e,t,r){if("number"!=typeof e)throw TypeError("Argument must be a number");var i=n(e);return void 0!==t?"string"==typeof r?i.fill(t,r):i.fill(t):i.fill(0),i},o.allocUnsafe=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return n(e)},o.allocUnsafeSlow=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return i.SlowBuffer(e)}},71269:(e,t,r)=>{let i=Symbol("SemVer ANY");class n{static get ANY(){return i}constructor(e,t){if(t=s(t),e instanceof n){if(!!t.loose===e.loose)return e;e=e.value}u("comparator",e=e.trim().split(/\s+/).join(" "),t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===i?this.value="":this.value=this.operator+this.semver.version,u("comp",this)}parse(e){let t=this.options.loose?o[a.COMPARATORLOOSE]:o[a.COMPARATOR],r=e.match(t);if(!r)throw TypeError(`Invalid comparator: ${e}`);this.operator=void 0!==r[1]?r[1]:"","="===this.operator&&(this.operator=""),r[2]?this.semver=new h(r[2],this.options.loose):this.semver=i}toString(){return this.value}test(e){if(u("Comparator.test",e,this.options.loose),this.semver===i||e===i)return!0;if("string"==typeof e)try{e=new h(e,this.options)}catch(e){return!1}return l(e,this.operator,this.semver,this.options)}intersects(e,t){if(!(e instanceof n))throw TypeError("a Comparator is required");return""===this.operator?""===this.value||new c(e.value,t).test(this.value):""===e.operator?""===e.value||new c(this.value,t).test(e.semver):!((t=s(t)).includePrerelease&&("<0.0.0-0"===this.value||"<0.0.0-0"===e.value)||!t.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0")))&&!!(this.operator.startsWith(">")&&e.operator.startsWith(">")||this.operator.startsWith("<")&&e.operator.startsWith("<")||this.semver.version===e.semver.version&&this.operator.includes("=")&&e.operator.includes("=")||l(this.semver,"<",e.semver,t)&&this.operator.startsWith(">")&&e.operator.startsWith("<")||l(this.semver,">",e.semver,t)&&this.operator.startsWith("<")&&e.operator.startsWith(">"))}}e.exports=n;let s=r(21875),{safeRe:o,t:a}=r(20601),l=r(81448),u=r(10893),h=r(8340),c=r(98965)},98965:(e,t,r)=>{class i{constructor(e,t){if(t=s(t),e instanceof i){if(!!t.loose===e.loose&&!!t.includePrerelease===e.includePrerelease)return e;return new i(e.raw,t)}if(e instanceof o)return this.raw=e.value,this.set=[[e]],this.format(),this;if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e.trim().split(/\s+/).join(" "),this.set=this.raw.split("||").map(e=>this.parseRange(e.trim())).filter(e=>e.length),!this.set.length)throw TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let e=this.set[0];if(this.set=this.set.filter(e=>!y(e[0])),0===this.set.length)this.set=[e];else if(this.set.length>1){for(let e of this.set)if(1===e.length&&v(e[0])){this.set=[e];break}}}this.format()}format(){return this.range=this.set.map(e=>e.join(" ").trim()).join("||").trim(),this.range}toString(){return this.range}parseRange(e){let t=((this.options.includePrerelease&&m)|(this.options.loose&&d))+":"+e,r=n.get(t);if(r)return r;let i=this.options.loose,s=i?u[h.HYPHENRANGELOOSE]:u[h.HYPHENRANGE];a("hyphen replace",e=e.replace(s,T(this.options.includePrerelease))),a("comparator trim",e=e.replace(u[h.COMPARATORTRIM],c)),a("tilde trim",e=e.replace(u[h.TILDETRIM],p)),a("caret trim",e=e.replace(u[h.CARETTRIM],f));let l=e.split(" ").map(e=>E(e,this.options)).join(" ").split(/\s+/).map(e=>A(e,this.options));i&&(l=l.filter(e=>(a("loose invalid filter",e,this.options),!!e.match(u[h.COMPARATORLOOSE])))),a("range list",l);let v=new Map;for(let e of l.map(e=>new o(e,this.options))){if(y(e))return[e];v.set(e.value,e)}v.size>1&&v.has("")&&v.delete("");let g=[...v.values()];return n.set(t,g),g}intersects(e,t){if(!(e instanceof i))throw TypeError("a Range is required");return this.set.some(r=>g(r,t)&&e.set.some(e=>g(e,t)&&r.every(r=>e.every(e=>r.intersects(e,t)))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new l(e,this.options)}catch(e){return!1}for(let t=0;t<this.set.length;t++)if(N(this.set[t],e,this.options))return!0;return!1}}e.exports=i;let n=new(r(22680))({max:1e3}),s=r(21875),o=r(71269),a=r(10893),l=r(8340),{safeRe:u,t:h,comparatorTrimReplace:c,tildeTrimReplace:p,caretTrimReplace:f}=r(20601),{FLAG_INCLUDE_PRERELEASE:m,FLAG_LOOSE:d}=r(7943),y=e=>"<0.0.0-0"===e.value,v=e=>""===e.value,g=(e,t)=>{let r=!0,i=e.slice(),n=i.pop();for(;r&&i.length;)r=i.every(e=>n.intersects(e,t)),n=i.pop();return r},E=(e,t)=>(a("comp",e,t),a("caret",e=$(e,t)),a("tildes",e=S(e,t)),a("xrange",e=I(e,t)),a("stars",e=O(e,t)),e),b=e=>!e||"x"===e.toLowerCase()||"*"===e,S=(e,t)=>e.trim().split(/\s+/).map(e=>w(e,t)).join(" "),w=(e,t)=>{let r=t.loose?u[h.TILDELOOSE]:u[h.TILDE];return e.replace(r,(t,r,i,n,s)=>{let o;return a("tilde",e,t,r,i,n,s),b(r)?o="":b(i)?o=`>=${r}.0.0 <${+r+1}.0.0-0`:b(n)?o=`>=${r}.${i}.0 <${r}.${+i+1}.0-0`:s?(a("replaceTilde pr",s),o=`>=${r}.${i}.${n}-${s} <${r}.${+i+1}.0-0`):o=`>=${r}.${i}.${n} <${r}.${+i+1}.0-0`,a("tilde return",o),o})},$=(e,t)=>e.trim().split(/\s+/).map(e=>R(e,t)).join(" "),R=(e,t)=>{a("caret",e,t);let r=t.loose?u[h.CARETLOOSE]:u[h.CARET],i=t.includePrerelease?"-0":"";return e.replace(r,(t,r,n,s,o)=>{let l;return a("caret",e,t,r,n,s,o),b(r)?l="":b(n)?l=`>=${r}.0.0${i} <${+r+1}.0.0-0`:b(s)?l="0"===r?`>=${r}.${n}.0${i} <${r}.${+n+1}.0-0`:`>=${r}.${n}.0${i} <${+r+1}.0.0-0`:o?(a("replaceCaret pr",o),l="0"===r?"0"===n?`>=${r}.${n}.${s}-${o} <${r}.${n}.${+s+1}-0`:`>=${r}.${n}.${s}-${o} <${r}.${+n+1}.0-0`:`>=${r}.${n}.${s}-${o} <${+r+1}.0.0-0`):(a("no pr"),l="0"===r?"0"===n?`>=${r}.${n}.${s}${i} <${r}.${n}.${+s+1}-0`:`>=${r}.${n}.${s}${i} <${r}.${+n+1}.0-0`:`>=${r}.${n}.${s} <${+r+1}.0.0-0`),a("caret return",l),l})},I=(e,t)=>(a("replaceXRanges",e,t),e.split(/\s+/).map(e=>x(e,t)).join(" ")),x=(e,t)=>{e=e.trim();let r=t.loose?u[h.XRANGELOOSE]:u[h.XRANGE];return e.replace(r,(r,i,n,s,o,l)=>{a("xRange",e,r,i,n,s,o,l);let u=b(n),h=u||b(s),c=h||b(o);return"="===i&&c&&(i=""),l=t.includePrerelease?"-0":"",u?r=">"===i||"<"===i?"<0.0.0-0":"*":i&&c?(h&&(s=0),o=0,">"===i?(i=">=",h?(n=+n+1,s=0):s=+s+1,o=0):"<="===i&&(i="<",h?n=+n+1:s=+s+1),"<"===i&&(l="-0"),r=`${i+n}.${s}.${o}${l}`):h?r=`>=${n}.0.0${l} <${+n+1}.0.0-0`:c&&(r=`>=${n}.${s}.0${l} <${n}.${+s+1}.0-0`),a("xRange return",r),r})},O=(e,t)=>(a("replaceStars",e,t),e.trim().replace(u[h.STAR],"")),A=(e,t)=>(a("replaceGTE0",e,t),e.trim().replace(u[t.includePrerelease?h.GTE0PRE:h.GTE0],"")),T=e=>(t,r,i,n,s,o,a,l,u,h,c,p,f)=>(r=b(i)?"":b(n)?`>=${i}.0.0${e?"-0":""}`:b(s)?`>=${i}.${n}.0${e?"-0":""}`:o?`>=${r}`:`>=${r}${e?"-0":""}`,l=b(u)?"":b(h)?`<${+u+1}.0.0-0`:b(c)?`<${u}.${+h+1}.0-0`:p?`<=${u}.${h}.${c}-${p}`:e?`<${u}.${h}.${+c+1}-0`:`<=${l}`,`${r} ${l}`.trim()),N=(e,t,r)=>{for(let r=0;r<e.length;r++)if(!e[r].test(t))return!1;if(t.prerelease.length&&!r.includePrerelease){for(let r=0;r<e.length;r++)if(a(e[r].semver),e[r].semver!==o.ANY&&e[r].semver.prerelease.length>0){let i=e[r].semver;if(i.major===t.major&&i.minor===t.minor&&i.patch===t.patch)return!0}return!1}return!0}},8340:(e,t,r)=>{let i=r(10893),{MAX_LENGTH:n,MAX_SAFE_INTEGER:s}=r(7943),{safeRe:o,t:a}=r(20601),l=r(21875),{compareIdentifiers:u}=r(28369);class h{constructor(e,t){if(t=l(t),e instanceof h){if(!!t.loose===e.loose&&!!t.includePrerelease===e.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>n)throw TypeError(`version is longer than ${n} characters`);i("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;let r=e.trim().match(t.loose?o[a.LOOSE]:o[a.FULL]);if(!r)throw TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>s||this.major<0)throw TypeError("Invalid major version");if(this.minor>s||this.minor<0)throw TypeError("Invalid minor version");if(this.patch>s||this.patch<0)throw TypeError("Invalid patch version");r[4]?this.prerelease=r[4].split(".").map(e=>{if(/^[0-9]+$/.test(e)){let t=+e;if(t>=0&&t<s)return t}return e}):this.prerelease=[],this.build=r[5]?r[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(i("SemVer.compare",this.version,this.options,e),!(e instanceof h)){if("string"==typeof e&&e===this.version)return 0;e=new h(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof h||(e=new h(e,this.options)),u(this.major,e.major)||u(this.minor,e.minor)||u(this.patch,e.patch)}comparePre(e){if(e instanceof h||(e=new h(e,this.options)),this.prerelease.length&&!e.prerelease.length)return -1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{let r=this.prerelease[t],n=e.prerelease[t];if(i("prerelease compare",t,r,n),void 0===r&&void 0===n)return 0;if(void 0===n)return 1;if(void 0===r)return -1;if(r===n)continue;else return u(r,n)}while(++t)}compareBuild(e){e instanceof h||(e=new h(e,this.options));let t=0;do{let r=this.build[t],n=e.build[t];if(i("prerelease compare",t,r,n),void 0===r&&void 0===n)return 0;if(void 0===n)return 1;if(void 0===r)return -1;if(r===n)continue;else return u(r,n)}while(++t)}inc(e,t,r){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,r),this.inc("pre",t,r);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t,r),this.inc("pre",t,r);break;case"major":(0!==this.minor||0!==this.patch||0===this.prerelease.length)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(0!==this.patch||0===this.prerelease.length)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{let e=Number(r)?1:0;if(!t&&!1===r)throw Error("invalid increment argument: identifier is empty");if(0===this.prerelease.length)this.prerelease=[e];else{let i=this.prerelease.length;for(;--i>=0;)"number"==typeof this.prerelease[i]&&(this.prerelease[i]++,i=-2);if(-1===i){if(t===this.prerelease.join(".")&&!1===r)throw Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(t){let i=[t,e];!1===r&&(i=[t]),0===u(this.prerelease[0],t)?isNaN(this.prerelease[1])&&(this.prerelease=i):this.prerelease=i}break}default:throw Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}e.exports=h},98388:(e,t,r)=>{let i=r(60384);e.exports=(e,t)=>{let r=i(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null}},81448:(e,t,r)=>{let i=r(48218),n=r(61686),s=r(1793),o=r(8979),a=r(93081),l=r(19488);e.exports=(e,t,r,u)=>{switch(t){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e===r;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e!==r;case"":case"=":case"==":return i(e,r,u);case"!=":return n(e,r,u);case">":return s(e,r,u);case">=":return o(e,r,u);case"<":return a(e,r,u);case"<=":return l(e,r,u);default:throw TypeError(`Invalid operator: ${t}`)}}},1643:(e,t,r)=>{let i=r(8340),n=r(60384),{safeRe:s,t:o}=r(20601);e.exports=(e,t)=>{if(e instanceof i)return e;if("number"==typeof e&&(e=String(e)),"string"!=typeof e)return null;let r=null;if((t=t||{}).rtl){let t;for(;(t=s[o.COERCERTL].exec(e))&&(!r||r.index+r[0].length!==e.length);)r&&t.index+t[0].length===r.index+r[0].length||(r=t),s[o.COERCERTL].lastIndex=t.index+t[1].length+t[2].length;s[o.COERCERTL].lastIndex=-1}else r=e.match(s[o.COERCE]);return null===r?null:n(`${r[2]}.${r[3]||"0"}.${r[4]||"0"}`,t)}},24220:(e,t,r)=>{let i=r(8340);e.exports=(e,t,r)=>{let n=new i(e,r),s=new i(t,r);return n.compare(s)||n.compareBuild(s)}},51847:(e,t,r)=>{let i=r(85998);e.exports=(e,t)=>i(e,t,!0)},85998:(e,t,r)=>{let i=r(8340);e.exports=(e,t,r)=>new i(e,r).compare(new i(t,r))},72694:(e,t,r)=>{let i=r(60384);e.exports=(e,t)=>{let r=i(e,null,!0),n=i(t,null,!0),s=r.compare(n);if(0===s)return null;let o=s>0,a=o?r:n,l=o?n:r,u=!!a.prerelease.length;if(l.prerelease.length&&!u)return l.patch||l.minor?a.patch?"patch":a.minor?"minor":"major":"major";let h=u?"pre":"";return r.major!==n.major?h+"major":r.minor!==n.minor?h+"minor":r.patch!==n.patch?h+"patch":"prerelease"}},48218:(e,t,r)=>{let i=r(85998);e.exports=(e,t,r)=>0===i(e,t,r)},1793:(e,t,r)=>{let i=r(85998);e.exports=(e,t,r)=>i(e,t,r)>0},8979:(e,t,r)=>{let i=r(85998);e.exports=(e,t,r)=>i(e,t,r)>=0},87248:(e,t,r)=>{let i=r(8340);e.exports=(e,t,r,n,s)=>{"string"==typeof r&&(s=n,n=r,r=void 0);try{return new i(e instanceof i?e.version:e,r).inc(t,n,s).version}catch(e){return null}}},93081:(e,t,r)=>{let i=r(85998);e.exports=(e,t,r)=>0>i(e,t,r)},19488:(e,t,r)=>{let i=r(85998);e.exports=(e,t,r)=>0>=i(e,t,r)},68663:(e,t,r)=>{let i=r(8340);e.exports=(e,t)=>new i(e,t).major},52891:(e,t,r)=>{let i=r(8340);e.exports=(e,t)=>new i(e,t).minor},61686:(e,t,r)=>{let i=r(85998);e.exports=(e,t,r)=>0!==i(e,t,r)},60384:(e,t,r)=>{let i=r(8340);e.exports=(e,t,r=!1)=>{if(e instanceof i)return e;try{return new i(e,t)}catch(e){if(!r)return null;throw e}}},10817:(e,t,r)=>{let i=r(8340);e.exports=(e,t)=>new i(e,t).patch},1185:(e,t,r)=>{let i=r(60384);e.exports=(e,t)=>{let r=i(e,t);return r&&r.prerelease.length?r.prerelease:null}},68798:(e,t,r)=>{let i=r(85998);e.exports=(e,t,r)=>i(t,e,r)},21994:(e,t,r)=>{let i=r(24220);e.exports=(e,t)=>e.sort((e,r)=>i(r,e,t))},77987:(e,t,r)=>{let i=r(98965);e.exports=(e,t,r)=>{try{t=new i(t,r)}catch(e){return!1}return t.test(e)}},76558:(e,t,r)=>{let i=r(24220);e.exports=(e,t)=>e.sort((e,r)=>i(e,r,t))},65506:(e,t,r)=>{let i=r(60384);e.exports=(e,t)=>{let r=i(e,t);return r?r.version:null}},9743:(e,t,r)=>{let i=r(20601),n=r(7943),s=r(8340),o=r(28369),a=r(60384),l=r(65506),u=r(98388),h=r(87248),c=r(72694),p=r(68663),f=r(52891),m=r(10817),d=r(1185),y=r(85998),v=r(68798),g=r(51847),E=r(24220),b=r(76558),S=r(21994),w=r(1793),$=r(93081),R=r(48218),I=r(61686),x=r(8979),O=r(19488),A=r(81448),T=r(1643),N=r(71269),L=r(98965),j=r(77987),P=r(9503),k=r(65389),D=r(79318),C=r(24323),M=r(89637),G=r(38806),B=r(2808),F=r(50147),_=r(35586),U=r(47190),K=r(70011);e.exports={parse:a,valid:l,clean:u,inc:h,diff:c,major:p,minor:f,patch:m,prerelease:d,compare:y,rcompare:v,compareLoose:g,compareBuild:E,sort:b,rsort:S,gt:w,lt:$,eq:R,neq:I,gte:x,lte:O,cmp:A,coerce:T,Comparator:N,Range:L,satisfies:j,toComparators:P,maxSatisfying:k,minSatisfying:D,minVersion:C,validRange:M,outside:G,gtr:B,ltr:F,intersects:_,simplifyRange:U,subset:K,SemVer:s,re:i.re,src:i.src,tokens:i.t,SEMVER_SPEC_VERSION:n.SEMVER_SPEC_VERSION,RELEASE_TYPES:n.RELEASE_TYPES,compareIdentifiers:o.compareIdentifiers,rcompareIdentifiers:o.rcompareIdentifiers}},7943:e=>{let t=Number.MAX_SAFE_INTEGER||9007199254740991;e.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:t,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},10893:e=>{let t="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};e.exports=t},28369:e=>{let t=/^[0-9]+$/,r=(e,r)=>{let i=t.test(e),n=t.test(r);return i&&n&&(e=+e,r=+r),e===r?0:i&&!n?-1:n&&!i?1:e<r?-1:1};e.exports={compareIdentifiers:r,rcompareIdentifiers:(e,t)=>r(t,e)}},21875:e=>{let t=Object.freeze({loose:!0}),r=Object.freeze({});e.exports=e=>e?"object"!=typeof e?t:e:r},20601:(e,t,r)=>{let{MAX_SAFE_COMPONENT_LENGTH:i,MAX_SAFE_BUILD_LENGTH:n,MAX_LENGTH:s}=r(7943),o=r(10893),a=(t=e.exports={}).re=[],l=t.safeRe=[],u=t.src=[],h=t.t={},c=0,p="[a-zA-Z0-9-]",f=[["\\s",1],["\\d",s],[p,n]],m=e=>{for(let[t,r]of f)e=e.split(`${t}*`).join(`${t}{0,${r}}`).split(`${t}+`).join(`${t}{1,${r}}`);return e},d=(e,t,r)=>{let i=m(t),n=c++;o(e,n,t),h[e]=n,u[n]=t,a[n]=new RegExp(t,r?"g":void 0),l[n]=new RegExp(i,r?"g":void 0)};d("NUMERICIDENTIFIER","0|[1-9]\\d*"),d("NUMERICIDENTIFIERLOOSE","\\d+"),d("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${p}*`),d("MAINVERSION",`(${u[h.NUMERICIDENTIFIER]})\\.(${u[h.NUMERICIDENTIFIER]})\\.(${u[h.NUMERICIDENTIFIER]})`),d("MAINVERSIONLOOSE",`(${u[h.NUMERICIDENTIFIERLOOSE]})\\.(${u[h.NUMERICIDENTIFIERLOOSE]})\\.(${u[h.NUMERICIDENTIFIERLOOSE]})`),d("PRERELEASEIDENTIFIER",`(?:${u[h.NUMERICIDENTIFIER]}|${u[h.NONNUMERICIDENTIFIER]})`),d("PRERELEASEIDENTIFIERLOOSE",`(?:${u[h.NUMERICIDENTIFIERLOOSE]}|${u[h.NONNUMERICIDENTIFIER]})`),d("PRERELEASE",`(?:-(${u[h.PRERELEASEIDENTIFIER]}(?:\\.${u[h.PRERELEASEIDENTIFIER]})*))`),d("PRERELEASELOOSE",`(?:-?(${u[h.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${u[h.PRERELEASEIDENTIFIERLOOSE]})*))`),d("BUILDIDENTIFIER",`${p}+`),d("BUILD",`(?:\\+(${u[h.BUILDIDENTIFIER]}(?:\\.${u[h.BUILDIDENTIFIER]})*))`),d("FULLPLAIN",`v?${u[h.MAINVERSION]}${u[h.PRERELEASE]}?${u[h.BUILD]}?`),d("FULL",`^${u[h.FULLPLAIN]}$`),d("LOOSEPLAIN",`[v=\\s]*${u[h.MAINVERSIONLOOSE]}${u[h.PRERELEASELOOSE]}?${u[h.BUILD]}?`),d("LOOSE",`^${u[h.LOOSEPLAIN]}$`),d("GTLT","((?:<|>)?=?)"),d("XRANGEIDENTIFIERLOOSE",`${u[h.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),d("XRANGEIDENTIFIER",`${u[h.NUMERICIDENTIFIER]}|x|X|\\*`),d("XRANGEPLAIN",`[v=\\s]*(${u[h.XRANGEIDENTIFIER]})(?:\\.(${u[h.XRANGEIDENTIFIER]})(?:\\.(${u[h.XRANGEIDENTIFIER]})(?:${u[h.PRERELEASE]})?${u[h.BUILD]}?)?)?`),d("XRANGEPLAINLOOSE",`[v=\\s]*(${u[h.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[h.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[h.XRANGEIDENTIFIERLOOSE]})(?:${u[h.PRERELEASELOOSE]})?${u[h.BUILD]}?)?)?`),d("XRANGE",`^${u[h.GTLT]}\\s*${u[h.XRANGEPLAIN]}$`),d("XRANGELOOSE",`^${u[h.GTLT]}\\s*${u[h.XRANGEPLAINLOOSE]}$`),d("COERCE",`(^|[^\\d])(\\d{1,${i}})(?:\\.(\\d{1,${i}}))?(?:\\.(\\d{1,${i}}))?(?:$|[^\\d])`),d("COERCERTL",u[h.COERCE],!0),d("LONETILDE","(?:~>?)"),d("TILDETRIM",`(\\s*)${u[h.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",d("TILDE",`^${u[h.LONETILDE]}${u[h.XRANGEPLAIN]}$`),d("TILDELOOSE",`^${u[h.LONETILDE]}${u[h.XRANGEPLAINLOOSE]}$`),d("LONECARET","(?:\\^)"),d("CARETTRIM",`(\\s*)${u[h.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",d("CARET",`^${u[h.LONECARET]}${u[h.XRANGEPLAIN]}$`),d("CARETLOOSE",`^${u[h.LONECARET]}${u[h.XRANGEPLAINLOOSE]}$`),d("COMPARATORLOOSE",`^${u[h.GTLT]}\\s*(${u[h.LOOSEPLAIN]})$|^$`),d("COMPARATOR",`^${u[h.GTLT]}\\s*(${u[h.FULLPLAIN]})$|^$`),d("COMPARATORTRIM",`(\\s*)${u[h.GTLT]}\\s*(${u[h.LOOSEPLAIN]}|${u[h.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",d("HYPHENRANGE",`^\\s*(${u[h.XRANGEPLAIN]})\\s+-\\s+(${u[h.XRANGEPLAIN]})\\s*$`),d("HYPHENRANGELOOSE",`^\\s*(${u[h.XRANGEPLAINLOOSE]})\\s+-\\s+(${u[h.XRANGEPLAINLOOSE]})\\s*$`),d("STAR","(<|>)?=?\\s*\\*"),d("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),d("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},2808:(e,t,r)=>{let i=r(38806);e.exports=(e,t,r)=>i(e,t,">",r)},35586:(e,t,r)=>{let i=r(98965);e.exports=(e,t,r)=>(e=new i(e,r),t=new i(t,r),e.intersects(t,r))},50147:(e,t,r)=>{let i=r(38806);e.exports=(e,t,r)=>i(e,t,"<",r)},65389:(e,t,r)=>{let i=r(8340),n=r(98965);e.exports=(e,t,r)=>{let s=null,o=null,a=null;try{a=new n(t,r)}catch(e){return null}return e.forEach(e=>{a.test(e)&&(!s||-1===o.compare(e))&&(o=new i(s=e,r))}),s}},79318:(e,t,r)=>{let i=r(8340),n=r(98965);e.exports=(e,t,r)=>{let s=null,o=null,a=null;try{a=new n(t,r)}catch(e){return null}return e.forEach(e=>{a.test(e)&&(!s||1===o.compare(e))&&(o=new i(s=e,r))}),s}},24323:(e,t,r)=>{let i=r(8340),n=r(98965),s=r(1793);e.exports=(e,t)=>{e=new n(e,t);let r=new i("0.0.0");if(e.test(r)||(r=new i("0.0.0-0"),e.test(r)))return r;r=null;for(let t=0;t<e.set.length;++t){let n=e.set[t],o=null;n.forEach(e=>{let t=new i(e.semver.version);switch(e.operator){case">":0===t.prerelease.length?t.patch++:t.prerelease.push(0),t.raw=t.format();case"":case">=":(!o||s(t,o))&&(o=t);break;case"<":case"<=":break;default:throw Error(`Unexpected operation: ${e.operator}`)}}),o&&(!r||s(r,o))&&(r=o)}return r&&e.test(r)?r:null}},38806:(e,t,r)=>{let i=r(8340),n=r(71269),{ANY:s}=n,o=r(98965),a=r(77987),l=r(1793),u=r(93081),h=r(19488),c=r(8979);e.exports=(e,t,r,p)=>{let f,m,d,y,v;switch(e=new i(e,p),t=new o(t,p),r){case">":f=l,m=h,d=u,y=">",v=">=";break;case"<":f=u,m=c,d=l,y="<",v="<=";break;default:throw TypeError('Must provide a hilo val of "<" or ">"')}if(a(e,t,p))return!1;for(let r=0;r<t.set.length;++r){let i=t.set[r],o=null,a=null;if(i.forEach(e=>{e.semver===s&&(e=new n(">=0.0.0")),o=o||e,a=a||e,f(e.semver,o.semver,p)?o=e:d(e.semver,a.semver,p)&&(a=e)}),o.operator===y||o.operator===v||(!a.operator||a.operator===y)&&m(e,a.semver)||a.operator===v&&d(e,a.semver))return!1}return!0}},47190:(e,t,r)=>{let i=r(77987),n=r(85998);e.exports=(e,t,r)=>{let s=[],o=null,a=null,l=e.sort((e,t)=>n(e,t,r));for(let e of l)i(e,t,r)?(a=e,o||(o=e)):(a&&s.push([o,a]),a=null,o=null);o&&s.push([o,null]);let u=[];for(let[e,t]of s)e===t?u.push(e):t||e!==l[0]?t?e===l[0]?u.push(`<=${t}`):u.push(`${e} - ${t}`):u.push(`>=${e}`):u.push("*");let h=u.join(" || "),c="string"==typeof t.raw?t.raw:String(t);return h.length<c.length?h:t}},70011:(e,t,r)=>{let i=r(98965),n=r(71269),{ANY:s}=n,o=r(77987),a=r(85998),l=[new n(">=0.0.0-0")],u=[new n(">=0.0.0")],h=(e,t,r)=>{let i,n,h,f,m,d,y;if(e===t)return!0;if(1===e.length&&e[0].semver===s){if(1===t.length&&t[0].semver===s)return!0;e=r.includePrerelease?l:u}if(1===t.length&&t[0].semver===s){if(r.includePrerelease)return!0;t=u}let v=new Set;for(let t of e)">"===t.operator||">="===t.operator?i=c(i,t,r):"<"===t.operator||"<="===t.operator?n=p(n,t,r):v.add(t.semver);if(v.size>1||i&&n&&((h=a(i.semver,n.semver,r))>0||0===h&&(">="!==i.operator||"<="!==n.operator)))return null;for(let e of v){if(i&&!o(e,String(i),r)||n&&!o(e,String(n),r))return null;for(let i of t)if(!o(e,String(i),r))return!1;return!0}let g=!!n&&!r.includePrerelease&&!!n.semver.prerelease.length&&n.semver,E=!!i&&!r.includePrerelease&&!!i.semver.prerelease.length&&i.semver;for(let e of(g&&1===g.prerelease.length&&"<"===n.operator&&0===g.prerelease[0]&&(g=!1),t)){if(y=y||">"===e.operator||">="===e.operator,d=d||"<"===e.operator||"<="===e.operator,i){if(E&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===E.major&&e.semver.minor===E.minor&&e.semver.patch===E.patch&&(E=!1),">"===e.operator||">="===e.operator){if((f=c(i,e,r))===e&&f!==i)return!1}else if(">="===i.operator&&!o(i.semver,String(e),r))return!1}if(n){if(g&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===g.major&&e.semver.minor===g.minor&&e.semver.patch===g.patch&&(g=!1),"<"===e.operator||"<="===e.operator){if((m=p(n,e,r))===e&&m!==n)return!1}else if("<="===n.operator&&!o(n.semver,String(e),r))return!1}if(!e.operator&&(n||i)&&0!==h)return!1}return(!i||!d||!!n||0===h)&&(!n||!y||!!i||0===h)&&!E&&!g},c=(e,t,r)=>{if(!e)return t;let i=a(e.semver,t.semver,r);return i>0?e:i<0?t:">"===t.operator&&">="===e.operator?t:e},p=(e,t,r)=>{if(!e)return t;let i=a(e.semver,t.semver,r);return i<0?e:i>0?t:"<"===t.operator&&"<="===e.operator?t:e};e.exports=(e,t,r={})=>{if(e===t)return!0;e=new i(e,r),t=new i(t,r);let n=!1;e:for(let i of e.set){for(let e of t.set){let t=h(i,e,r);if(n=n||null!==t,t)continue e}if(n)return!1}return!0}},9503:(e,t,r)=>{let i=r(98965);e.exports=(e,t)=>new i(e,t).set.map(e=>e.map(e=>e.value).join(" ").trim().split(" "))},89637:(e,t,r)=>{let i=r(98965);e.exports=(e,t)=>{try{return new i(e,t).range||"*"}catch(e){return null}}},74733:e=>{"use strict";e.exports=function(e){e.prototype[Symbol.iterator]=function*(){for(let e=this.head;e;e=e.next)yield e.value}}},50826:(e,t,r)=>{"use strict";function i(e){var t=this;if(t instanceof i||(t=new i),t.tail=null,t.head=null,t.length=0,e&&"function"==typeof e.forEach)e.forEach(function(e){t.push(e)});else if(arguments.length>0)for(var r=0,n=arguments.length;r<n;r++)t.push(arguments[r]);return t}function n(e,t,r,i){if(!(this instanceof n))return new n(e,t,r,i);this.list=i,this.value=e,t?(t.next=this,this.prev=t):this.prev=null,r?(r.prev=this,this.next=r):this.next=null}e.exports=i,i.Node=n,i.create=i,i.prototype.removeNode=function(e){if(e.list!==this)throw Error("removing node which does not belong to this list");var t=e.next,r=e.prev;return t&&(t.prev=r),r&&(r.next=t),e===this.head&&(this.head=t),e===this.tail&&(this.tail=r),e.list.length--,e.next=null,e.prev=null,e.list=null,t},i.prototype.unshiftNode=function(e){if(e!==this.head){e.list&&e.list.removeNode(e);var t=this.head;e.list=this,e.next=t,t&&(t.prev=e),this.head=e,this.tail||(this.tail=e),this.length++}},i.prototype.pushNode=function(e){if(e!==this.tail){e.list&&e.list.removeNode(e);var t=this.tail;e.list=this,e.prev=t,t&&(t.next=e),this.tail=e,this.head||(this.head=e),this.length++}},i.prototype.push=function(){for(var e,t=0,r=arguments.length;t<r;t++)e=arguments[t],this.tail=new n(e,this.tail,null,this),this.head||(this.head=this.tail),this.length++;return this.length},i.prototype.unshift=function(){for(var e,t=0,r=arguments.length;t<r;t++)e=arguments[t],this.head=new n(e,null,this.head,this),this.tail||(this.tail=this.head),this.length++;return this.length},i.prototype.pop=function(){if(this.tail){var e=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,e}},i.prototype.shift=function(){if(this.head){var e=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,e}},i.prototype.forEach=function(e,t){t=t||this;for(var r=this.head,i=0;null!==r;i++)e.call(t,r.value,i,this),r=r.next},i.prototype.forEachReverse=function(e,t){t=t||this;for(var r=this.tail,i=this.length-1;null!==r;i--)e.call(t,r.value,i,this),r=r.prev},i.prototype.get=function(e){for(var t=0,r=this.head;null!==r&&t<e;t++)r=r.next;if(t===e&&null!==r)return r.value},i.prototype.getReverse=function(e){for(var t=0,r=this.tail;null!==r&&t<e;t++)r=r.prev;if(t===e&&null!==r)return r.value},i.prototype.map=function(e,t){t=t||this;for(var r=new i,n=this.head;null!==n;)r.push(e.call(t,n.value,this)),n=n.next;return r},i.prototype.mapReverse=function(e,t){t=t||this;for(var r=new i,n=this.tail;null!==n;)r.push(e.call(t,n.value,this)),n=n.prev;return r},i.prototype.reduce=function(e,t){var r,i=this.head;if(arguments.length>1)r=t;else if(this.head)i=this.head.next,r=this.head.value;else throw TypeError("Reduce of empty list with no initial value");for(var n=0;null!==i;n++)r=e(r,i.value,n),i=i.next;return r},i.prototype.reduceReverse=function(e,t){var r,i=this.tail;if(arguments.length>1)r=t;else if(this.tail)i=this.tail.prev,r=this.tail.value;else throw TypeError("Reduce of empty list with no initial value");for(var n=this.length-1;null!==i;n--)r=e(r,i.value,n),i=i.prev;return r},i.prototype.toArray=function(){for(var e=Array(this.length),t=0,r=this.head;null!==r;t++)e[t]=r.value,r=r.next;return e},i.prototype.toArrayReverse=function(){for(var e=Array(this.length),t=0,r=this.tail;null!==r;t++)e[t]=r.value,r=r.prev;return e},i.prototype.slice=function(e,t){(t=t||this.length)<0&&(t+=this.length),(e=e||0)<0&&(e+=this.length);var r=new i;if(t<e||t<0)return r;e<0&&(e=0),t>this.length&&(t=this.length);for(var n=0,s=this.head;null!==s&&n<e;n++)s=s.next;for(;null!==s&&n<t;n++,s=s.next)r.push(s.value);return r},i.prototype.sliceReverse=function(e,t){(t=t||this.length)<0&&(t+=this.length),(e=e||0)<0&&(e+=this.length);var r=new i;if(t<e||t<0)return r;e<0&&(e=0),t>this.length&&(t=this.length);for(var n=this.length,s=this.tail;null!==s&&n>t;n--)s=s.prev;for(;null!==s&&n>e;n--,s=s.prev)r.push(s.value);return r},i.prototype.splice=function(e,t,...r){e>this.length&&(e=this.length-1),e<0&&(e=this.length+e);for(var i=0,s=this.head;null!==s&&i<e;i++)s=s.next;for(var o=[],i=0;s&&i<t;i++)o.push(s.value),s=this.removeNode(s);null===s&&(s=this.tail),s!==this.head&&s!==this.tail&&(s=s.prev);for(var i=0;i<r.length;i++)s=function(e,t,r){var i=t===e.head?new n(r,null,t,e):new n(r,t,t.next,e);return null===i.next&&(e.tail=i),null===i.prev&&(e.head=i),e.length++,i}(this,s,r[i]);return o},i.prototype.reverse=function(){for(var e=this.head,t=this.tail,r=e;null!==r;r=r.prev){var i=r.prev;r.prev=r.next,r.next=i}return this.head=t,this.tail=e,this};try{r(74733)(i)}catch(e){}}};
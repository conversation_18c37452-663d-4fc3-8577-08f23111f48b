(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[982],{9192:function(e,t,n){Promise.resolve().then(n.bind(n,8155))},4257:function(e,t,n){"use strict";n.d(t,{L:function(){return a}});let a={facebook_icon:{src:"/_next/static/media/facebook_icon.cbcfc36d.png",height:58,width:58,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAbUlEQVR42m3HsRGCQBCG0X/sgArowytFG9BCqIESnLEHAhswNrqYmcvkAmB3P4Yh5WVPXCRP3ntvV2mf7B7sArtJGhsvkJfPAl7GRjUZxIs3hFOTcmsVvutvBZtyK+nfgRPA1OlQHvMwD+WpMxvnWUuxSavcBwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},googleplus_icon:{src:"/_next/static/media/googleplus_icon.15e2de32.png",height:59,width:59,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAe0lEQVR42mP4z8TA8Mv+z5x/M36bMzCAeAy/k/5Dwe9gBgaGZyL/P3z6f/3Xv59///97fIOT4b3d///H/s76v/3/g///f77WY7il8P/r+/+Hf73/9f//39dnhBkYGD41/f8PAv/+fyphgICXSV+3fNv4IoIBHfxnZGAAALhiS7/aN4AvAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},twitter_icon:{src:"/_next/static/media/twitter_icon.0d1dc581.png",height:59,width:59,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAeklEQVR42l3HvQnCUBiG0Uc7ax1AsLe10yFsrDODEziFhStYCO5gIThCGkHEgJqfC8mX5L4hJFVOd9AYbFOfyqOtoB1loJ5tgddM/0w/uxVOet4nxGspqtFCuWTfJeHcu0pnC02qoscUSA9eLa9kT+cTuKu7vHcMaQQNWbdKicv1GyQAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},profile_icon:{src:"/_next/static/media/profile_icon.fa2679c4.png",height:92,width:92,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABCklEQVR42iWPMU/CUACEn7/B+CPen3CS+BccTNyMm2kdpDoYiFEjVkgcQGOVONRookZUGlqf5Ukk9CkVU4ymxk2CU/e+5Shw2919NxwZqtf7o93ggzPmSKt6L5vNBvf9Nzoq+/1/KjwvUlUFiQWZINC0NFyXRcmAkjAMeTabwewUiRvlPMyt9BCMS6Ui6i7jxG+35fRMCgVlHt+VM7DDHFKTBHv6PhzHlqQbBHJ7N4eTDQW/1iWqO2vIryyi5jhgjwkghMc9IfBwexV/Xp/CXF5A7e4mfu908MRsTl5Fi9Y5j1ovz/ixL2CocyhurqJsHEWWbY+fZPQCNY8P+Jd1Liu6Jo31JZ7Eo3IAXaWfc0g8AF4AAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},logo:{src:"/_next/static/media/logo.c649e147.png",height:53,width:186,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAQAAADPnVVmAAAAKklEQVR42mNgaGdIY9BhcGQwZjBgMGHQYWCoZWhnMGSwY3BjyGSIYjAAAFAcBMpReGCWAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:2},arrow:{src:"/_next/static/media/arrow.35bdbbc1.png",height:16,width:18,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAQAAACfUMTVAAAATUlEQVR42mPQZNRgZGDQ4NC4qeHPwKDJxgADGvYazzRcGRgYNLk1eTR4NPkZGDS8NF5o+DBoHtI4p3lW44zmFY1tGp80fmGowDQD3RYA4awVkVQ4JrAAAAAASUVORK5CYII=",blurWidth:8,blurHeight:7},logo_light:{src:"/_next/static/media/logo_light.9ce1f99e.png",height:55,width:201,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAQAAADPnVVmAAAALElEQVR42mP41/Av9J/uP7d/5v8s/tn8M2X41/Sv9p/OP9t/rv9y/0X/MwIAZagUsO6duCoAAAAASUVORK5CYII=",blurWidth:8,blurHeight:2},blog_icon:{src:"/_next/static/media/blog_icon.6cf97bbc.png",height:32,width:32,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAVklEQVR42jXNMRGDQBAAwC2pGfozgA4KcjMMFIejN5IoSI8YLGAAKtbAQiil0xshNGky2J1GygccLrue1YKf22HQsUn8fTEpygwgFaGZpUVq4m03qxI8rIYRbx4WRDgAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},add_icon:{src:"/_next/static/media/add_icon.17426346.png",height:32,width:32,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAARElEQVR42mWNsQnAMAwEr3LwUMoGmiJKlf37HMZgg/+aP4EkRpKSZOYhaBI2kxboAqFRNOWTzqXxroGtILn3lePo8fYH8E4LJKezO8EAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},email_icon:{src:"/_next/static/media/email_icon.4caec7c6.png",height:32,width:32,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAUUlEQVR42l3NsQmAMAAEwHONrCBqlVJrG1dQ1MIt4pZmHQMBC7nu4f9pAEADg9lSzDoIsmQskiwQ7S5tcTlEotPmlqw1CB63qagV9N/ogP/tC+8IDv7EJZnRAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},upload_area:{src:"/_next/static/media/upload_area.1ee5fe3d.png",height:140,width:240,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAMAAABPT11nAAAANlBMVEX6+vr6+vn5+fn5+fj5+Pj4+Pj39/f29vb19fXz8/Py8vLv7+/u7u7r6+rp6ejj5evf4OXc3+fsgmBfAAAALUlEQVR42g3GtwEAIAwDMFMc0wn/P0s0Cc1UqqzBiPsSDWJ3HxSU19kzR8QgfRm1AShVawqCAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:5}};Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now()},8155:function(e,t,n){"use strict";n.r(t);var a=n(7437),o=n(4257),s=n(2173),r=n(6691),i=n.n(r),l=n(1396),c=n.n(l),d=n(4033),u=n(2265),A=n(7948);t.default=()=>{let[e,t]=(0,u.useState)([]),[n,r]=(0,u.useState)(!0),l=(0,d.useRouter)();(0,u.useEffect)(()=>{let e=localStorage.getItem("authToken"),t=localStorage.getItem("userId");if(!e||!t){l.push("/login");return}g(e)},[l]);let g=async e=>{try{r(!0);let n=await s.Z.get("/api/favorites",{headers:{Authorization:"Bearer ".concat(e)}});n.data.success?t(n.data.favorites||[]):A.toast.error("Failed to load favorites")}catch(e){console.error("Error fetching favorites:",e),A.toast.error("Failed to load favorites")}finally{r(!1)}},f=async n=>{try{let a=localStorage.getItem("authToken");(await s.Z.delete("/api/favorites?blogId=".concat(n),{headers:{Authorization:"Bearer ".concat(a)}})).data.success?(t(e.filter(e=>e._id.toString()!==n)),A.toast.success("Removed from favorites")):A.toast.error("Failed to remove from favorites")}catch(e){console.error("Error removing favorite:",e),A.toast.error("Failed to remove from favorites")}};return(0,a.jsx)("div",{className:"min-h-screen bg-gray-100",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"My Favorite Blogs"}),(0,a.jsx)(c(),{href:"/profile",className:"px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300",children:"Back to Profile"})]}),n?(0,a.jsx)("div",{className:"flex justify-center items-center py-16",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-black"})}):e.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-lg overflow-hidden shadow-md",children:[(0,a.jsx)("div",{className:"relative h-48",children:(0,a.jsx)(i(),{src:e.image||o.L.placeholder,alt:e.title,fill:!0,className:"object-cover"})}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 rounded-full",children:e.category}),(0,a.jsx)("button",{onClick:()=>f(e._id),className:"text-yellow-500 hover:text-yellow-700",title:"Remove from favorites",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,a.jsx)("polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"})})})]}),(0,a.jsx)("h2",{className:"text-lg font-semibold mb-2 line-clamp-2",children:e.title}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mb-3 line-clamp-3",dangerouslySetInnerHTML:{__html:e.description.substring(0,120)+"..."}}),(0,a.jsx)(c(),{href:"/blogs/".concat(e._id),className:"inline-block px-3 py-1 bg-black text-white text-sm rounded hover:bg-gray-800 transition",children:"Read Article"})]})]},e._id))}):(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[(0,a.jsx)("div",{className:"text-5xl mb-4",children:"⭐"}),(0,a.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"No favorites yet"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Start adding blogs to your favorites by clicking the star icon on blog posts."}),(0,a.jsx)(c(),{href:"/",className:"inline-block px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition",children:"Browse Blogs"})]})]})})}},4033:function(e,t,n){e.exports=n(5313)},7948:function(e,t,n){"use strict";n.r(t),n.d(t,{Bounce:function(){return S},Flip:function(){return M},Icons:function(){return L},Slide:function(){return P},ToastContainer:function(){return Q},Zoom:function(){return V},collapseToast:function(){return d},cssTransition:function(){return u},toast:function(){return R},useToast:function(){return w},useToastContainer:function(){return E}});var a=n(2265),o=function(){for(var e,t,n=0,a="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=function e(t){var n,a,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t)){var s=t.length;for(n=0;n<s;n++)t[n]&&(a=e(t[n]))&&(o&&(o+=" "),o+=a)}else for(a in t)t[a]&&(o&&(o+=" "),o+=a)}return o}(e))&&(a&&(a+=" "),a+=t);return a};let s=e=>"number"==typeof e&&!isNaN(e),r=e=>"string"==typeof e,i=e=>"function"==typeof e,l=e=>r(e)||i(e)?e:null,c=e=>(0,a.isValidElement)(e)||r(e)||i(e)||s(e);function d(e,t,n){void 0===n&&(n=300);let{scrollHeight:a,style:o}=e;requestAnimationFrame(()=>{o.minHeight="initial",o.height=a+"px",o.transition=`all ${n}ms`,requestAnimationFrame(()=>{o.height="0",o.padding="0",o.margin="0",setTimeout(t,n)})})}function u(e){let{enter:t,exit:n,appendPosition:o=!1,collapse:s=!0,collapseDuration:r=300}=e;return function(e){let{children:i,position:l,preventExitTransition:c,done:u,nodeRef:A,isIn:g,playToast:f}=e,m=o?`${t}--${l}`:t,p=o?`${n}--${l}`:n,h=(0,a.useRef)(0);return(0,a.useLayoutEffect)(()=>{let e=A.current,t=m.split(" "),n=a=>{a.target===A.current&&(f(),e.removeEventListener("animationend",n),e.removeEventListener("animationcancel",n),0===h.current&&"animationcancel"!==a.type&&e.classList.remove(...t))};e.classList.add(...t),e.addEventListener("animationend",n),e.addEventListener("animationcancel",n)},[]),(0,a.useEffect)(()=>{let e=A.current,t=()=>{e.removeEventListener("animationend",t),s?d(e,u,r):u()};g||(c?t():(h.current=1,e.className+=` ${p}`,e.addEventListener("animationend",t)))},[g]),a.createElement(a.Fragment,null,i)}}function A(e,t){return null!=e?{content:e.content,containerId:e.props.containerId,id:e.props.toastId,theme:e.props.theme,type:e.props.type,data:e.props.data||{},isLoading:e.props.isLoading,icon:e.props.icon,status:t}:{}}let g=new Map,f=[],m=new Set,p=e=>m.forEach(t=>t(e)),h=()=>g.size>0;function v(e,t){var n;if(t)return!(null==(n=g.get(t))||!n.isToastActive(e));let a=!1;return g.forEach(t=>{t.isToastActive(e)&&(a=!0)}),a}function y(e,t){c(e)&&(h()||f.push({content:e,options:t}),g.forEach(n=>{n.buildToast(e,t)}))}function b(e,t){g.forEach(n=>{null!=t&&null!=t&&t.containerId?(null==t?void 0:t.containerId)===n.id&&n.toggle(e,null==t?void 0:t.id):n.toggle(e,null==t?void 0:t.id)})}function E(e){let{subscribe:t,getSnapshot:n,setProps:o}=(0,a.useRef)(function(e){let t=e.containerId||1;return{subscribe(n){let o=function(e,t,n){let o=1,d=0,u=[],g=[],f=[],m=t,p=new Map,h=new Set,v=()=>{f=Array.from(p.values()),h.forEach(e=>e())},y=e=>{g=null==e?[]:g.filter(t=>t!==e),v()},b=e=>{let{toastId:t,onOpen:o,updateId:s,children:r}=e.props,l=null==s;e.staleId&&p.delete(e.staleId),p.set(t,e),g=[...g,e.props.toastId].filter(t=>t!==e.staleId),v(),n(A(e,l?"added":"updated")),l&&i(o)&&o((0,a.isValidElement)(r)&&r.props)};return{id:e,props:m,observe:e=>(h.add(e),()=>h.delete(e)),toggle:(e,t)=>{p.forEach(n=>{null!=t&&t!==n.props.toastId||i(n.toggle)&&n.toggle(e)})},removeToast:y,toasts:p,clearQueue:()=>{d-=u.length,u=[]},buildToast:(t,g)=>{var f,h;if((t=>{let{containerId:n,toastId:a,updateId:o}=t,s=p.has(a)&&null==o;return(n?n!==e:1!==e)||s})(g))return;let{toastId:E,updateId:w,data:C,staleId:I,delay:T}=g,x=()=>{y(E)},N=null==w;N&&d++;let _={...m,style:m.toastStyle,key:o++,...Object.fromEntries(Object.entries(g).filter(e=>{let[t,n]=e;return null!=n})),toastId:E,updateId:w,data:C,closeToast:x,isIn:!1,className:l(g.className||m.toastClassName),bodyClassName:l(g.bodyClassName||m.bodyClassName),progressClassName:l(g.progressClassName||m.progressClassName),autoClose:!g.isLoading&&(f=g.autoClose,h=m.autoClose,!1===f||s(f)&&f>0?f:h),deleteToast(){let e=p.get(E),{onClose:t,children:o}=e.props;i(t)&&t((0,a.isValidElement)(o)&&o.props),n(A(e,"removed")),p.delete(E),--d<0&&(d=0),u.length>0?b(u.shift()):v()}};_.closeButton=m.closeButton,!1===g.closeButton||c(g.closeButton)?_.closeButton=g.closeButton:!0===g.closeButton&&(_.closeButton=!c(m.closeButton)||m.closeButton);let R=t;(0,a.isValidElement)(t)&&!r(t.type)?R=(0,a.cloneElement)(t,{closeToast:x,toastProps:_,data:C}):i(t)&&(R=t({closeToast:x,toastProps:_,data:C}));let B={content:R,props:_,staleId:I};m.limit&&m.limit>0&&d>m.limit&&N?u.push(B):s(T)?setTimeout(()=>{b(B)},T):b(B)},setProps(e){m=e},setToggle:(e,t)=>{p.get(e).toggle=t},isToastActive:e=>g.some(t=>t===e),getSnapshot:()=>m.newestOnTop?f.reverse():f}}(t,e,p);g.set(t,o);let d=o.observe(n);return f.forEach(e=>y(e.content,e.options)),f=[],()=>{d(),g.delete(t)}},setProps(e){var n;null==(n=g.get(t))||n.setProps(e)},getSnapshot(){var e;return null==(e=g.get(t))?void 0:e.getSnapshot()}}}(e)).current;o(e);let d=(0,a.useSyncExternalStore)(t,n,n);return{getToastToRender:function(e){if(!d)return[];let t=new Map;return d.forEach(e=>{let{position:n}=e.props;t.has(n)||t.set(n,[]),t.get(n).push(e)}),Array.from(t,t=>e(t[0],t[1]))},isToastActive:v,count:null==d?void 0:d.length}}function w(e){var t,n;let[o,s]=(0,a.useState)(!1),[r,i]=(0,a.useState)(!1),l=(0,a.useRef)(null),c=(0,a.useRef)({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:d,pauseOnHover:u,closeToast:A,onClick:f,closeOnClick:m}=e;function p(){s(!0)}function h(){s(!1)}function v(t){let n=l.current;c.canDrag&&n&&(c.didMove=!0,o&&h(),c.delta="x"===e.draggableDirection?t.clientX-c.start:t.clientY-c.start,c.start!==t.clientX&&(c.canCloseOnClick=!1),n.style.transform=`translate3d(${"x"===e.draggableDirection?`${c.delta}px, var(--y)`:`0, calc(${c.delta}px + var(--y))`},0)`,n.style.opacity=""+(1-Math.abs(c.delta/c.removalDistance)))}function y(){document.removeEventListener("pointermove",v),document.removeEventListener("pointerup",y);let t=l.current;if(c.canDrag&&c.didMove&&t){if(c.canDrag=!1,Math.abs(c.delta)>c.removalDistance)return i(!0),e.closeToast(),void e.collapseAll();t.style.transition="transform 0.2s, opacity 0.2s",t.style.removeProperty("transform"),t.style.removeProperty("opacity")}}null==(n=g.get((t={id:e.toastId,containerId:e.containerId,fn:s}).containerId||1))||n.setToggle(t.id,t.fn),(0,a.useEffect)(()=>{if(e.pauseOnFocusLoss)return document.hasFocus()||h(),window.addEventListener("focus",p),window.addEventListener("blur",h),()=>{window.removeEventListener("focus",p),window.removeEventListener("blur",h)}},[e.pauseOnFocusLoss]);let b={onPointerDown:function(t){if(!0===e.draggable||e.draggable===t.pointerType){c.didMove=!1,document.addEventListener("pointermove",v),document.addEventListener("pointerup",y);let n=l.current;c.canCloseOnClick=!0,c.canDrag=!0,n.style.transition="none","x"===e.draggableDirection?(c.start=t.clientX,c.removalDistance=n.offsetWidth*(e.draggablePercent/100)):(c.start=t.clientY,c.removalDistance=n.offsetHeight*(80===e.draggablePercent?1.5*e.draggablePercent:e.draggablePercent)/100)}},onPointerUp:function(t){let{top:n,bottom:a,left:o,right:s}=l.current.getBoundingClientRect();"touchend"!==t.nativeEvent.type&&e.pauseOnHover&&t.clientX>=o&&t.clientX<=s&&t.clientY>=n&&t.clientY<=a?h():p()}};return d&&u&&(b.onMouseEnter=h,e.stacked||(b.onMouseLeave=p)),m&&(b.onClick=e=>{f&&f(e),c.canCloseOnClick&&A()}),{playToast:p,pauseToast:h,isRunning:o,preventExitTransition:r,toastRef:l,eventHandlers:b}}function C(e){let{delay:t,isRunning:n,closeToast:s,type:r="default",hide:l,className:c,style:d,controlledProgress:u,progress:A,rtl:g,isIn:f,theme:m}=e,p=l||u&&0===A,h={...d,animationDuration:`${t}ms`,animationPlayState:n?"running":"paused"};u&&(h.transform=`scaleX(${A})`);let v=o("Toastify__progress-bar",u?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${m}`,`Toastify__progress-bar--${r}`,{"Toastify__progress-bar--rtl":g}),y=i(c)?c({rtl:g,type:r,defaultClassName:v}):o(v,c);return a.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":p},a.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${m} Toastify__progress-bar--${r}`}),a.createElement("div",{role:"progressbar","aria-hidden":p?"true":"false","aria-label":"notification timer",className:y,style:h,[u&&A>=1?"onTransitionEnd":"onAnimationEnd"]:u&&A<1?null:()=>{f&&s()}}))}let I=1,T=()=>""+I++;function x(e,t){return y(e,t),t.toastId}function N(e,t){return{...t,type:t&&t.type||e,toastId:t&&(r(t.toastId)||s(t.toastId))?t.toastId:T()}}function _(e){return(t,n)=>x(t,N(e,n))}function R(e,t){return x(e,N("default",t))}R.loading=(e,t)=>x(e,N("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...t})),R.promise=function(e,t,n){let a,{pending:o,error:s,success:l}=t;o&&(a=r(o)?R.loading(o,n):R.loading(o.render,{...n,...o}));let c={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},d=(e,t,o)=>{if(null==t)return void R.dismiss(a);let s={type:e,...c,...n,data:o},i=r(t)?{render:t}:t;return a?R.update(a,{...s,...i}):R(i.render,{...s,...i}),o},u=i(e)?e():e;return u.then(e=>d("success",l,e)).catch(e=>d("error",s,e)),u},R.success=_("success"),R.info=_("info"),R.error=_("error"),R.warning=_("warning"),R.warn=R.warning,R.dark=(e,t)=>x(e,N("default",{theme:"dark",...t})),R.dismiss=function(e){var t,n;h()?null==e||r(t=e)||s(t)?g.forEach(t=>{t.removeToast(e)}):e&&("containerId"in e||"id"in e)&&((null==(n=g.get(e.containerId))?void 0:n.removeToast(e.id))||g.forEach(t=>{t.removeToast(e.id)})):f=f.filter(t=>null!=e&&t.options.toastId!==e)},R.clearWaitingQueue=function(e){void 0===e&&(e={}),g.forEach(t=>{!t.props.limit||e.containerId&&t.id!==e.containerId||t.clearQueue()})},R.isActive=v,R.update=function(e,t){void 0===t&&(t={});let n=((e,t)=>{var n;let{containerId:a}=t;return null==(n=g.get(a||1))?void 0:n.toasts.get(e)})(e,t);if(n){let{props:a,content:o}=n,s={delay:100,...a,...t,toastId:t.toastId||e,updateId:T()};s.toastId!==e&&(s.staleId=e);let r=s.render||o;delete s.render,x(r,s)}},R.done=e=>{R.update(e,{progress:1})},R.onChange=function(e){return m.add(e),()=>{m.delete(e)}},R.play=e=>b(!0,e),R.pause=e=>b(!1,e);let B="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,k=e=>{let{theme:t,type:n,isLoading:o,...s}=e;return a.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:"colored"===t?"currentColor":`var(--toastify-icon-color-${n})`,...s})},L={info:function(e){return a.createElement(k,{...e},a.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))},warning:function(e){return a.createElement(k,{...e},a.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))},success:function(e){return a.createElement(k,{...e},a.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))},error:function(e){return a.createElement(k,{...e},a.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))},spinner:function(){return a.createElement("div",{className:"Toastify__spinner"})}},D=e=>{let{isRunning:t,preventExitTransition:n,toastRef:s,eventHandlers:r,playToast:l}=w(e),{closeButton:c,children:d,autoClose:u,onClick:A,type:g,hideProgressBar:f,closeToast:m,transition:p,position:h,className:v,style:y,bodyClassName:b,bodyStyle:E,progressClassName:I,progressStyle:T,updateId:x,role:N,progress:_,rtl:R,toastId:B,deleteToast:k,isIn:D,isLoading:U,closeOnClick:S,theme:P}=e,V=o("Toastify__toast",`Toastify__toast-theme--${P}`,`Toastify__toast--${g}`,{"Toastify__toast--rtl":R},{"Toastify__toast--close-on-click":S}),M=i(v)?v({rtl:R,position:h,type:g,defaultClassName:V}):o(V,v),O=function(e){let{theme:t,type:n,isLoading:o,icon:s}=e,r=null,l={theme:t,type:n,isLoading:o};return!1===s||(i(s)?r=s(l):(0,a.isValidElement)(s)?r=(0,a.cloneElement)(s,l):o?r=L.spinner():n in L&&(r=L[n](l))),r}(e),Q=!!_||!u,j={closeToast:m,type:g,theme:P},G=null;return!1===c||(G=i(c)?c(j):(0,a.isValidElement)(c)?(0,a.cloneElement)(c,j):function(e){let{closeToast:t,theme:n,ariaLabel:o="close"}=e;return a.createElement("button",{className:`Toastify__close-button Toastify__close-button--${n}`,type:"button",onClick:e=>{e.stopPropagation(),t(e)},"aria-label":o},a.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},a.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}(j)),a.createElement(p,{isIn:D,done:k,position:h,preventExitTransition:n,nodeRef:s,playToast:l},a.createElement("div",{id:B,onClick:A,"data-in":D,className:M,...r,style:y,ref:s},a.createElement("div",{...D&&{role:N},className:i(b)?b({type:g}):o("Toastify__toast-body",b),style:E},null!=O&&a.createElement("div",{className:o("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!U})},O),a.createElement("div",null,d)),G,a.createElement(C,{...x&&!Q?{key:`pb-${x}`}:{},rtl:R,theme:P,delay:u,isRunning:t,isIn:D,closeToast:m,hide:f,type:g,style:T,className:I,controlledProgress:Q,progress:_||0})))},U=function(e,t){return void 0===t&&(t=!1),{enter:`Toastify--animate Toastify__${e}-enter`,exit:`Toastify--animate Toastify__${e}-exit`,appendPosition:t}},S=u(U("bounce",!0)),P=u(U("slide",!0)),V=u(U("zoom")),M=u(U("flip")),O={position:"top-right",transition:S,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light"};function Q(e){let t={...O,...e},n=e.stacked,[s,r]=(0,a.useState)(!0),c=(0,a.useRef)(null),{getToastToRender:d,isToastActive:u,count:A}=E(t),{className:g,style:f,rtl:m,containerId:p}=t;function h(){n&&(r(!0),R.play())}return B(()=>{if(n){var e;let n=c.current.querySelectorAll('[data-in="true"]'),a=null==(e=t.position)?void 0:e.includes("top"),o=0,r=0;Array.from(n).reverse().forEach((e,t)=>{e.classList.add("Toastify__toast--stacked"),t>0&&(e.dataset.collapsed=`${s}`),e.dataset.pos||(e.dataset.pos=a?"top":"bot");let n=o*(s?.2:1)+(s?0:12*t);e.style.setProperty("--y",`${a?n:-1*n}px`),e.style.setProperty("--g","12"),e.style.setProperty("--s",""+(1-(s?r:0))),o+=e.offsetHeight,r+=.025})}},[s,A,n]),a.createElement("div",{ref:c,className:"Toastify",id:p,onMouseEnter:()=>{n&&(r(!1),R.pause())},onMouseLeave:h},d((e,t)=>{let s=t.length?{...f}:{...f,pointerEvents:"none"};return a.createElement("div",{className:function(e){let t=o("Toastify__toast-container",`Toastify__toast-container--${e}`,{"Toastify__toast-container--rtl":m});return i(g)?g({position:e,rtl:m,defaultClassName:t}):o(t,l(g))}(e),style:s,key:`container-${e}`},t.map(e=>{let{content:t,props:o}=e;return a.createElement(D,{...o,stacked:n,collapseAll:h,isIn:u(o.toastId,o.containerId),style:o.style,key:`toast-${o.key}`},t)}))}))}}},function(e){e.O(0,[580,691,396,971,938,744],function(){return e(e.s=9192)}),_N_E=e.O()}]);
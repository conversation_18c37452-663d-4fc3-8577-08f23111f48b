"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./Components/Header.jsx":
/*!*******************************!*\
  !*** ./Components/Header.jsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _Assets_assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Assets/assets */ \"(app-pages-browser)/./Assets/assets.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _PaperRocketAnimation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PaperRocketAnimation */ \"(app-pages-browser)/./Components/PaperRocketAnimation.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst Header = (param)=>{\n    let { setSearchTerm } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [isLoggedIn, setIsLoggedIn] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [showLoginModal, setShowLoginModal] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isRegistering, setIsRegistering] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showAccountDropdown, setShowAccountDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [loginData, setLoginData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({\n        email: \"\",\n        password: \"\"\n    });\n    const [registerData, setRegisterData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        role: \"user\"\n    });\n    const [userProfilePicture, setUserProfilePicture] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"/default_profile.png\");\n    const [userName, setUserName] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [showLogoutConfirm, setShowLogoutConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showRegisterPassword, setShowRegisterPassword] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showRegisterConfirmPassword, setShowRegisterConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [rememberMe, setRememberMe] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isSearchMode, setIsSearchMode] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [localSearchTerm, setLocalSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [showTooltip, setShowTooltip] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    // Add this function to toggle password visibility\n    const togglePasswordVisibility = ()=>{\n        setShowPassword(!showPassword);\n    };\n    const toggleRegisterPasswordVisibility = ()=>{\n        setShowRegisterPassword(!showRegisterPassword);\n    };\n    const toggleRegisterConfirmPasswordVisibility = ()=>{\n        setShowRegisterConfirmPassword(!showRegisterConfirmPassword);\n    };\n    // Check if user is logged in on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        const authToken = localStorage.getItem(\"authToken\");\n        const storedUserRole = localStorage.getItem(\"userRole\");\n        const storedUserId = localStorage.getItem(\"userId\");\n        const storedProfilePicture = localStorage.getItem(\"userProfilePicture\");\n        const storedUserName = localStorage.getItem(\"userName\");\n        // Check for remembered credentials\n        const rememberedEmail = localStorage.getItem(\"rememberedEmail\");\n        const rememberedPassword = localStorage.getItem(\"rememberedPassword\");\n        const wasRemembered = localStorage.getItem(\"rememberMe\") === \"true\";\n        if (rememberedEmail && rememberedPassword && wasRemembered) {\n            setLoginData({\n                email: rememberedEmail,\n                password: rememberedPassword\n            });\n            setRememberMe(true);\n        }\n        if (authToken) {\n            setIsLoggedIn(true);\n            setUserRole(storedUserRole || \"user\");\n            if (storedProfilePicture) {\n                setUserProfilePicture(storedProfilePicture);\n            }\n            if (storedUserName) {\n                setUserName(storedUserName);\n            }\n        }\n    }, []);\n    const onSubmitHandler = async (e)=>{\n        // Existing email subscription code\n        e.preventDefault();\n        const formData = new FormData();\n        formData.append(\"email\", email);\n        const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].post(\"/api/email\", formData);\n        if (response.data.success) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(response.data.msg);\n            setEmail(\"\");\n        } else {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Error\");\n        }\n    };\n    const handleLoginChange = (e)=>{\n        setLoginData({\n            ...loginData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleRegisterChange = (e)=>{\n        setRegisterData({\n            ...registerData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleLoginSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].post(\"/api/auth\", {\n                email: loginData.email,\n                password: loginData.password\n            });\n            if (response.data.success) {\n                // Store auth data in localStorage\n                localStorage.setItem(\"authToken\", response.data.token || \"dummy-token\");\n                localStorage.setItem(\"userRole\", response.data.user.role);\n                localStorage.setItem(\"userId\", response.data.user.id);\n                localStorage.setItem(\"userProfilePicture\", response.data.user.profilePicture);\n                localStorage.setItem(\"userName\", response.data.user.name || \"\");\n                // Handle remember me functionality\n                if (rememberMe) {\n                    localStorage.setItem(\"rememberedEmail\", loginData.email);\n                    localStorage.setItem(\"rememberedPassword\", loginData.password);\n                    localStorage.setItem(\"rememberMe\", \"true\");\n                } else {\n                    localStorage.removeItem(\"rememberedEmail\");\n                    localStorage.removeItem(\"rememberedPassword\");\n                    localStorage.removeItem(\"rememberMe\");\n                }\n                // Update state\n                setIsLoggedIn(true);\n                setUserRole(response.data.user.role);\n                setUserProfilePicture(response.data.user.profilePicture);\n                setUserName(response.data.user.name || \"\");\n                setShowLoginModal(false);\n                // Check if user is admin\n                if (response.data.user.role === \"admin\") {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Login successful\");\n                    window.location.href = \"/admin\";\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Login successful\");\n                    // Redirect regular users to homepage or user dashboard\n                    window.location.href = \"/\";\n                }\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Invalid credentials\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Login error:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Login failed\");\n        }\n    };\n    const handleRegisterSubmit = async (e)=>{\n        e.preventDefault();\n        // Validate passwords match\n        if (registerData.password !== registerData.confirmPassword) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Passwords do not match\");\n            return;\n        }\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].post(\"/api/register\", {\n                email: registerData.email,\n                password: registerData.password,\n                role: registerData.role // Always \"user\" for public registration\n            });\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Registration successful! Please login.\");\n                // Switch back to login form\n                setIsRegistering(false);\n                // Pre-fill email in login form\n                setLoginData({\n                    ...loginData,\n                    email: registerData.email\n                });\n                // Reset register form\n                setRegisterData({\n                    email: \"\",\n                    password: \"\",\n                    confirmPassword: \"\",\n                    role: \"user\"\n                });\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(response.data.message || \"Registration failed\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Registration error:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Registration failed\");\n        }\n    };\n    const toggleForm = ()=>{\n        setIsRegistering(!isRegistering);\n    };\n    const handleLogoutClick = ()=>{\n        setShowLogoutConfirm(true);\n        setShowAccountDropdown(false);\n    };\n    const handleLogoutConfirm = ()=>{\n        // Clear auth data from localStorage\n        localStorage.removeItem(\"authToken\");\n        localStorage.removeItem(\"userRole\");\n        localStorage.removeItem(\"userId\");\n        localStorage.removeItem(\"userProfilePicture\");\n        localStorage.removeItem(\"userName\");\n        // Update state\n        setIsLoggedIn(false);\n        setUserRole(\"\");\n        setShowLogoutConfirm(false);\n        // Show toast and wait briefly before redirecting\n        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Logged out successfully\");\n        // Add a small delay before any navigation\n        setTimeout(()=>{\n            window.location.href = \"/\";\n        }, 300);\n    };\n    const handleLogoutCancel = ()=>{\n        setShowLogoutConfirm(false);\n    };\n    const toggleAccountDropdown = ()=>{\n        setShowAccountDropdown(!showAccountDropdown);\n    };\n    // Search handler (for now, just alert or log, you can wire to blog list)\n    const onSearchHandler = (e)=>{\n        e.preventDefault();\n        if (!localSearchTerm.trim()) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Please enter a search term\");\n            return;\n        }\n        setSearchTerm(localSearchTerm);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-5 px-5 md:px-12 lg:px-28 relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PaperRocketAnimation__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.logo,\n                        width: 180,\n                        alt: \"\",\n                        className: \"w-[130px] sm:w-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/profile\"),\n                                className: \"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-solid border-black shadow-[-7px_7px_0px_#000000]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        src: userProfilePicture,\n                                        width: 24,\n                                        height: 24,\n                                        alt: \"Account\",\n                                        className: \"w-6 h-6 rounded-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: userName || \"Account\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowLoginModal(true),\n                            className: \"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-solid border-black shadow-[-7px_7px_0px_#000000]\",\n                            children: [\n                                \"Get started \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.arrow,\n                                    alt: \"\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 27\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, undefined),\n            showLoginModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-8 rounded-md shadow-lg w-96\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: isRegistering ? \"Register\" : \"Login\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, undefined),\n                        isRegistering ? // Registration Form\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleRegisterSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            name: \"email\",\n                                            value: registerData.email,\n                                            onChange: handleRegisterChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: showRegisterPassword ? \"text\" : \"password\",\n                                                    name: \"password\",\n                                                    value: registerData.password,\n                                                    onChange: handleRegisterChange,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md pr-10\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: toggleRegisterPasswordVisibility,\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600\",\n                                                    children: showRegisterPassword ? // Eye-slash icon (hidden)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 25\n                                                    }, undefined) : // Eye icon (visible)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: showRegisterConfirmPassword ? \"text\" : \"password\",\n                                                    name: \"confirmPassword\",\n                                                    value: registerData.confirmPassword,\n                                                    onChange: handleRegisterChange,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md pr-10\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: toggleRegisterConfirmPasswordVisibility,\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600\",\n                                                    children: showRegisterConfirmPassword ? // Eye-slash icon (hidden)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 25\n                                                    }, undefined) : // Eye icon (visible)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"bg-black text-white px-4 py-2 rounded-md\",\n                                            children: \"Register\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowLoginModal(false),\n                                            className: \"text-gray-600 px-4 py-2\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Already have an account?\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: toggleForm,\n                                                className: \"text-blue-600 hover:underline\",\n                                                children: \"Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 293,\n                            columnNumber: 15\n                        }, undefined) : // Login Form\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleLoginSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            name: \"email\",\n                                            value: loginData.email,\n                                            onChange: handleLoginChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    name: \"password\",\n                                                    value: loginData.password,\n                                                    onChange: handleLoginChange,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md pr-10\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: togglePasswordVisibility,\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600\",\n                                                    children: showPassword ? // Eye-slash icon (hidden)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 431,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 25\n                                                    }, undefined) : // Eye icon (visible)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: rememberMe,\n                                                onChange: (e)=>setRememberMe(e.target.checked),\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-700\",\n                                                children: \"Remember me\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"bg-black text-white px-4 py-2 rounded-md\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowLoginModal(false),\n                                            className: \"text-gray-600 px-4 py-2\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Don't have an account?\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: toggleForm,\n                                                className: \"text-blue-600 hover:underline\",\n                                                children: \"Register\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 399,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 287,\n                columnNumber: 9\n            }, undefined),\n            showLogoutConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-6 rounded-md shadow-lg max-w-sm w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Confirm Logout\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 491,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-6\",\n                            children: \"Are you sure you want to log out? You will need to log in again to access your account.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 492,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogoutCancel,\n                                    className: \"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogoutConfirm,\n                                    className: \"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800\",\n                                    children: \"Logout\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 493,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                    lineNumber: 490,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 489,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center my-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl sm:text-5xl font-medium\",\n                        children: \"Mr.Blogger\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 512,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-10 max-w-[740px] m-auto text-xs sm:text-base\",\n                        children: \"Discover insightful articles, trending tech news, startup journeys, and lifestyle stories — all in one place. Welcome to your daily dose of inspiration and knowledge.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 513,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: isSearchMode ? onSearchHandler : onSubmitHandler,\n                        className: \"flex justify-between max-w-[500px] scale-75 sm:scale-100 mx-auto mt-10 border border-black shadow-[-7px_7px_0px_#000000]\",\n                        action: \"\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                onChange: isSearchMode ? (e)=>{\n                                    setLocalSearchTerm(e.target.value);\n                                    if (e.target.value === \"\") setSearchTerm(\"\");\n                                } : (e)=>setEmail(e.target.value),\n                                value: isSearchMode ? localSearchTerm : email,\n                                type: isSearchMode ? \"text\" : \"email\",\n                                placeholder: isSearchMode ? \"Search blogs...\" : \"Enter your email\",\n                                className: \"pl-4 outline-none flex-1\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 519,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"border-l border-black py-4 px-4 sm:px-8 active:bg-gray-600 active:text-white\",\n                                children: isSearchMode ? \"Search\" : \"Subscribe\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 530,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            setIsSearchMode((prev)=>{\n                                                if (prev) {\n                                                    setLocalSearchTerm(\"\");\n                                                    setSearchTerm(\"\"); // Clear parent search when toggling back\n                                                }\n                                                return !prev;\n                                            });\n                                        },\n                                        onMouseEnter: ()=>setShowTooltip(true),\n                                        onMouseLeave: ()=>setShowTooltip(false),\n                                        className: \"flex items-center justify-center px-4 border-l border-black hover:bg-gray-100 transition-colors\",\n                                        style: {\n                                            minWidth: \"56px\"\n                                        },\n                                        children: isSearchMode ? // Mail/Envelope Icon\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            width: \"22\",\n                                            height: \"22\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                    x: \"3\",\n                                                    y: \"5\",\n                                                    width: \"18\",\n                                                    height: \"14\",\n                                                    rx: \"2\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                    points: \"3,7 12,13 21,7\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 17\n                                        }, undefined) : // Search Icon\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            width: \"22\",\n                                            height: \"22\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"11\",\n                                                    cy: \"11\",\n                                                    r: \"8\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    fill: \"none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"21\",\n                                                    y1: \"21\",\n                                                    x2: \"16.65\",\n                                                    y2: \"16.65\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showTooltip && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-10 left-1/2 -translate-x-1/2 bg-black text-white text-xs rounded px-3 py-1 shadow-lg animate-fade-in z-10 whitespace-nowrap\",\n                                        children: isSearchMode ? \"Switch to Subscribe mode\" : \"Switch to Search mode\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 536,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 514,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 511,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n        lineNumber: 249,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Header, \"iexpWvhRv7F7lDRJbJ4iOk3P4RQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = Header;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Header);\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./Components/Header.jsx\n"));

/***/ })

});
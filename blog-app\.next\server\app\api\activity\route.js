"use strict";(()=>{var e={};e.id=2504,e.ids=[2504],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},35448:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>v,originalPathname:()=>w,patchFetch:()=>h,requestAsyncStorage:()=>g,routeModule:()=>m,serverHooks:()=>S,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>f});var a={};r.r(a),r.d(a,{GET:()=>c});var i=r(95419),n=r(69108),o=r(99678),s=r(78070),l=r(91887),d=r(42244),u=r(96488),p=r(96617);async function c(){try{await (0,l.n)();let e=await d.Z.find().sort({date:-1}).limit(5).select("title date"),t=await u.Z.find().sort({date:-1}).limit(5).select("name email date"),r=await p.Z.find().sort({date:-1}).limit(5).select("email date"),a=[...e.map(e=>({type:"New Blog",message:`New blog post: "${e.title}"`,timestamp:e.date})),...t.map(e=>({type:"New User",message:`New user registered: ${e.name||e.email}`,timestamp:e.date})),...r.map(e=>({type:"Subscription",message:`New email subscription: ${e.email}`,timestamp:e.date}))];return a.sort((e,t)=>new Date(t.timestamp)-new Date(e.timestamp)),a=a.slice(0,10),s.Z.json({success:!0,activities:a})}catch(e){return console.error("Error fetching activity:",e),s.Z.json({success:!1,message:"Failed to fetch recent activity"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/activity/route",pathname:"/api/activity",filename:"route",bundlePath:"app/api/activity/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\activity\\route.js",nextConfigOutput:"",userland:a}),{requestAsyncStorage:g,staticGenerationAsyncStorage:y,serverHooks:S,headerHooks:v,staticGenerationBailout:f}=m,w="/api/activity/route";function h(){return(0,o.patchFetch)({serverHooks:S,staticGenerationAsyncStorage:y})}},91887:(e,t,r)=>{r.d(t,{n:()=>n});var a=r(11185),i=r.n(a);let n=async()=>{try{if(i().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await i().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},42244:(e,t,r)=>{r.d(t,{Z:()=>o});var a=r(11185),i=r.n(a);let n=new(i()).Schema({title:{type:String,required:!0},description:{type:String,required:!0},category:{type:String,required:!0},author:{type:String,required:!0},image:{type:String,required:!0},authorImg:{type:String,required:!0},date:{type:Date,default:Date.now()}}),o=i().models.blog||i().model("blog",n)},96617:(e,t,r)=>{r.d(t,{Z:()=>o});var a=r(11185),i=r.n(a);let n=new(i()).Schema({email:{type:String,required:!0},date:{type:Date,default:Date.now()}}),o=i().models.email||i().model("email",n)},96488:(e,t,r)=>{r.d(t,{Z:()=>o});var a=r(11185),i=r.n(a);let n=new(i()).Schema({email:{type:String,required:!0,unique:!0},password:{type:String,required:!0},role:{type:String,default:"user",enum:["user","admin"]},profilePicture:{type:String,default:"/default_profile.png"},name:{type:String,default:""},date:{type:Date,default:Date.now()}}),o=i().models.user||i().model("user",n)},78070:(e,t,r)=>{Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return a.NextResponse}});let a=r(70457)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,2993],()=>r(35448));module.exports=a})();
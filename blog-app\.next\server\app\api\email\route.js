"use strict";(()=>{var e={};e.id=7433,e.ids=[7433],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},73533:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>y,originalPathname:()=>D,patchFetch:()=>S,requestAsyncStorage:()=>g,routeModule:()=>p,serverHooks:()=>h,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>v});var n={};a.r(n),a.d(n,{DELETE:()=>m,GET:()=>d,POST:()=>u});var r=a(95419),o=a(69108),i=a(99678),s=a(91887),l=a(96617),c=a(78070);async function u(e){let t=await e.formData(),a={email:`${t.get("email")}`};return await l.Z.create(a),c.Z.json({success:!0,msg:"Email Subscribed"})}async function d(e){let t=await l.Z.find({});return c.Z.json({emails:t})}async function m(e){let t=await e.nextUrl.searchParams.get("id");return await l.Z.findByIdAndDelete(t),c.Z.json({success:!0,msg:"Email Deleted"})}(async()=>{await (0,s.n)()})();let p=new r.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/email/route",pathname:"/api/email",filename:"route",bundlePath:"app/api/email/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\email\\route.js",nextConfigOutput:"",userland:n}),{requestAsyncStorage:g,staticGenerationAsyncStorage:f,serverHooks:h,headerHooks:y,staticGenerationBailout:v}=p,D="/api/email/route";function S(){return(0,i.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:f})}},91887:(e,t,a)=>{a.d(t,{n:()=>o});var n=a(11185),r=a.n(n);let o=async()=>{try{if(r().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await r().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},96617:(e,t,a)=>{a.d(t,{Z:()=>i});var n=a(11185),r=a.n(n);let o=new(r()).Schema({email:{type:String,required:!0},date:{type:Date,default:Date.now()}}),i=r().models.email||r().model("email",o)},78070:(e,t,a)=>{Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return n.NextResponse}});let n=a(70457)}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),n=t.X(0,[1638,2993],()=>a(73533));module.exports=n})();
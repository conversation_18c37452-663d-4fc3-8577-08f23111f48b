'use client'
import { assets } from '@/Assets/assets'
import Image from 'next/image'
import Link from 'next/link'
import React from 'react'
import Footer from '@/Components/Footer'

const AboutPage = () => {
  return (
    <>
      <div className='bg-gray-200 py-5 px-5 md:px-12 lg:px-28'>
        <div className='flex justify-between items-center'>
          <Link href='/'>
            <Image src={assets.logo} width={180} alt='Mr.Blogger' className='w-[130px] sm:w-auto' />
          </Link>
          <Link href='/'>
            <button className='flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]'>
              Back to Home
            </button>
          </Link>
        </div>
        
        <div className='text-center my-16'>
          <h1 className='text-3xl sm:text-5xl font-semibold'>About Us</h1>
          <p className='mt-4 text-gray-600'>Learn more about Mr.Blogger and our mission</p>
        </div>
      </div>

      <div className='container mx-auto px-4 py-12 max-w-4xl'>
        <div className='bg-white rounded-lg shadow-md p-8'>
          <div className='space-y-8'>
            <section>
              <h2 className='text-2xl font-semibold mb-4'>Our Story</h2>
              <p className='text-gray-600 mb-4'>
                Mr.Blogger was founded in 2023 with a simple mission: to create a platform where writers and readers can connect through meaningful content. What started as a small project has grown into a vibrant community of content creators and enthusiasts.
              </p>
              <p className='text-gray-600'>
                We believe in the power of words to inspire, educate, and entertain. Our platform is designed to make sharing ideas accessible to everyone, regardless of their background or experience level.
              </p>
            </section>
            
            <section>
              <h2 className='text-2xl font-semibold mb-4'>Our Mission</h2>
              <p className='text-gray-600'>
                At Mr.Blogger, we&apos;re committed to:
              </p>
              <ul className='list-disc pl-5 mt-2 space-y-2 text-gray-600'>
                <li>Providing a user-friendly platform for content creators</li>
                <li>Fostering a supportive community of writers and readers</li>
                <li>Promoting diverse voices and perspectives</li>
                <li>Delivering high-quality, engaging content across various topics</li>
              </ul>
            </section>
            
            <section>
              <h2 className='text-2xl font-semibold mb-4'>Our Team</h2>
              <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mt-4'>
                <div className='text-center'>
                  <div className='w-32 h-32 mx-auto bg-gray-300 rounded-full mb-4'></div>
                  <h3 className='font-medium'>John Doe</h3>
                  <p className='text-gray-600'>Founder & CEO</p>
                </div>
                <div className='text-center'>
                  <div className='w-32 h-32 mx-auto bg-gray-300 rounded-full mb-4'></div>
                  <h3 className='font-medium'>Jane Smith</h3>
                  <p className='text-gray-600'>Head of Content</p>
                </div>
                <div className='text-center'>
                  <div className='w-32 h-32 mx-auto bg-gray-300 rounded-full mb-4'></div>
                  <h3 className='font-medium'>Mike Johnson</h3>
                  <p className='text-gray-600'>Lead Developer</p>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
      
      <Footer />
    </>
  )
}

export default AboutPage
{"/_not-found": "app/_not-found.js", "/about/page": "app/about/page.js", "/api/activity/route": "app/api/activity/route.js", "/api/analytics/debug/route": "app/api/analytics/debug/route.js", "/api/auth/route": "app/api/auth/route.js", "/api/analytics/route": "app/api/analytics/route.js", "/api/authors/[id]/route": "app/api/authors/[id]/route.js", "/api/authors/route": "app/api/authors/route.js", "/api/blog/analytics/route": "app/api/blog/analytics/route.js", "/api/blog/likes/route": "app/api/blog/likes/route.js", "/api/blog/route": "app/api/blog/route.js", "/api/blog/trending/route": "app/api/blog/trending/route.js", "/api/categories/route": "app/api/categories/route.js", "/api/debug/route": "app/api/debug/route.js", "/api/email/route": "app/api/email/route.js", "/api/favorites/route": "app/api/favorites/route.js", "/api/feedback/route": "app/api/feedback/route.js", "/api/images/[id]/route": "app/api/images/[id]/route.js", "/api/images/route": "app/api/images/route.js", "/api/likes/route": "app/api/likes/route.js", "/api/password/route": "app/api/password/route.js", "/api/profile/route": "app/api/profile/route.js", "/api/register/route": "app/api/register/route.js", "/api/upload/image/route": "app/api/upload/image/route.js", "/api/users/route": "app/api/users/route.js", "/blogs/[id]/page": "app/blogs/[id]/page.js", "/contact/page": "app/contact/page.js", "/debug/page": "app/debug/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/favorites/page": "app/favorites/page.js", "/login-test/page": "app/login-test/page.js", "/page": "app/page.js", "/privacy-policy/page": "app/privacy-policy/page.js", "/profile/favorites/page": "app/profile/favorites/page.js", "/profile/page": "app/profile/page.js", "/admin/blogList/page": "app/admin/blogList/page.js", "/admin/addBlog/page": "app/admin/addBlog/page.js", "/admin/addUser/page": "app/admin/addUser/page.js", "/admin/editAuthor/[id]/page": "app/admin/editAuthor/[id]/page.js", "/admin/editBlog/[id]/page": "app/admin/editBlog/[id]/page.js", "/admin/feedback/page": "app/admin/feedback/page.js", "/admin/profile/page": "app/admin/profile/page.js", "/admin/page": "app/admin/page.js", "/admin/reactions/page": "app/admin/reactions/page.js", "/admin/settings/page": "app/admin/settings/page.js", "/admin/subscriptions/page": "app/admin/subscriptions/page.js", "/admin/traffic/page": "app/admin/traffic/page.js"}
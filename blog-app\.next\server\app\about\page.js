(()=>{var e={};e.id=7301,e.ids=[7301],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},30724:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=t(50482),a=t(69108),i=t(62563),n=t.n(i),o=t(68300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let c=["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,7353)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\about\\page.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,39459)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\about\\page.jsx"],x="/about/page",p={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},4982:(e,s,t)=>{Promise.resolve().then(t.bind(t,98546))},28674:(e,s,t)=>{"use strict";t.d(s,{Z:()=>c});var r=t(95344),a=t(6880),i=t(41223),n=t.n(i),o=t(20783),l=t.n(o);t(3729);let c=()=>(0,r.jsxs)("div",{className:"flex justify-around flex-col gap-2 sm:gap-0 sm:flex-row bg-black py-5 items-center",children:[r.jsx(n(),{src:a.L.logo_light,alt:"Mr.Blogger",width:120}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"flex justify-center gap-4 mb-1",children:[r.jsx(l(),{href:"/about",className:"text-sm text-white hover:underline",children:"About Us"}),r.jsx(l(),{href:"/contact",className:"text-sm text-white hover:underline",children:"Contact Us"})]}),r.jsx("p",{className:"text-sm text-white",children:"All right reserved. Copyright @Mr.Blogger"})]}),(0,r.jsxs)("div",{className:"flex",children:[r.jsx(n(),{src:a.L.facebook_icon,alt:"",width:40}),r.jsx(n(),{src:a.L.twitter_icon,alt:"",width:40}),r.jsx(n(),{src:a.L.googleplus_icon,alt:"",width:40})]})]})},98546:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var r=t(95344),a=t(6880),i=t(41223),n=t.n(i),o=t(20783),l=t.n(o);t(3729);var c=t(28674);let d=()=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"bg-gray-200 py-5 px-5 md:px-12 lg:px-28",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx(l(),{href:"/",children:r.jsx(n(),{src:a.L.logo,width:180,alt:"Mr.Blogger",className:"w-[130px] sm:w-auto"})}),r.jsx(l(),{href:"/",children:r.jsx("button",{className:"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]",children:"Back to Home"})})]}),(0,r.jsxs)("div",{className:"text-center my-16",children:[r.jsx("h1",{className:"text-3xl sm:text-5xl font-semibold",children:"About Us"}),r.jsx("p",{className:"mt-4 text-gray-600",children:"Learn more about Mr.Blogger and our mission"})]})]}),r.jsx("div",{className:"container mx-auto px-4 py-12 max-w-4xl",children:r.jsx("div",{className:"bg-white rounded-lg shadow-md p-8",children:(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("section",{children:[r.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"Our Story"}),r.jsx("p",{className:"text-gray-600 mb-4",children:"Mr.Blogger was founded in 2023 with a simple mission: to create a platform where writers and readers can connect through meaningful content. What started as a small project has grown into a vibrant community of content creators and enthusiasts."}),r.jsx("p",{className:"text-gray-600",children:"We believe in the power of words to inspire, educate, and entertain. Our platform is designed to make sharing ideas accessible to everyone, regardless of their background or experience level."})]}),(0,r.jsxs)("section",{children:[r.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"Our Mission"}),r.jsx("p",{className:"text-gray-600",children:"At Mr.Blogger, we're committed to:"}),(0,r.jsxs)("ul",{className:"list-disc pl-5 mt-2 space-y-2 text-gray-600",children:[r.jsx("li",{children:"Providing a user-friendly platform for content creators"}),r.jsx("li",{children:"Fostering a supportive community of writers and readers"}),r.jsx("li",{children:"Promoting diverse voices and perspectives"}),r.jsx("li",{children:"Delivering high-quality, engaging content across various topics"})]})]}),(0,r.jsxs)("section",{children:[r.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"Our Team"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mt-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"w-32 h-32 mx-auto bg-gray-300 rounded-full mb-4"}),r.jsx("h3",{className:"font-medium",children:"John Doe"}),r.jsx("p",{className:"text-gray-600",children:"Founder & CEO"})]}),(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"w-32 h-32 mx-auto bg-gray-300 rounded-full mb-4"}),r.jsx("h3",{className:"font-medium",children:"Jane Smith"}),r.jsx("p",{className:"text-gray-600",children:"Head of Content"})]}),(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"w-32 h-32 mx-auto bg-gray-300 rounded-full mb-4"}),r.jsx("h3",{className:"font-medium",children:"Mike Johnson"}),r.jsx("p",{className:"text-gray-600",children:"Lead Developer"})]})]})]})]})})}),r.jsx(c.Z,{})]})},7353:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\about\page.jsx`),{__esModule:a,$$typeof:i}=r,n=r.default}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[1638,3998,337,8468,5757],()=>t(30724));module.exports=r})();
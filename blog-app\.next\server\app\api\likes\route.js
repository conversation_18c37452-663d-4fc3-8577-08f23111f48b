"use strict";(()=>{var e={};e.id=623,e.ids=[623],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},12781:e=>{e.exports=require("stream")},73837:e=>{e.exports=require("util")},39742:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>v,originalPathname:()=>S,patchFetch:()=>I,requestAsyncStorage:()=>y,routeModule:()=>h,serverHooks:()=>f,staticGenerationAsyncStorage:()=>k,staticGenerationBailout:()=>q});var s={};t.r(s),t.d(s,{DELETE:()=>m,GET:()=>g,POST:()=>p});var n=t(95419),o=t(69108),i=t(99678),a=t(78070),u=t(91887),c=t(11497);t(42244);var l=t(27476);function d(e){let r=e.headers.get("Authorization");if(!r||!r.startsWith("Bearer "))return null;let t=r.split(" ")[1];return(0,c.W)(t)}async function g(e){try{await (0,u.n)();let r=d(e);if(!r)return a.Z.json({success:!1,message:"Authentication required"},{status:401});let{searchParams:t}=new URL(e.url),s=t.get("blogId"),n=await l.Z.findOne({userId:r.userId,blogId:s});return a.Z.json({success:!0,isLiked:!!n})}catch(e){return console.error("Error checking like status:",e),a.Z.json({success:!1,message:"Failed to check like status"},{status:500})}}async function p(e){try{await (0,u.n)();let r=d(e);if(!r)return a.Z.json({success:!1,message:"Authentication required"},{status:401});let{blogId:t}=await e.json();if(await l.Z.findOne({userId:r.userId,blogId:t}))return a.Z.json({success:!0,message:"Blog already liked"});return await l.Z.create({userId:r.userId,blogId:t}),a.Z.json({success:!0,message:"Blog liked"})}catch(e){return console.error("Error liking blog:",e),a.Z.json({success:!1,message:"Failed to like blog"},{status:500})}}async function m(e){try{await (0,u.n)();let r=d(e);if(!r)return a.Z.json({success:!1,message:"Authentication required"},{status:401});let{searchParams:t}=new URL(e.url),s=t.get("blogId");return await l.Z.findOneAndDelete({userId:r.userId,blogId:s}),a.Z.json({success:!0,message:"Like removed"})}catch(e){return console.error("Error removing like:",e),a.Z.json({success:!1,message:"Failed to remove like"},{status:500})}}let h=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/likes/route",pathname:"/api/likes",filename:"route",bundlePath:"app/api/likes/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\likes\\route.js",nextConfigOutput:"",userland:s}),{requestAsyncStorage:y,staticGenerationAsyncStorage:k,serverHooks:f,headerHooks:v,staticGenerationBailout:q}=h,S="/api/likes/route";function I(){return(0,i.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:k})}},91887:(e,r,t)=>{t.d(r,{n:()=>o});var s=t(11185),n=t.n(s);let o=async()=>{try{if(n().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await n().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},42244:(e,r,t)=>{t.d(r,{Z:()=>i});var s=t(11185),n=t.n(s);let o=new(n()).Schema({title:{type:String,required:!0},description:{type:String,required:!0},category:{type:String,required:!0},author:{type:String,required:!0},image:{type:String,required:!0},authorImg:{type:String,required:!0},date:{type:Date,default:Date.now()}}),i=n().models.blog||n().model("blog",o)},11497:(e,r,t)=>{t.d(r,{V:()=>i,W:()=>a});var s=t(46082),n=t.n(s);let o=process.env.JWT_SECRET||"your-secret-key-here",i=e=>n().sign(e,o,{expiresIn:"7d"}),a=e=>{try{return n().verify(e,o)}catch(e){return console.error("Token verification error:",e.message),null}}},27476:(e,r,t)=>{t.d(r,{Z:()=>i});var s=t(11185),n=t.n(s);let o=new(n()).Schema({userId:{type:String,required:!0},blogId:{type:n().Schema.Types.ObjectId,ref:"Blog",required:!0},createdAt:{type:Date,default:Date.now}});o.index({userId:1,blogId:1},{unique:!0});let i=n().models.Like||n().model("Like",o)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,2993,185],()=>t(39742));module.exports=s})();
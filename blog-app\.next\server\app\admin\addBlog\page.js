(()=>{var e={};e.id=4714,e.ids=[4714],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},52573:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>g,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=a(50482),r=a(69108),l=a(62563),o=a.n(l),i=a(68300),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);a.d(t,n);let d=["",{children:["admin",{children:["addBlog",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,24578)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\addBlog\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,32143)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,39459)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\addBlog\\page.jsx"],m="/admin/addBlog/page",g={require:a,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/addBlog/page",pathname:"/admin/addBlog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},58158:(e,t,a)=>{Promise.resolve().then(a.bind(a,40325))},40325:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var s=a(95344),r=a(6880),l=a(53608),o=a(41223),i=a.n(o),n=a(3729),d=a(69697),c=a(20783),m=a.n(c);let g=({authorImg:e,title:t,author:a,date:l,deleteBlog:o,mongoId:n})=>{let d=new Date(l);return(0,s.jsxs)("tr",{className:"bg-white border-b hover:bg-gray-50",children:[s.jsx("th",{scope:"row",className:"hidden sm:table-cell px-6 py-4 font-medium text-gray-900",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[s.jsx(i(),{width:40,height:40,src:e||r.L.profile_icon,alt:a||"Author",className:"rounded-full object-cover w-10 h-10 border border-gray-200"}),s.jsx("p",{className:"truncate",children:a||"No author"})]})}),s.jsx("td",{className:"px-6 py-4 truncate",children:t||"No title"}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:d.toDateString()}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex gap-3",children:[s.jsx(m(),{href:`/blogs/${n}`,target:"_blank",className:"cursor-pointer text-green-600 hover:underline",children:"View"}),s.jsx(m(),{href:`/admin/editBlog/${n}`,className:"cursor-pointer text-blue-600 hover:underline",children:"Edit"}),s.jsx("button",{onClick:()=>o(n),className:"cursor-pointer text-red-600 hover:underline",children:"Delete"})]})})]})},u=()=>{let[e,t]=(0,n.useState)("add"),[a,o]=(0,n.useState)(!1),[c,m]=(0,n.useState)([]),[u,h]=(0,n.useState)([]),[p,x]=(0,n.useState)(!0),[b,j]=(0,n.useState)([]),[f,y]=(0,n.useState)({title:"",description:"",category:"",author:"",authorId:"",authorImg:""}),[v,w]=(0,n.useState)(!1),[N,k]=(0,n.useState)([]),[S,C]=(0,n.useState)(""),[_,B]=(0,n.useState)(!1),[I,q]=(0,n.useState)([]),[P,L]=(0,n.useState)(""),[D,M]=(0,n.useState)(!1),[E,F]=(0,n.useState)(null),[A,U]=(0,n.useState)(null);(0,n.useEffect)(()=>{$(),T(),"manage"===e&&Z()},[e]),(0,n.useEffect)(()=>{U("temp_"+Date.now()+"_"+Math.random().toString(36).substring(2,11))},[]);let $=async()=>{try{let e=await l.Z.get("/api/categories");e.data.success&&e.data.categories.length>0?(m(e.data.categories),y(t=>({...t,category:e.data.categories[0].name}))):(d.toast.error("No categories found. Please add categories in Settings."),m([]))}catch(e){console.error("Error fetching categories:",e),d.toast.error("Failed to load categories"),m([])}finally{x(!1)}},T=async()=>{try{let e=await l.Z.get("/api/authors");e.data.success&&e.data.authors.length>0?(h(e.data.authors),y(t=>({...t,author:e.data.authors[0].name,authorId:e.data.authors[0]._id,authorImg:e.data.authors[0].image||"/author_img.png"}))):(d.toast.error("No authors found. Please add authors in Settings."),h([]))}catch(e){console.error("Error fetching authors:",e),d.toast.error("Failed to load authors"),h([])}},Z=async()=>{try{x(!0);let e=await l.Z.get("/api/blog");j(e.data.blogs||[])}catch(e){console.error("Error fetching blogs:",e),d.toast.error("Failed to load blogs")}finally{x(!1)}},G=e=>{let{name:t,value:a}=e.target;if("authorId"===t){let e=u.find(e=>e._id===a);e&&y({...f,author:e.name,authorId:e._id,authorImg:e.image||"/author_img.png"})}else y({...f,[t]:a})},W=async e=>{if(e.preventDefault(),!a){d.toast.error("Please select a blog thumbnail image");return}if(!f.title.trim()){d.toast.error("Please enter a blog title");return}let s=new FormData;s.append("title",f.title),s.append("description",f.description),s.append("category",f.category),s.append("author",f.author),s.append("authorId",f.authorId),s.append("authorImg",f.authorImg),s.append("image",a),s.append("tempBlogId",A);try{x(!0);let e=await l.Z.post("/api/blog",s);e.data.success?(d.toast.success(e.data.msg||"Blog added successfully"),o(!1),y({title:"",description:"",category:c.length>0?c[0].name:"",author:u.length>0?u[0].name:"",authorId:u.length>0?u[0]._id:"",authorImg:u.length>0&&u[0].image||"/author_img.png"}),U("temp_"+Date.now()+"_"+Math.random().toString(36).substring(2,11)),q([]),t("manage")):d.toast.error(e.data.msg||"Error adding blog")}catch(e){console.error("Error submitting blog:",e),d.toast.error("Failed to add blog")}finally{x(!1)}},R=async e=>{if(window.confirm("Are you sure you want to delete this blog?"))try{x(!0);let t=await l.Z.delete("/api/blog",{params:{id:e}});d.toast.success(t.data.msg||"Blog deleted successfully"),Z()}catch(e){console.error("Error deleting blog:",e),d.toast.error("Failed to delete blog")}finally{x(!1)}},Y=async()=>{try{let e=await l.Z.get("/api/blog");k(e.data.blogs||[])}catch(e){console.error("Error fetching blogs:",e)}},z=(e,t)=>{let a=`[[${e}|${t}]]`,s=document.getElementById("blog-description"),r=s.selectionStart,l=f.description.substring(0,r),o=f.description.substring(r);y({...f,description:l+a+o}),w(!1),setTimeout(()=>{s.focus(),s.setSelectionRange(r+a.length,r+a.length)},100)},O=async()=>{if(A)try{let e=await l.Z.get("/api/images",{params:{blogId:A,limit:50}});e.data.success&&q(e.data.images)}catch(e){console.error("Error fetching images:",e),d.toast.error("Failed to fetch images")}},V=async()=>{if(!E){d.toast.error("Please select an image file");return}M(!0);try{let e=new FormData;e.append("image",E),e.append("blogId",A||"new");let t=await l.Z.post("/api/upload/image",e);t.data.success?(d.toast.success("Image uploaded successfully"),F(null),await O()):d.toast.error(t.data.message||"Failed to upload image")}catch(e){console.error("Error uploading image:",e),d.toast.error("Failed to upload image")}finally{M(!1)}},H=async(e,t)=>{if(window.confirm("Are you sure you want to delete this image? This action cannot be undone."))try{let t=await l.Z.delete(`/api/images/${e}`);t.data.success?(d.toast.success("Image deleted successfully"),await O()):d.toast.error(t.data.message||"Failed to delete image")}catch(e){console.error("Error deleting image:",e),d.toast.error("Failed to delete image")}},X=(e,t)=>{let a=`{{image:${e}|${t}}}`,s=document.getElementById("blog-description"),r=s.selectionStart,l=f.description.substring(0,r),o=f.description.substring(r);y({...f,description:l+a+o}),B(!1),setTimeout(()=>{s.focus(),s.setSelectionRange(r+a.length,r+a.length)},100)};return(0,s.jsxs)("div",{className:"pt-5 px-5 sm:pt-12 sm:pl-16",children:[s.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Blog Management"}),(0,s.jsxs)("div",{className:"flex border-b border-gray-300 mb-6",children:[s.jsx("button",{className:`py-3 px-6 font-medium rounded-t-lg ${"add"===e?"bg-black text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,onClick:()=>t("add"),children:"Add New Blog"}),s.jsx("button",{className:`py-3 px-6 font-medium rounded-t-lg ml-2 ${"manage"===e?"bg-black text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,onClick:()=>t("manage"),children:"Manage Blogs"})]}),"add"===e&&s.jsx("form",{onSubmit:W,className:"max-w-[800px]",children:(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[s.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Create New Blog Post"}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("p",{className:"text-sm font-medium mb-2",children:["Upload Thumbnail ",s.jsx("span",{className:"text-red-500",children:"*"})]}),s.jsx("label",{htmlFor:"image",className:"cursor-pointer block",children:s.jsx(i(),{className:"border border-gray-300 rounded-md",src:a?URL.createObjectURL(a):r.L.upload_area,width:200,height:120,alt:"",style:{objectFit:"cover",height:"120px"}})}),s.jsx("input",{onChange:e=>o(e.target.files[0]),type:"file",id:"image",hidden:!0,required:!0})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium mb-2",children:["Blog Title ",s.jsx("span",{className:"text-red-500",children:"*"})]}),s.jsx("input",{name:"title",onChange:G,value:f.title,className:"w-full px-4 py-3 border rounded-md",type:"text",placeholder:"Type here",required:!0})]}),(0,s.jsxs)("div",{className:"mb-4 relative",children:[s.jsx("label",{className:"block text-sm font-medium mb-2",children:"Blog Description"}),s.jsx("div",{className:"flex items-start",children:s.jsx("textarea",{id:"blog-description",name:"description",onChange:G,value:f.description,className:"w-full px-4 py-3 border rounded-md",placeholder:"Write content here",rows:6,required:!0})}),(0,s.jsxs)("div",{className:"mt-2 flex items-center flex-wrap gap-4",children:[(0,s.jsxs)("button",{type:"button",onClick:()=>{Y(),w(!0)},className:"text-sm flex items-center text-blue-600 hover:text-blue-800",children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101"})}),"Mention another blog"]}),(0,s.jsxs)("button",{type:"button",onClick:()=>{O(),B(!0)},className:"text-sm flex items-center text-green-600 hover:text-green-800",children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),"Insert image"]}),s.jsx("div",{className:"text-xs text-gray-500",children:(0,s.jsxs)("span",{children:["Formats: [[blogId|blogTitle]] | ","{{image:url|filename}}"]})})]}),v&&s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[s.jsx("h3",{className:"text-lg font-semibold",children:"Select a blog to mention"}),s.jsx("button",{onClick:()=>w(!1),className:"text-gray-500 hover:text-gray-700",children:s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),s.jsx("input",{type:"text",placeholder:"Search blogs...",className:"w-full px-4 py-2 border rounded-md mb-4",value:S,onChange:e=>C(e.target.value)}),s.jsx("div",{className:"divide-y",children:N.filter(e=>e.title.toLowerCase().includes(S.toLowerCase())).map(e=>(0,s.jsxs)("div",{className:"py-3 px-2 hover:bg-gray-100 cursor-pointer flex items-center",onClick:()=>z(e._id,e.title),children:[s.jsx("div",{className:"w-12 h-12 relative mr-3",children:s.jsx(i(),{src:e.image,alt:e.title,fill:!0,className:"object-cover rounded"})}),(0,s.jsxs)("div",{children:[s.jsx("h4",{className:"font-medium",children:e.title}),s.jsx("p",{className:"text-sm text-gray-500",children:e.category})]})]},e._id))})]})}),_&&s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[s.jsx("h3",{className:"text-lg font-semibold",children:"Insert Image"}),s.jsx("button",{onClick:()=>B(!1),className:"text-gray-500 hover:text-gray-700",children:s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsxs)("div",{className:"mb-6 p-4 border rounded-lg bg-gray-50",children:[s.jsx("h4",{className:"font-medium mb-3",children:"Upload New Image"}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[s.jsx("input",{type:"file",accept:"image/*",onChange:e=>F(e.target.files[0]),className:"flex-1"}),s.jsx("button",{type:"button",onClick:V,disabled:!E||D,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50",children:D?"Uploading...":"Upload"})]})]}),s.jsx("input",{type:"text",placeholder:"Search images...",className:"w-full px-4 py-2 border rounded-md mb-4",value:P,onChange:e=>L(e.target.value)}),s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:I.filter(e=>e.filename.toLowerCase().includes(P.toLowerCase())).map(e=>(0,s.jsxs)("div",{className:"border rounded-lg p-2 hover:bg-gray-100 cursor-pointer relative group",children:[(0,s.jsxs)("div",{className:"aspect-square relative mb-2",onClick:()=>X(e.url,e.filename),children:[s.jsx(i(),{src:e.url,alt:e.filename,fill:!0,className:"object-cover rounded"}),s.jsx("button",{onClick:t=>{t.stopPropagation(),H(e._id,e.url)},className:"absolute top-1 right-1 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",title:"Delete image",children:s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsxs)("div",{onClick:()=>X(e.url,e.filename),children:[s.jsx("p",{className:"text-xs text-gray-600 truncate",children:e.filename}),s.jsx("p",{className:"text-xs text-gray-400",children:new Date(e.uploadDate).toLocaleDateString()})]})]},e._id))}),0===I.length&&s.jsx("div",{className:"text-center py-8 text-gray-500",children:"No images found. Upload your first image above."})]})})]}),(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-sm font-medium mb-2",children:"Blog Category"}),p?s.jsx("p",{children:"Loading categories..."}):c.length>0?s.jsx("select",{name:"category",onChange:G,value:f.category,className:"w-full px-4 py-3 border rounded-md text-gray-700",required:!0,children:c.map(e=>s.jsx("option",{value:e.name,children:e.name},e._id))}):s.jsx("p",{className:"text-red-500",children:"No categories available. Please add categories in Settings."})]}),(0,s.jsxs)("div",{className:"mb-6",children:[s.jsx("label",{className:"block text-sm font-medium mb-2",children:"Blog Author"}),p?s.jsx("p",{children:"Loading authors..."}):u.length>0?(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[s.jsx("select",{name:"authorId",onChange:G,value:f.authorId,className:"w-full px-4 py-3 border rounded-md text-gray-700",required:!0,children:u.map(e=>s.jsx("option",{value:e._id,children:e.name},e._id))}),f.authorId&&s.jsx("div",{className:"flex items-center gap-2",children:s.jsx("img",{src:f.authorImg,alt:f.author,className:"w-10 h-10 rounded-full object-cover border border-gray-200"})})]}):s.jsx("p",{className:"text-red-500",children:"No authors available. Please add authors in Settings."})]}),s.jsx("button",{type:"submit",className:"w-full py-3 bg-black text-white rounded-md hover:bg-gray-800 transition-colors",disabled:p||0===c.length||0===u.length,children:p?"Creating...":"Create Blog Post"})]})}),"manage"===e&&(0,s.jsxs)("div",{className:"max-w-[850px] bg-white p-6 rounded-lg shadow-md",children:[s.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Manage Blog Posts"}),p?(0,s.jsxs)("div",{className:"flex justify-center items-center py-8",children:[s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-black"}),s.jsx("span",{className:"ml-2",children:"Loading blogs..."})]}):b.length>0?s.jsx("div",{className:"relative overflow-x-auto border border-gray-300 rounded-lg",children:(0,s.jsxs)("table",{className:"w-full text-sm text-gray-500 table-fixed",children:[s.jsx("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[s.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Title"}),s.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Author"}),s.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),s.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),s.jsx("tbody",{children:b.map((e,t)=>s.jsx(g,{mongoId:e._id,title:e.title,author:e.author,authorImg:e.authorImg,date:e.date,deleteBlog:R},t))})]})}):(0,s.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[s.jsx("p",{children:"No blogs found."}),s.jsx("button",{onClick:()=>t("add"),className:"mt-4 text-blue-600 hover:underline",children:"Add your first blog"})]}),b.length>0&&s.jsx("div",{className:"mt-4 text-sm text-gray-500",children:(0,s.jsxs)("p",{children:["Showing ",b.length," blog posts"]})})]})]})}},24578:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>l,__esModule:()=>r,default:()=>o});let s=(0,a(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\admin\addBlog\page.jsx`),{__esModule:r,$$typeof:l}=s,o=s.default}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[1638,3998,337,8468,5757,7388],()=>a(52573));module.exports=s})();
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[894],{7157:function(e,t,a){Promise.resolve().then(a.bind(a,4756))},4257:function(e,t,a){"use strict";a.d(t,{L:function(){return s}});let s={facebook_icon:{src:"/_next/static/media/facebook_icon.cbcfc36d.png",height:58,width:58,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAbUlEQVR42m3HsRGCQBCG0X/sgArowytFG9BCqIESnLEHAhswNrqYmcvkAmB3P4Yh5WVPXCRP3ntvV2mf7B7sArtJGhsvkJfPAl7GRjUZxIs3hFOTcmsVvutvBZtyK+nfgRPA1OlQHvMwD+WpMxvnWUuxSavcBwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},googleplus_icon:{src:"/_next/static/media/googleplus_icon.15e2de32.png",height:59,width:59,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAe0lEQVR42mP4z8TA8Mv+z5x/M36bMzCAeAy/k/5Dwe9gBgaGZyL/P3z6f/3Xv59///97fIOT4b3d///H/s76v/3/g///f77WY7il8P/r+/+Hf73/9f//39dnhBkYGD41/f8PAv/+fyphgICXSV+3fNv4IoIBHfxnZGAAALhiS7/aN4AvAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},twitter_icon:{src:"/_next/static/media/twitter_icon.0d1dc581.png",height:59,width:59,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAeklEQVR42l3HvQnCUBiG0Uc7ax1AsLe10yFsrDODEziFhStYCO5gIThCGkHEgJqfC8mX5L4hJFVOd9AYbFOfyqOtoB1loJ5tgddM/0w/uxVOet4nxGspqtFCuWTfJeHcu0pnC02qoscUSA9eLa9kT+cTuKu7vHcMaQQNWbdKicv1GyQAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},profile_icon:{src:"/_next/static/media/profile_icon.fa2679c4.png",height:92,width:92,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABCklEQVR42iWPMU/CUACEn7/B+CPen3CS+BccTNyMm2kdpDoYiFEjVkgcQGOVONRookZUGlqf5Ukk9CkVU4ymxk2CU/e+5Shw2919NxwZqtf7o93ggzPmSKt6L5vNBvf9Nzoq+/1/KjwvUlUFiQWZINC0NFyXRcmAkjAMeTabwewUiRvlPMyt9BCMS6Ui6i7jxG+35fRMCgVlHt+VM7DDHFKTBHv6PhzHlqQbBHJ7N4eTDQW/1iWqO2vIryyi5jhgjwkghMc9IfBwexV/Xp/CXF5A7e4mfu908MRsTl5Fi9Y5j1ovz/ixL2CocyhurqJsHEWWbY+fZPQCNY8P+Jd1Liu6Jo31JZ7Eo3IAXaWfc0g8AF4AAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},logo:{src:"/_next/static/media/logo.c649e147.png",height:53,width:186,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAQAAADPnVVmAAAAKklEQVR42mNgaGdIY9BhcGQwZjBgMGHQYWCoZWhnMGSwY3BjyGSIYjAAAFAcBMpReGCWAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:2},arrow:{src:"/_next/static/media/arrow.35bdbbc1.png",height:16,width:18,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAQAAACfUMTVAAAATUlEQVR42mPQZNRgZGDQ4NC4qeHPwKDJxgADGvYazzRcGRgYNLk1eTR4NPkZGDS8NF5o+DBoHtI4p3lW44zmFY1tGp80fmGowDQD3RYA4awVkVQ4JrAAAAAASUVORK5CYII=",blurWidth:8,blurHeight:7},logo_light:{src:"/_next/static/media/logo_light.9ce1f99e.png",height:55,width:201,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAQAAADPnVVmAAAALElEQVR42mP41/Av9J/uP7d/5v8s/tn8M2X41/Sv9p/OP9t/rv9y/0X/MwIAZagUsO6duCoAAAAASUVORK5CYII=",blurWidth:8,blurHeight:2},blog_icon:{src:"/_next/static/media/blog_icon.6cf97bbc.png",height:32,width:32,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAVklEQVR42jXNMRGDQBAAwC2pGfozgA4KcjMMFIejN5IoSI8YLGAAKtbAQiil0xshNGky2J1GygccLrue1YKf22HQsUn8fTEpygwgFaGZpUVq4m03qxI8rIYRbx4WRDgAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},add_icon:{src:"/_next/static/media/add_icon.17426346.png",height:32,width:32,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAARElEQVR42mWNsQnAMAwEr3LwUMoGmiJKlf37HMZgg/+aP4EkRpKSZOYhaBI2kxboAqFRNOWTzqXxroGtILn3lePo8fYH8E4LJKezO8EAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},email_icon:{src:"/_next/static/media/email_icon.4caec7c6.png",height:32,width:32,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAQAAABuBnYAAAAAUUlEQVR42l3NsQmAMAAEwHONrCBqlVJrG1dQ1MIt4pZmHQMBC7nu4f9pAEADg9lSzDoIsmQskiwQ7S5tcTlEotPmlqw1CB63qagV9N/ogP/tC+8IDv7EJZnRAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},upload_area:{src:"/_next/static/media/upload_area.1ee5fe3d.png",height:140,width:240,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAMAAABPT11nAAAANlBMVEX6+vr6+vn5+fn5+fj5+Pj4+Pj39/f29vb19fXz8/Py8vLv7+/u7u7r6+rp6ejj5evf4OXc3+fsgmBfAAAALUlEQVR42g3GtwEAIAwDMFMc0wn/P0s0Cc1UqqzBiPsSDWJ3HxSU19kzR8QgfRm1AShVawqCAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:5}};Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now()},4756:function(e,t,a){"use strict";a.r(t);var s=a(7437),o=a(4257),n=a(2173),r=a(6691),i=a.n(r),l=a(4033),c=a(2265),d=a(7948);t.default=()=>{let e=(0,l.useRouter)(),t=(0,l.useParams)(),[a,r]=(0,c.useState)(!0),[u,g]=(0,c.useState)(null),[m,p]=(0,c.useState)(""),[h,A]=(0,c.useState)([]),[f,x]=(0,c.useState)([]),[b,y]=(0,c.useState)(0),[v,w]=(0,c.useState)({totalViews:0,uniqueVisitors:0}),[N,E]=(0,c.useState)({title:"",description:"",category:"",author:"",authorId:"",authorImg:"/author_img.png"}),[C,j]=(0,c.useState)(!1),[I,T]=(0,c.useState)([]),[k,_]=(0,c.useState)(""),[L,B]=(0,c.useState)(!1),[S,R]=(0,c.useState)([]),[D,U]=(0,c.useState)(""),[M,P]=(0,c.useState)(!1),[V,O]=(0,c.useState)(null),Q=async()=>{try{let e=await n.Z.get("/api/categories");e.data.success&&e.data.categories.length>0?A(e.data.categories):(console.error("No categories found"),d.toast.error("Failed to load categories"))}catch(e){console.error("Error fetching categories:",e),d.toast.error("Failed to load categories")}},F=async()=>{try{let e=await n.Z.get("/api/authors");e.data.success&&e.data.authors.length>0?x(e.data.authors):(console.error("No authors found"),d.toast.error("Failed to load authors"))}catch(e){console.error("Error fetching authors:",e),d.toast.error("Failed to load authors")}},G=async()=>{try{let e=await n.Z.get("/api/blog/likes?id=".concat(t.id));e.data.success&&y(e.data.count)}catch(e){console.error("Error fetching likes count:",e)}},H=async()=>{try{let e=await n.Z.get("/api/blog/analytics?id=".concat(t.id));e.data.success&&w(e.data.analytics)}catch(e){console.error("Error fetching blog analytics:",e)}},W=async()=>{try{let e=await n.Z.get("/api/blog");T(e.data.blogs||[])}catch(e){console.error("Error fetching blogs:",e)}},Y=(e,t)=>{let a="[[".concat(e,"|").concat(t,"]]"),s=document.getElementById("blog-description"),o=s.selectionStart,n=N.description.substring(0,o),r=N.description.substring(o);E({...N,description:n+a+r}),j(!1),setTimeout(()=>{s.focus(),s.setSelectionRange(o+a.length,o+a.length)},100)},z=async()=>{try{let e=await n.Z.get("/api/images",{params:{blogId:t.id,limit:50}});e.data.success&&R(e.data.images)}catch(e){console.error("Error fetching images:",e),d.toast.error("Failed to fetch images")}},Z=async()=>{if(!V){d.toast.error("Please select an image file");return}P(!0);try{let e=new FormData;e.append("image",V),e.append("blogId",t.id);let a=await n.Z.post("/api/upload/image",e);a.data.success?(d.toast.success("Image uploaded successfully"),O(null),await z()):d.toast.error(a.data.message||"Failed to upload image")}catch(e){console.error("Error uploading image:",e),d.toast.error("Failed to upload image")}finally{P(!1)}},q=async(e,t)=>{if(window.confirm("Are you sure you want to delete this image? This action cannot be undone."))try{let t=await n.Z.delete("/api/images/".concat(e));t.data.success?(d.toast.success("Image deleted successfully"),await z()):d.toast.error(t.data.message||"Failed to delete image")}catch(e){console.error("Error deleting image:",e),d.toast.error("Failed to delete image")}},K=(e,t)=>{let a="{{image:".concat(e,"|").concat(t,"}}"),s=document.getElementById("blog-description"),o=s.selectionStart,n=N.description.substring(0,o),r=N.description.substring(o);E({...N,description:n+a+r}),B(!1),setTimeout(()=>{s.focus(),s.setSelectionRange(o+a.length,o+a.length)},100)};(0,c.useEffect)(()=>{(async()=>{try{let e=(await n.Z.get("/api/blog",{params:{id:t.id}})).data;E({title:e.title||"",description:e.description||"",category:e.category||"Startup",author:e.author||"",authorId:e.authorId||"",authorImg:e.authorImg||"/author_img.png"}),p(e.image||""),await Q(),await F(),await G(),await H(),r(!1)}catch(t){console.error("Error fetching blog:",t),d.toast.error("Failed to load blog data"),e.push("/admin/blogList")}})()},[t.id,e]);let $=e=>{let t=e.target.name,a=e.target.value;if("authorId"===t){let e=f.find(e=>e._id===a);e&&E(t=>({...t,author:e.name,authorId:e._id,authorImg:e.image||"/author_img.png"}))}else E(e=>({...e,[t]:a}))},J=async a=>{a.preventDefault();try{let a=new FormData;a.append("id",t.id),a.append("title",N.title),a.append("description",N.description),a.append("category",N.category),a.append("author",N.author),a.append("authorId",N.authorId),a.append("authorImg",N.authorImg),u&&a.append("image",u);let s=await n.Z.put("/api/blog",a);s.data.success?(d.toast.success(s.data.msg||"Blog updated successfully"),e.push("/admin/blogList")):d.toast.error(s.data.msg||"Error updating blog")}catch(e){console.error("Error updating blog:",e),d.toast.error("Failed to update blog")}},X=async()=>{if(window.confirm("Are you sure you want to delete this blog? This action cannot be undone."))try{let a=await n.Z.delete("/api/blog",{params:{id:t.id}});a.data.success?(d.toast.success(a.data.msg||"Blog deleted successfully"),e.push("/admin/blogList")):d.toast.error(a.data.msg||"Error deleting blog")}catch(e){console.error("Error deleting blog:",e),d.toast.error("Failed to delete blog")}};return a?(0,s.jsx)("div",{className:"pt-5 px-5 sm:pt-12 sm:pl-16",children:(0,s.jsx)("p",{children:"Loading blog data..."})}):(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("form",{onSubmit:J,className:"pt-5 px-5 sm:pt-12 sm:pl-16",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Edit Blog"}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 bg-gray-100 px-3 py-2 rounded-md",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",className:"text-red-500",children:(0,s.jsx)("path",{d:"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"})}),(0,s.jsx)("span",{className:"font-medium",children:b}),(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"likes"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 bg-gray-100 px-3 py-2 rounded-md",children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"text-blue-500",children:[(0,s.jsx)("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}),(0,s.jsx)("circle",{cx:"12",cy:"12",r:"3"})]}),(0,s.jsx)("span",{className:"font-medium",children:v.totalViews}),(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"views"})]})]})]}),(0,s.jsx)("p",{className:"text-xl",children:"Current thumbnail"}),(0,s.jsx)("div",{className:"mt-4 mb-4",children:(0,s.jsx)(i(),{src:m,width:200,height:120,alt:"Current thumbnail",className:"border border-gray-300"})}),(0,s.jsx)("p",{className:"text-xl",children:"Upload new thumbnail (optional)"}),(0,s.jsx)("label",{htmlFor:"image",children:(0,s.jsx)(i(),{className:"mt-4 cursor-pointer",src:u?URL.createObjectURL(u):o.L.upload_area,width:140,height:70,alt:""})}),(0,s.jsx)("input",{onChange:e=>g(e.target.files[0]),type:"file",id:"image",hidden:!0}),(0,s.jsx)("p",{className:"text-xl mt-4",children:"Blog title"}),(0,s.jsx)("input",{name:"title",onChange:$,value:N.title,className:"w-full sm:w-[500px] mt-4 px-4 py-3 border",type:"text",placeholder:"Type here",required:!0}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("p",{className:"text-xl",children:"Blog Description"}),(0,s.jsx)("div",{className:"flex items-start mt-4",children:(0,s.jsx)("textarea",{id:"blog-description",name:"description",onChange:$,value:N.description,className:"w-full sm:w-[500px] px-4 py-3 border",placeholder:"Write content here",rows:6,required:!0})}),(0,s.jsxs)("div",{className:"mt-2 flex items-center flex-wrap gap-4",children:[(0,s.jsxs)("button",{type:"button",onClick:()=>{W(),j(!0)},className:"text-sm flex items-center text-blue-600 hover:text-blue-800",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101"})}),"Mention another blog"]}),(0,s.jsxs)("button",{type:"button",onClick:()=>{z(),B(!0)},className:"text-sm flex items-center text-green-600 hover:text-green-800",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),"Insert image"]}),(0,s.jsx)("div",{className:"text-xs text-gray-500",children:(0,s.jsxs)("span",{children:["Formats: [[blogId|blogTitle]] | ","{{image:url|filename}}"]})})]}),C&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Select a blog to mention"}),(0,s.jsx)("button",{onClick:()=>j(!1),className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsx)("input",{type:"text",placeholder:"Search blogs...",className:"w-full px-4 py-2 border rounded-md mb-4",value:k,onChange:e=>_(e.target.value)}),(0,s.jsx)("div",{className:"divide-y",children:I.filter(e=>e.title.toLowerCase().includes(k.toLowerCase())).map(e=>(0,s.jsxs)("div",{className:"py-3 px-2 hover:bg-gray-100 cursor-pointer flex items-center",onClick:()=>Y(e._id,e.title),children:[(0,s.jsx)("div",{className:"w-12 h-12 relative mr-3",children:(0,s.jsx)(i(),{src:e.image,alt:e.title,fill:!0,className:"object-cover rounded"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:e.category})]})]},e._id))})]})}),L&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Insert Image"}),(0,s.jsx)("button",{onClick:()=>B(!1),className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsxs)("div",{className:"mb-6 p-4 border rounded-lg bg-gray-50",children:[(0,s.jsx)("h4",{className:"font-medium mb-3",children:"Upload New Image"}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("input",{type:"file",accept:"image/*",onChange:e=>O(e.target.files[0]),className:"flex-1"}),(0,s.jsx)("button",{type:"button",onClick:Z,disabled:!V||M,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50",children:M?"Uploading...":"Upload"})]})]}),(0,s.jsx)("input",{type:"text",placeholder:"Search images...",className:"w-full px-4 py-2 border rounded-md mb-4",value:D,onChange:e=>U(e.target.value)}),(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:S.filter(e=>e.filename.toLowerCase().includes(D.toLowerCase())).map(e=>(0,s.jsxs)("div",{className:"border rounded-lg p-2 hover:bg-gray-100 cursor-pointer relative group",children:[(0,s.jsxs)("div",{className:"aspect-square relative mb-2",onClick:()=>K(e.url,e.filename),children:[(0,s.jsx)(i(),{src:e.url,alt:e.filename,fill:!0,className:"object-cover rounded"}),(0,s.jsx)("button",{onClick:t=>{t.stopPropagation(),q(e._id,e.url)},className:"absolute top-1 right-1 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",title:"Delete image",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsxs)("div",{onClick:()=>K(e.url,e.filename),children:[(0,s.jsx)("p",{className:"text-xs text-gray-600 truncate",children:e.filename}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:new Date(e.uploadDate).toLocaleDateString()})]})]},e._id))}),0===S.length&&(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No images found. Upload your first image above."})]})})]}),(0,s.jsx)("p",{className:"text-xl mt-4",children:"Blog category"}),(0,s.jsx)("select",{name:"category",onChange:$,value:N.category,className:"w-40 mt-4 px-4 py-3 border text-gray-500",children:h.map(e=>(0,s.jsx)("option",{value:e.name,children:e.name},e._id))}),(0,s.jsx)("p",{className:"text-xl mt-4",children:"Blog author"}),f.length>0?(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-4 mt-4",children:[(0,s.jsxs)("select",{name:"authorId",onChange:$,value:N.authorId,className:"w-full sm:w-40 px-4 py-3 border text-gray-500",children:[(0,s.jsx)("option",{value:"",children:"Select an author"}),f.map(e=>(0,s.jsx)("option",{value:e._id,children:e.name},e._id))]}),N.authorId&&(0,s.jsxs)("div",{className:"flex items-center gap-3 mt-2 sm:mt-0",children:[(0,s.jsx)("img",{src:N.authorImg,alt:N.author,className:"w-10 h-10 rounded-full object-cover border border-gray-200"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:N.author})]})]}):(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("p",{className:"text-red-500",children:"No authors available. Please add authors in Settings."}),(0,s.jsx)("input",{name:"author",onChange:$,value:N.author,className:"w-full sm:w-[500px] mt-2 px-4 py-3 border",type:"text",placeholder:"Author name",required:!0})]}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)("button",{type:"submit",className:"mt-8 w-40 h-12 bg-black text-white",children:"UPDATE"}),(0,s.jsx)("button",{type:"button",onClick:()=>e.push("/admin/blogList"),className:"mt-8 w-40 h-12 border border-black",children:"CANCEL"}),(0,s.jsx)("button",{type:"button",onClick:X,className:"mt-8 w-40 h-12 bg-red-600 text-white hover:bg-red-700",children:"DELETE"})]})]})})}},4033:function(e,t,a){e.exports=a(5313)},7948:function(e,t,a){"use strict";a.r(t),a.d(t,{Bounce:function(){return D},Flip:function(){return P},Icons:function(){return B},Slide:function(){return U},ToastContainer:function(){return O},Zoom:function(){return M},collapseToast:function(){return d},cssTransition:function(){return u},toast:function(){return k},useToast:function(){return w},useToastContainer:function(){return v}});var s=a(2265),o=function(){for(var e,t,a=0,s="",o=arguments.length;a<o;a++)(e=arguments[a])&&(t=function e(t){var a,s,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t)){var n=t.length;for(a=0;a<n;a++)t[a]&&(s=e(t[a]))&&(o&&(o+=" "),o+=s)}else for(s in t)t[s]&&(o&&(o+=" "),o+=s)}return o}(e))&&(s&&(s+=" "),s+=t);return s};let n=e=>"number"==typeof e&&!isNaN(e),r=e=>"string"==typeof e,i=e=>"function"==typeof e,l=e=>r(e)||i(e)?e:null,c=e=>(0,s.isValidElement)(e)||r(e)||i(e)||n(e);function d(e,t,a){void 0===a&&(a=300);let{scrollHeight:s,style:o}=e;requestAnimationFrame(()=>{o.minHeight="initial",o.height=s+"px",o.transition=`all ${a}ms`,requestAnimationFrame(()=>{o.height="0",o.padding="0",o.margin="0",setTimeout(t,a)})})}function u(e){let{enter:t,exit:a,appendPosition:o=!1,collapse:n=!0,collapseDuration:r=300}=e;return function(e){let{children:i,position:l,preventExitTransition:c,done:u,nodeRef:g,isIn:m,playToast:p}=e,h=o?`${t}--${l}`:t,A=o?`${a}--${l}`:a,f=(0,s.useRef)(0);return(0,s.useLayoutEffect)(()=>{let e=g.current,t=h.split(" "),a=s=>{s.target===g.current&&(p(),e.removeEventListener("animationend",a),e.removeEventListener("animationcancel",a),0===f.current&&"animationcancel"!==s.type&&e.classList.remove(...t))};e.classList.add(...t),e.addEventListener("animationend",a),e.addEventListener("animationcancel",a)},[]),(0,s.useEffect)(()=>{let e=g.current,t=()=>{e.removeEventListener("animationend",t),n?d(e,u,r):u()};m||(c?t():(f.current=1,e.className+=` ${A}`,e.addEventListener("animationend",t)))},[m]),s.createElement(s.Fragment,null,i)}}function g(e,t){return null!=e?{content:e.content,containerId:e.props.containerId,id:e.props.toastId,theme:e.props.theme,type:e.props.type,data:e.props.data||{},isLoading:e.props.isLoading,icon:e.props.icon,status:t}:{}}let m=new Map,p=[],h=new Set,A=e=>h.forEach(t=>t(e)),f=()=>m.size>0;function x(e,t){var a;if(t)return!(null==(a=m.get(t))||!a.isToastActive(e));let s=!1;return m.forEach(t=>{t.isToastActive(e)&&(s=!0)}),s}function b(e,t){c(e)&&(f()||p.push({content:e,options:t}),m.forEach(a=>{a.buildToast(e,t)}))}function y(e,t){m.forEach(a=>{null!=t&&null!=t&&t.containerId?(null==t?void 0:t.containerId)===a.id&&a.toggle(e,null==t?void 0:t.id):a.toggle(e,null==t?void 0:t.id)})}function v(e){let{subscribe:t,getSnapshot:a,setProps:o}=(0,s.useRef)(function(e){let t=e.containerId||1;return{subscribe(a){let o=function(e,t,a){let o=1,d=0,u=[],m=[],p=[],h=t,A=new Map,f=new Set,x=()=>{p=Array.from(A.values()),f.forEach(e=>e())},b=e=>{m=null==e?[]:m.filter(t=>t!==e),x()},y=e=>{let{toastId:t,onOpen:o,updateId:n,children:r}=e.props,l=null==n;e.staleId&&A.delete(e.staleId),A.set(t,e),m=[...m,e.props.toastId].filter(t=>t!==e.staleId),x(),a(g(e,l?"added":"updated")),l&&i(o)&&o((0,s.isValidElement)(r)&&r.props)};return{id:e,props:h,observe:e=>(f.add(e),()=>f.delete(e)),toggle:(e,t)=>{A.forEach(a=>{null!=t&&t!==a.props.toastId||i(a.toggle)&&a.toggle(e)})},removeToast:b,toasts:A,clearQueue:()=>{d-=u.length,u=[]},buildToast:(t,m)=>{var p,f;if((t=>{let{containerId:a,toastId:s,updateId:o}=t,n=A.has(s)&&null==o;return(a?a!==e:1!==e)||n})(m))return;let{toastId:v,updateId:w,data:N,staleId:E,delay:C}=m,j=()=>{b(v)},I=null==w;I&&d++;let T={...h,style:h.toastStyle,key:o++,...Object.fromEntries(Object.entries(m).filter(e=>{let[t,a]=e;return null!=a})),toastId:v,updateId:w,data:N,closeToast:j,isIn:!1,className:l(m.className||h.toastClassName),bodyClassName:l(m.bodyClassName||h.bodyClassName),progressClassName:l(m.progressClassName||h.progressClassName),autoClose:!m.isLoading&&(p=m.autoClose,f=h.autoClose,!1===p||n(p)&&p>0?p:f),deleteToast(){let e=A.get(v),{onClose:t,children:o}=e.props;i(t)&&t((0,s.isValidElement)(o)&&o.props),a(g(e,"removed")),A.delete(v),--d<0&&(d=0),u.length>0?y(u.shift()):x()}};T.closeButton=h.closeButton,!1===m.closeButton||c(m.closeButton)?T.closeButton=m.closeButton:!0===m.closeButton&&(T.closeButton=!c(h.closeButton)||h.closeButton);let k=t;(0,s.isValidElement)(t)&&!r(t.type)?k=(0,s.cloneElement)(t,{closeToast:j,toastProps:T,data:N}):i(t)&&(k=t({closeToast:j,toastProps:T,data:N}));let _={content:k,props:T,staleId:E};h.limit&&h.limit>0&&d>h.limit&&I?u.push(_):n(C)?setTimeout(()=>{y(_)},C):y(_)},setProps(e){h=e},setToggle:(e,t)=>{A.get(e).toggle=t},isToastActive:e=>m.some(t=>t===e),getSnapshot:()=>h.newestOnTop?p.reverse():p}}(t,e,A);m.set(t,o);let d=o.observe(a);return p.forEach(e=>b(e.content,e.options)),p=[],()=>{d(),m.delete(t)}},setProps(e){var a;null==(a=m.get(t))||a.setProps(e)},getSnapshot(){var e;return null==(e=m.get(t))?void 0:e.getSnapshot()}}}(e)).current;o(e);let d=(0,s.useSyncExternalStore)(t,a,a);return{getToastToRender:function(e){if(!d)return[];let t=new Map;return d.forEach(e=>{let{position:a}=e.props;t.has(a)||t.set(a,[]),t.get(a).push(e)}),Array.from(t,t=>e(t[0],t[1]))},isToastActive:x,count:null==d?void 0:d.length}}function w(e){var t,a;let[o,n]=(0,s.useState)(!1),[r,i]=(0,s.useState)(!1),l=(0,s.useRef)(null),c=(0,s.useRef)({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:d,pauseOnHover:u,closeToast:g,onClick:p,closeOnClick:h}=e;function A(){n(!0)}function f(){n(!1)}function x(t){let a=l.current;c.canDrag&&a&&(c.didMove=!0,o&&f(),c.delta="x"===e.draggableDirection?t.clientX-c.start:t.clientY-c.start,c.start!==t.clientX&&(c.canCloseOnClick=!1),a.style.transform=`translate3d(${"x"===e.draggableDirection?`${c.delta}px, var(--y)`:`0, calc(${c.delta}px + var(--y))`},0)`,a.style.opacity=""+(1-Math.abs(c.delta/c.removalDistance)))}function b(){document.removeEventListener("pointermove",x),document.removeEventListener("pointerup",b);let t=l.current;if(c.canDrag&&c.didMove&&t){if(c.canDrag=!1,Math.abs(c.delta)>c.removalDistance)return i(!0),e.closeToast(),void e.collapseAll();t.style.transition="transform 0.2s, opacity 0.2s",t.style.removeProperty("transform"),t.style.removeProperty("opacity")}}null==(a=m.get((t={id:e.toastId,containerId:e.containerId,fn:n}).containerId||1))||a.setToggle(t.id,t.fn),(0,s.useEffect)(()=>{if(e.pauseOnFocusLoss)return document.hasFocus()||f(),window.addEventListener("focus",A),window.addEventListener("blur",f),()=>{window.removeEventListener("focus",A),window.removeEventListener("blur",f)}},[e.pauseOnFocusLoss]);let y={onPointerDown:function(t){if(!0===e.draggable||e.draggable===t.pointerType){c.didMove=!1,document.addEventListener("pointermove",x),document.addEventListener("pointerup",b);let a=l.current;c.canCloseOnClick=!0,c.canDrag=!0,a.style.transition="none","x"===e.draggableDirection?(c.start=t.clientX,c.removalDistance=a.offsetWidth*(e.draggablePercent/100)):(c.start=t.clientY,c.removalDistance=a.offsetHeight*(80===e.draggablePercent?1.5*e.draggablePercent:e.draggablePercent)/100)}},onPointerUp:function(t){let{top:a,bottom:s,left:o,right:n}=l.current.getBoundingClientRect();"touchend"!==t.nativeEvent.type&&e.pauseOnHover&&t.clientX>=o&&t.clientX<=n&&t.clientY>=a&&t.clientY<=s?f():A()}};return d&&u&&(y.onMouseEnter=f,e.stacked||(y.onMouseLeave=A)),h&&(y.onClick=e=>{p&&p(e),c.canCloseOnClick&&g()}),{playToast:A,pauseToast:f,isRunning:o,preventExitTransition:r,toastRef:l,eventHandlers:y}}function N(e){let{delay:t,isRunning:a,closeToast:n,type:r="default",hide:l,className:c,style:d,controlledProgress:u,progress:g,rtl:m,isIn:p,theme:h}=e,A=l||u&&0===g,f={...d,animationDuration:`${t}ms`,animationPlayState:a?"running":"paused"};u&&(f.transform=`scaleX(${g})`);let x=o("Toastify__progress-bar",u?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${h}`,`Toastify__progress-bar--${r}`,{"Toastify__progress-bar--rtl":m}),b=i(c)?c({rtl:m,type:r,defaultClassName:x}):o(x,c);return s.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":A},s.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${h} Toastify__progress-bar--${r}`}),s.createElement("div",{role:"progressbar","aria-hidden":A?"true":"false","aria-label":"notification timer",className:b,style:f,[u&&g>=1?"onTransitionEnd":"onAnimationEnd"]:u&&g<1?null:()=>{p&&n()}}))}let E=1,C=()=>""+E++;function j(e,t){return b(e,t),t.toastId}function I(e,t){return{...t,type:t&&t.type||e,toastId:t&&(r(t.toastId)||n(t.toastId))?t.toastId:C()}}function T(e){return(t,a)=>j(t,I(e,a))}function k(e,t){return j(e,I("default",t))}k.loading=(e,t)=>j(e,I("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...t})),k.promise=function(e,t,a){let s,{pending:o,error:n,success:l}=t;o&&(s=r(o)?k.loading(o,a):k.loading(o.render,{...a,...o}));let c={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},d=(e,t,o)=>{if(null==t)return void k.dismiss(s);let n={type:e,...c,...a,data:o},i=r(t)?{render:t}:t;return s?k.update(s,{...n,...i}):k(i.render,{...n,...i}),o},u=i(e)?e():e;return u.then(e=>d("success",l,e)).catch(e=>d("error",n,e)),u},k.success=T("success"),k.info=T("info"),k.error=T("error"),k.warning=T("warning"),k.warn=k.warning,k.dark=(e,t)=>j(e,I("default",{theme:"dark",...t})),k.dismiss=function(e){var t,a;f()?null==e||r(t=e)||n(t)?m.forEach(t=>{t.removeToast(e)}):e&&("containerId"in e||"id"in e)&&((null==(a=m.get(e.containerId))?void 0:a.removeToast(e.id))||m.forEach(t=>{t.removeToast(e.id)})):p=p.filter(t=>null!=e&&t.options.toastId!==e)},k.clearWaitingQueue=function(e){void 0===e&&(e={}),m.forEach(t=>{!t.props.limit||e.containerId&&t.id!==e.containerId||t.clearQueue()})},k.isActive=x,k.update=function(e,t){void 0===t&&(t={});let a=((e,t)=>{var a;let{containerId:s}=t;return null==(a=m.get(s||1))?void 0:a.toasts.get(e)})(e,t);if(a){let{props:s,content:o}=a,n={delay:100,...s,...t,toastId:t.toastId||e,updateId:C()};n.toastId!==e&&(n.staleId=e);let r=n.render||o;delete n.render,j(r,n)}},k.done=e=>{k.update(e,{progress:1})},k.onChange=function(e){return h.add(e),()=>{h.delete(e)}},k.play=e=>y(!0,e),k.pause=e=>y(!1,e);let _="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,L=e=>{let{theme:t,type:a,isLoading:o,...n}=e;return s.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:"colored"===t?"currentColor":`var(--toastify-icon-color-${a})`,...n})},B={info:function(e){return s.createElement(L,{...e},s.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))},warning:function(e){return s.createElement(L,{...e},s.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))},success:function(e){return s.createElement(L,{...e},s.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))},error:function(e){return s.createElement(L,{...e},s.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))},spinner:function(){return s.createElement("div",{className:"Toastify__spinner"})}},S=e=>{let{isRunning:t,preventExitTransition:a,toastRef:n,eventHandlers:r,playToast:l}=w(e),{closeButton:c,children:d,autoClose:u,onClick:g,type:m,hideProgressBar:p,closeToast:h,transition:A,position:f,className:x,style:b,bodyClassName:y,bodyStyle:v,progressClassName:E,progressStyle:C,updateId:j,role:I,progress:T,rtl:k,toastId:_,deleteToast:L,isIn:S,isLoading:R,closeOnClick:D,theme:U}=e,M=o("Toastify__toast",`Toastify__toast-theme--${U}`,`Toastify__toast--${m}`,{"Toastify__toast--rtl":k},{"Toastify__toast--close-on-click":D}),P=i(x)?x({rtl:k,position:f,type:m,defaultClassName:M}):o(M,x),V=function(e){let{theme:t,type:a,isLoading:o,icon:n}=e,r=null,l={theme:t,type:a,isLoading:o};return!1===n||(i(n)?r=n(l):(0,s.isValidElement)(n)?r=(0,s.cloneElement)(n,l):o?r=B.spinner():a in B&&(r=B[a](l))),r}(e),O=!!T||!u,Q={closeToast:h,type:m,theme:U},F=null;return!1===c||(F=i(c)?c(Q):(0,s.isValidElement)(c)?(0,s.cloneElement)(c,Q):function(e){let{closeToast:t,theme:a,ariaLabel:o="close"}=e;return s.createElement("button",{className:`Toastify__close-button Toastify__close-button--${a}`,type:"button",onClick:e=>{e.stopPropagation(),t(e)},"aria-label":o},s.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},s.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}(Q)),s.createElement(A,{isIn:S,done:L,position:f,preventExitTransition:a,nodeRef:n,playToast:l},s.createElement("div",{id:_,onClick:g,"data-in":S,className:P,...r,style:b,ref:n},s.createElement("div",{...S&&{role:I},className:i(y)?y({type:m}):o("Toastify__toast-body",y),style:v},null!=V&&s.createElement("div",{className:o("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!R})},V),s.createElement("div",null,d)),F,s.createElement(N,{...j&&!O?{key:`pb-${j}`}:{},rtl:k,theme:U,delay:u,isRunning:t,isIn:S,closeToast:h,hide:p,type:m,style:C,className:E,controlledProgress:O,progress:T||0})))},R=function(e,t){return void 0===t&&(t=!1),{enter:`Toastify--animate Toastify__${e}-enter`,exit:`Toastify--animate Toastify__${e}-exit`,appendPosition:t}},D=u(R("bounce",!0)),U=u(R("slide",!0)),M=u(R("zoom")),P=u(R("flip")),V={position:"top-right",transition:D,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light"};function O(e){let t={...V,...e},a=e.stacked,[n,r]=(0,s.useState)(!0),c=(0,s.useRef)(null),{getToastToRender:d,isToastActive:u,count:g}=v(t),{className:m,style:p,rtl:h,containerId:A}=t;function f(){a&&(r(!0),k.play())}return _(()=>{if(a){var e;let a=c.current.querySelectorAll('[data-in="true"]'),s=null==(e=t.position)?void 0:e.includes("top"),o=0,r=0;Array.from(a).reverse().forEach((e,t)=>{e.classList.add("Toastify__toast--stacked"),t>0&&(e.dataset.collapsed=`${n}`),e.dataset.pos||(e.dataset.pos=s?"top":"bot");let a=o*(n?.2:1)+(n?0:12*t);e.style.setProperty("--y",`${s?a:-1*a}px`),e.style.setProperty("--g","12"),e.style.setProperty("--s",""+(1-(n?r:0))),o+=e.offsetHeight,r+=.025})}},[n,g,a]),s.createElement("div",{ref:c,className:"Toastify",id:A,onMouseEnter:()=>{a&&(r(!1),k.pause())},onMouseLeave:f},d((e,t)=>{let n=t.length?{...p}:{...p,pointerEvents:"none"};return s.createElement("div",{className:function(e){let t=o("Toastify__toast-container",`Toastify__toast-container--${e}`,{"Toastify__toast-container--rtl":h});return i(m)?m({position:e,rtl:h,defaultClassName:t}):o(t,l(m))}(e),style:n,key:`container-${e}`},t.map(e=>{let{content:t,props:o}=e;return s.createElement(S,{...o,stacked:a,collapseAll:f,isIn:u(o.toastId,o.containerId),style:o.style,key:`toast-${o.key}`},t)}))}))}}},function(e){e.O(0,[580,691,971,938,744],function(){return e(e.s=7157)}),_N_E=e.O()}]);
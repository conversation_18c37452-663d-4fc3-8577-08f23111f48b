(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[327],{1362:function(e,t,a){Promise.resolve().then(a.bind(a,8471))},8471:function(e,t,a){"use strict";a.r(t);var s=a(7437),n=a(4257),o=a(2173),r=a(6691),l=a.n(r),i=a(1396),c=a.n(i),d=a(4033),u=a(2265),m=a(7948);a(8062);var p=a(3637);t.default=()=>{(0,d.useRouter)();let[e,t]=(0,u.useState)({name:"",email:"",message:""}),[a,r]=(0,u.useState)(!1),[i,g]=(0,u.useState)(!1),[f,h]=(0,u.useState)(!1),[v,y]=(0,u.useState)({email:"",password:""}),[x,b]=(0,u.useState)(!1),[w,N]=(0,u.useState)(!1);(0,u.useEffect)(()=>{let e=localStorage.getItem("rememberedEmail"),a=localStorage.getItem("rememberedPassword"),s="true"===localStorage.getItem("rememberMe");e&&a&&s&&(y({email:e,password:a}),N(!0));let n=localStorage.getItem("authToken"),r=localStorage.getItem("userId");if(!n||!r){g(!1),h(!0);return}g(!0),(async()=>{try{let e=await o.Z.get("/api/profile?userId=".concat(r));e.data.success&&t({name:e.data.user.name||"",email:e.data.user.email||"",message:""})}catch(e){console.error("Profile fetch error:",e)}})()},[]);let j=a=>{t({...e,[a.target.name]:a.target.value})},E=async a=>{if(a.preventDefault(),!i){h(!0);return}try{r(!0),(await o.Z.post("/api/feedback",e)).data.success&&(m.toast.success("Feedback submitted successfully"),t({...e,message:""}))}catch(e){var s,n;m.toast.error((null===(n=e.response)||void 0===n?void 0:null===(s=n.data)||void 0===s?void 0:s.message)||"Failed to submit feedback")}finally{r(!1)}},T=e=>{y({...v,[e.target.name]:e.target.value})},k=async e=>{e.preventDefault();try{let e=await o.Z.post("/api/auth",{email:v.email,password:v.password});e.data.success?(localStorage.setItem("authToken",e.data.token||"dummy-token"),localStorage.setItem("userRole",e.data.user.role),localStorage.setItem("userId",e.data.user.id),w?(localStorage.setItem("rememberedEmail",v.email),localStorage.setItem("rememberedPassword",v.password),localStorage.setItem("rememberMe","true")):(localStorage.removeItem("rememberedEmail"),localStorage.removeItem("rememberedPassword"),localStorage.removeItem("rememberMe")),m.toast.success("Login successful"),g(!0),h(!1),t({name:e.data.user.name||"",email:e.data.user.email||"",message:""})):m.toast.error("Invalid credentials")}catch(e){var a,s;console.error("Login error:",e),m.toast.error((null===(s=e.response)||void 0===s?void 0:null===(a=s.data)||void 0===a?void 0:a.message)||"Login failed")}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(m.ToastContainer,{theme:"dark"}),(0,s.jsxs)("div",{className:"bg-gray-200 py-5 px-5 md:px-12 lg:px-28",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(c(),{href:"/",children:(0,s.jsx)(l(),{src:n.L.logo,width:180,alt:"Mr.Blogger",className:"w-[130px] sm:w-auto"})}),(0,s.jsx)(c(),{href:"/",children:(0,s.jsx)("button",{className:"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]",children:"Back to Home"})})]}),(0,s.jsxs)("div",{className:"text-center my-16",children:[(0,s.jsx)("h1",{className:"text-3xl sm:text-5xl font-semibold",children:"Contact Us"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"We'd love to hear from you. Send us a message!"})]})]}),f&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white p-8 rounded-lg max-w-md w-full",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Login Required"}),(0,s.jsx)("p",{className:"mb-4",children:"Please login to submit feedback"}),(0,s.jsxs)("form",{onSubmit:k,children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-gray-700 mb-2",children:"Email"}),(0,s.jsx)("input",{type:"email",name:"email",value:v.email,onChange:T,className:"w-full px-3 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-gray-700 mb-2",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:x?"text":"password",name:"password",value:v.password,onChange:T,className:"w-full px-3 py-2 border border-gray-300 rounded-md pr-10",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>{b(!x)},className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600",children:x?(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,s.jsx)("path",{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}),(0,s.jsx)("path",{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"})]}):(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,s.jsx)("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),(0,s.jsx)("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]})})]})]}),(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:w,onChange:e=>N(e.target.checked),className:"mr-2"}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"Remember me"})]})}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("button",{type:"submit",className:"bg-black text-white px-4 py-2 rounded-md",children:"Login"}),(0,s.jsx)(c(),{href:"/",children:(0,s.jsx)("button",{type:"button",className:"text-gray-600 px-4 py-2",children:"Cancel"})})]})]})]})}),(0,s.jsx)("div",{className:"container mx-auto px-4 py-12 max-w-4xl",children:(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-md p-8",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Get in Touch"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"Have questions, suggestions, or feedback? Fill out the form and we'll get back to you as soon as possible."}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"bg-black p-3 rounded-full mr-4",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium",children:"Email"}),(0,s.jsx)("p",{className:"text-gray-600",children:"<EMAIL>"})]})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"bg-black p-3 rounded-full mr-4",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium",children:"Phone"}),(0,s.jsx)("p",{className:"text-gray-600",children:"+****************"})]})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"bg-black p-3 rounded-full mr-4",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium",children:"Address"}),(0,s.jsx)("p",{className:"text-gray-600",children:"123 Blog Street, Content City, 10001"})]})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Send a Message"}),i?(0,s.jsxs)("form",{onSubmit:E,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:"Your Name"}),(0,s.jsx)("input",{type:"text",id:"name",name:"name",placeholder:"John Doe",value:e.name,onChange:j,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black",required:!0,readOnly:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Your Email"}),(0,s.jsx)("input",{type:"email",id:"email",name:"email",placeholder:"<EMAIL>",value:e.email,onChange:j,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black",required:!0,readOnly:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-1",children:"Your Message"}),(0,s.jsx)("textarea",{id:"message",name:"message",placeholder:"How can we help you?",value:e.message,onChange:j,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black min-h-[150px]",required:!0})]}),(0,s.jsx)("button",{type:"submit",disabled:a,className:"w-full bg-black text-white py-3 px-4 rounded-md font-medium hover:bg-gray-800 transition disabled:opacity-70",children:a?"Sending...":"Send Message"})]}):(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Please log in to send us a message"}),(0,s.jsx)("button",{onClick:()=>h(!0),className:"bg-black text-white py-2 px-6 rounded-md font-medium hover:bg-gray-800 transition",children:"Login"})]})]})]})})}),(0,s.jsx)(p.Z,{})]})}},8062:function(){},4033:function(e,t,a){e.exports=a(5313)},7948:function(e,t,a){"use strict";a.r(t),a.d(t,{Bounce:function(){return $},Flip:function(){return A},Icons:function(){return M},Slide:function(){return z},ToastContainer:function(){return D},Zoom:function(){return R},collapseToast:function(){return d},cssTransition:function(){return u},toast:function(){return _},useToast:function(){return N},useToastContainer:function(){return w}});var s=a(2265),n=function(){for(var e,t,a=0,s="",n=arguments.length;a<n;a++)(e=arguments[a])&&(t=function e(t){var a,s,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t){if(Array.isArray(t)){var o=t.length;for(a=0;a<o;a++)t[a]&&(s=e(t[a]))&&(n&&(n+=" "),n+=s)}else for(s in t)t[s]&&(n&&(n+=" "),n+=s)}return n}(e))&&(s&&(s+=" "),s+=t);return s};let o=e=>"number"==typeof e&&!isNaN(e),r=e=>"string"==typeof e,l=e=>"function"==typeof e,i=e=>r(e)||l(e)?e:null,c=e=>(0,s.isValidElement)(e)||r(e)||l(e)||o(e);function d(e,t,a){void 0===a&&(a=300);let{scrollHeight:s,style:n}=e;requestAnimationFrame(()=>{n.minHeight="initial",n.height=s+"px",n.transition=`all ${a}ms`,requestAnimationFrame(()=>{n.height="0",n.padding="0",n.margin="0",setTimeout(t,a)})})}function u(e){let{enter:t,exit:a,appendPosition:n=!1,collapse:o=!0,collapseDuration:r=300}=e;return function(e){let{children:l,position:i,preventExitTransition:c,done:u,nodeRef:m,isIn:p,playToast:g}=e,f=n?`${t}--${i}`:t,h=n?`${a}--${i}`:a,v=(0,s.useRef)(0);return(0,s.useLayoutEffect)(()=>{let e=m.current,t=f.split(" "),a=s=>{s.target===m.current&&(g(),e.removeEventListener("animationend",a),e.removeEventListener("animationcancel",a),0===v.current&&"animationcancel"!==s.type&&e.classList.remove(...t))};e.classList.add(...t),e.addEventListener("animationend",a),e.addEventListener("animationcancel",a)},[]),(0,s.useEffect)(()=>{let e=m.current,t=()=>{e.removeEventListener("animationend",t),o?d(e,u,r):u()};p||(c?t():(v.current=1,e.className+=` ${h}`,e.addEventListener("animationend",t)))},[p]),s.createElement(s.Fragment,null,l)}}function m(e,t){return null!=e?{content:e.content,containerId:e.props.containerId,id:e.props.toastId,theme:e.props.theme,type:e.props.type,data:e.props.data||{},isLoading:e.props.isLoading,icon:e.props.icon,status:t}:{}}let p=new Map,g=[],f=new Set,h=e=>f.forEach(t=>t(e)),v=()=>p.size>0;function y(e,t){var a;if(t)return!(null==(a=p.get(t))||!a.isToastActive(e));let s=!1;return p.forEach(t=>{t.isToastActive(e)&&(s=!0)}),s}function x(e,t){c(e)&&(v()||g.push({content:e,options:t}),p.forEach(a=>{a.buildToast(e,t)}))}function b(e,t){p.forEach(a=>{null!=t&&null!=t&&t.containerId?(null==t?void 0:t.containerId)===a.id&&a.toggle(e,null==t?void 0:t.id):a.toggle(e,null==t?void 0:t.id)})}function w(e){let{subscribe:t,getSnapshot:a,setProps:n}=(0,s.useRef)(function(e){let t=e.containerId||1;return{subscribe(a){let n=function(e,t,a){let n=1,d=0,u=[],p=[],g=[],f=t,h=new Map,v=new Set,y=()=>{g=Array.from(h.values()),v.forEach(e=>e())},x=e=>{p=null==e?[]:p.filter(t=>t!==e),y()},b=e=>{let{toastId:t,onOpen:n,updateId:o,children:r}=e.props,i=null==o;e.staleId&&h.delete(e.staleId),h.set(t,e),p=[...p,e.props.toastId].filter(t=>t!==e.staleId),y(),a(m(e,i?"added":"updated")),i&&l(n)&&n((0,s.isValidElement)(r)&&r.props)};return{id:e,props:f,observe:e=>(v.add(e),()=>v.delete(e)),toggle:(e,t)=>{h.forEach(a=>{null!=t&&t!==a.props.toastId||l(a.toggle)&&a.toggle(e)})},removeToast:x,toasts:h,clearQueue:()=>{d-=u.length,u=[]},buildToast:(t,p)=>{var g,v;if((t=>{let{containerId:a,toastId:s,updateId:n}=t,o=h.has(s)&&null==n;return(a?a!==e:1!==e)||o})(p))return;let{toastId:w,updateId:N,data:j,staleId:E,delay:T}=p,k=()=>{x(w)},I=null==N;I&&d++;let C={...f,style:f.toastStyle,key:n++,...Object.fromEntries(Object.entries(p).filter(e=>{let[t,a]=e;return null!=a})),toastId:w,updateId:N,data:j,closeToast:k,isIn:!1,className:i(p.className||f.toastClassName),bodyClassName:i(p.bodyClassName||f.bodyClassName),progressClassName:i(p.progressClassName||f.progressClassName),autoClose:!p.isLoading&&(g=p.autoClose,v=f.autoClose,!1===g||o(g)&&g>0?g:v),deleteToast(){let e=h.get(w),{onClose:t,children:n}=e.props;l(t)&&t((0,s.isValidElement)(n)&&n.props),a(m(e,"removed")),h.delete(w),--d<0&&(d=0),u.length>0?b(u.shift()):y()}};C.closeButton=f.closeButton,!1===p.closeButton||c(p.closeButton)?C.closeButton=p.closeButton:!0===p.closeButton&&(C.closeButton=!c(f.closeButton)||f.closeButton);let _=t;(0,s.isValidElement)(t)&&!r(t.type)?_=(0,s.cloneElement)(t,{closeToast:k,toastProps:C,data:j}):l(t)&&(_=t({closeToast:k,toastProps:C,data:j}));let L={content:_,props:C,staleId:E};f.limit&&f.limit>0&&d>f.limit&&I?u.push(L):o(T)?setTimeout(()=>{b(L)},T):b(L)},setProps(e){f=e},setToggle:(e,t)=>{h.get(e).toggle=t},isToastActive:e=>p.some(t=>t===e),getSnapshot:()=>f.newestOnTop?g.reverse():g}}(t,e,h);p.set(t,n);let d=n.observe(a);return g.forEach(e=>x(e.content,e.options)),g=[],()=>{d(),p.delete(t)}},setProps(e){var a;null==(a=p.get(t))||a.setProps(e)},getSnapshot(){var e;return null==(e=p.get(t))?void 0:e.getSnapshot()}}}(e)).current;n(e);let d=(0,s.useSyncExternalStore)(t,a,a);return{getToastToRender:function(e){if(!d)return[];let t=new Map;return d.forEach(e=>{let{position:a}=e.props;t.has(a)||t.set(a,[]),t.get(a).push(e)}),Array.from(t,t=>e(t[0],t[1]))},isToastActive:y,count:null==d?void 0:d.length}}function N(e){var t,a;let[n,o]=(0,s.useState)(!1),[r,l]=(0,s.useState)(!1),i=(0,s.useRef)(null),c=(0,s.useRef)({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:d,pauseOnHover:u,closeToast:m,onClick:g,closeOnClick:f}=e;function h(){o(!0)}function v(){o(!1)}function y(t){let a=i.current;c.canDrag&&a&&(c.didMove=!0,n&&v(),c.delta="x"===e.draggableDirection?t.clientX-c.start:t.clientY-c.start,c.start!==t.clientX&&(c.canCloseOnClick=!1),a.style.transform=`translate3d(${"x"===e.draggableDirection?`${c.delta}px, var(--y)`:`0, calc(${c.delta}px + var(--y))`},0)`,a.style.opacity=""+(1-Math.abs(c.delta/c.removalDistance)))}function x(){document.removeEventListener("pointermove",y),document.removeEventListener("pointerup",x);let t=i.current;if(c.canDrag&&c.didMove&&t){if(c.canDrag=!1,Math.abs(c.delta)>c.removalDistance)return l(!0),e.closeToast(),void e.collapseAll();t.style.transition="transform 0.2s, opacity 0.2s",t.style.removeProperty("transform"),t.style.removeProperty("opacity")}}null==(a=p.get((t={id:e.toastId,containerId:e.containerId,fn:o}).containerId||1))||a.setToggle(t.id,t.fn),(0,s.useEffect)(()=>{if(e.pauseOnFocusLoss)return document.hasFocus()||v(),window.addEventListener("focus",h),window.addEventListener("blur",v),()=>{window.removeEventListener("focus",h),window.removeEventListener("blur",v)}},[e.pauseOnFocusLoss]);let b={onPointerDown:function(t){if(!0===e.draggable||e.draggable===t.pointerType){c.didMove=!1,document.addEventListener("pointermove",y),document.addEventListener("pointerup",x);let a=i.current;c.canCloseOnClick=!0,c.canDrag=!0,a.style.transition="none","x"===e.draggableDirection?(c.start=t.clientX,c.removalDistance=a.offsetWidth*(e.draggablePercent/100)):(c.start=t.clientY,c.removalDistance=a.offsetHeight*(80===e.draggablePercent?1.5*e.draggablePercent:e.draggablePercent)/100)}},onPointerUp:function(t){let{top:a,bottom:s,left:n,right:o}=i.current.getBoundingClientRect();"touchend"!==t.nativeEvent.type&&e.pauseOnHover&&t.clientX>=n&&t.clientX<=o&&t.clientY>=a&&t.clientY<=s?v():h()}};return d&&u&&(b.onMouseEnter=v,e.stacked||(b.onMouseLeave=h)),f&&(b.onClick=e=>{g&&g(e),c.canCloseOnClick&&m()}),{playToast:h,pauseToast:v,isRunning:n,preventExitTransition:r,toastRef:i,eventHandlers:b}}function j(e){let{delay:t,isRunning:a,closeToast:o,type:r="default",hide:i,className:c,style:d,controlledProgress:u,progress:m,rtl:p,isIn:g,theme:f}=e,h=i||u&&0===m,v={...d,animationDuration:`${t}ms`,animationPlayState:a?"running":"paused"};u&&(v.transform=`scaleX(${m})`);let y=n("Toastify__progress-bar",u?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${f}`,`Toastify__progress-bar--${r}`,{"Toastify__progress-bar--rtl":p}),x=l(c)?c({rtl:p,type:r,defaultClassName:y}):n(y,c);return s.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":h},s.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${f} Toastify__progress-bar--${r}`}),s.createElement("div",{role:"progressbar","aria-hidden":h?"true":"false","aria-label":"notification timer",className:x,style:v,[u&&m>=1?"onTransitionEnd":"onAnimationEnd"]:u&&m<1?null:()=>{g&&o()}}))}let E=1,T=()=>""+E++;function k(e,t){return x(e,t),t.toastId}function I(e,t){return{...t,type:t&&t.type||e,toastId:t&&(r(t.toastId)||o(t.toastId))?t.toastId:T()}}function C(e){return(t,a)=>k(t,I(e,a))}function _(e,t){return k(e,I("default",t))}_.loading=(e,t)=>k(e,I("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...t})),_.promise=function(e,t,a){let s,{pending:n,error:o,success:i}=t;n&&(s=r(n)?_.loading(n,a):_.loading(n.render,{...a,...n}));let c={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},d=(e,t,n)=>{if(null==t)return void _.dismiss(s);let o={type:e,...c,...a,data:n},l=r(t)?{render:t}:t;return s?_.update(s,{...o,...l}):_(l.render,{...o,...l}),n},u=l(e)?e():e;return u.then(e=>d("success",i,e)).catch(e=>d("error",o,e)),u},_.success=C("success"),_.info=C("info"),_.error=C("error"),_.warning=C("warning"),_.warn=_.warning,_.dark=(e,t)=>k(e,I("default",{theme:"dark",...t})),_.dismiss=function(e){var t,a;v()?null==e||r(t=e)||o(t)?p.forEach(t=>{t.removeToast(e)}):e&&("containerId"in e||"id"in e)&&((null==(a=p.get(e.containerId))?void 0:a.removeToast(e.id))||p.forEach(t=>{t.removeToast(e.id)})):g=g.filter(t=>null!=e&&t.options.toastId!==e)},_.clearWaitingQueue=function(e){void 0===e&&(e={}),p.forEach(t=>{!t.props.limit||e.containerId&&t.id!==e.containerId||t.clearQueue()})},_.isActive=y,_.update=function(e,t){void 0===t&&(t={});let a=((e,t)=>{var a;let{containerId:s}=t;return null==(a=p.get(s||1))?void 0:a.toasts.get(e)})(e,t);if(a){let{props:s,content:n}=a,o={delay:100,...s,...t,toastId:t.toastId||e,updateId:T()};o.toastId!==e&&(o.staleId=e);let r=o.render||n;delete o.render,k(r,o)}},_.done=e=>{_.update(e,{progress:1})},_.onChange=function(e){return f.add(e),()=>{f.delete(e)}},_.play=e=>b(!0,e),_.pause=e=>b(!1,e);let L="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,S=e=>{let{theme:t,type:a,isLoading:n,...o}=e;return s.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:"colored"===t?"currentColor":`var(--toastify-icon-color-${a})`,...o})},M={info:function(e){return s.createElement(S,{...e},s.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))},warning:function(e){return s.createElement(S,{...e},s.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))},success:function(e){return s.createElement(S,{...e},s.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))},error:function(e){return s.createElement(S,{...e},s.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))},spinner:function(){return s.createElement("div",{className:"Toastify__spinner"})}},P=e=>{let{isRunning:t,preventExitTransition:a,toastRef:o,eventHandlers:r,playToast:i}=N(e),{closeButton:c,children:d,autoClose:u,onClick:m,type:p,hideProgressBar:g,closeToast:f,transition:h,position:v,className:y,style:x,bodyClassName:b,bodyStyle:w,progressClassName:E,progressStyle:T,updateId:k,role:I,progress:C,rtl:_,toastId:L,deleteToast:S,isIn:P,isLoading:B,closeOnClick:$,theme:z}=e,R=n("Toastify__toast",`Toastify__toast-theme--${z}`,`Toastify__toast--${p}`,{"Toastify__toast--rtl":_},{"Toastify__toast--close-on-click":$}),A=l(y)?y({rtl:_,position:v,type:p,defaultClassName:R}):n(R,y),O=function(e){let{theme:t,type:a,isLoading:n,icon:o}=e,r=null,i={theme:t,type:a,isLoading:n};return!1===o||(l(o)?r=o(i):(0,s.isValidElement)(o)?r=(0,s.cloneElement)(o,i):n?r=M.spinner():a in M&&(r=M[a](i))),r}(e),D=!!C||!u,F={closeToast:f,type:p,theme:z},H=null;return!1===c||(H=l(c)?c(F):(0,s.isValidElement)(c)?(0,s.cloneElement)(c,F):function(e){let{closeToast:t,theme:a,ariaLabel:n="close"}=e;return s.createElement("button",{className:`Toastify__close-button Toastify__close-button--${a}`,type:"button",onClick:e=>{e.stopPropagation(),t(e)},"aria-label":n},s.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},s.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}(F)),s.createElement(h,{isIn:P,done:S,position:v,preventExitTransition:a,nodeRef:o,playToast:i},s.createElement("div",{id:L,onClick:m,"data-in":P,className:A,...r,style:x,ref:o},s.createElement("div",{...P&&{role:I},className:l(b)?b({type:p}):n("Toastify__toast-body",b),style:w},null!=O&&s.createElement("div",{className:n("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!B})},O),s.createElement("div",null,d)),H,s.createElement(j,{...k&&!D?{key:`pb-${k}`}:{},rtl:_,theme:z,delay:u,isRunning:t,isIn:P,closeToast:f,hide:g,type:p,style:T,className:E,controlledProgress:D,progress:C||0})))},B=function(e,t){return void 0===t&&(t=!1),{enter:`Toastify--animate Toastify__${e}-enter`,exit:`Toastify--animate Toastify__${e}-exit`,appendPosition:t}},$=u(B("bounce",!0)),z=u(B("slide",!0)),R=u(B("zoom")),A=u(B("flip")),O={position:"top-right",transition:$,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light"};function D(e){let t={...O,...e},a=e.stacked,[o,r]=(0,s.useState)(!0),c=(0,s.useRef)(null),{getToastToRender:d,isToastActive:u,count:m}=w(t),{className:p,style:g,rtl:f,containerId:h}=t;function v(){a&&(r(!0),_.play())}return L(()=>{if(a){var e;let a=c.current.querySelectorAll('[data-in="true"]'),s=null==(e=t.position)?void 0:e.includes("top"),n=0,r=0;Array.from(a).reverse().forEach((e,t)=>{e.classList.add("Toastify__toast--stacked"),t>0&&(e.dataset.collapsed=`${o}`),e.dataset.pos||(e.dataset.pos=s?"top":"bot");let a=n*(o?.2:1)+(o?0:12*t);e.style.setProperty("--y",`${s?a:-1*a}px`),e.style.setProperty("--g","12"),e.style.setProperty("--s",""+(1-(o?r:0))),n+=e.offsetHeight,r+=.025})}},[o,m,a]),s.createElement("div",{ref:c,className:"Toastify",id:h,onMouseEnter:()=>{a&&(r(!1),_.pause())},onMouseLeave:v},d((e,t)=>{let o=t.length?{...g}:{...g,pointerEvents:"none"};return s.createElement("div",{className:function(e){let t=n("Toastify__toast-container",`Toastify__toast-container--${e}`,{"Toastify__toast-container--rtl":f});return l(p)?p({position:e,rtl:f,defaultClassName:t}):n(t,i(p))}(e),style:o,key:`container-${e}`},t.map(e=>{let{content:t,props:n}=e;return s.createElement(P,{...n,stacked:a,collapseAll:v,isIn:u(n.toastId,n.containerId),style:n.style,key:`toast-${n.key}`},t)}))}))}}},function(e){e.O(0,[580,691,396,637,971,938,744],function(){return e(e.s=1362)}),_N_E=e.O()}]);
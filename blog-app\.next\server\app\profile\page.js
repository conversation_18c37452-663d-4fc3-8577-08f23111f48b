(()=>{var e={};e.id=4178,e.ids=[4178],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},44017:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(50482),a=t(69108),o=t(62563),l=t.n(o),i=t(68300),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);t.d(r,n);let d=["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,71114)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\profile\\page.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,39459)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\profile\\page.jsx"],u="/profile/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},15586:(e,r,t)=>{Promise.resolve().then(t.bind(t,80549))},80549:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var s=t(95344),a=t(3729),o=t(22254),l=t(69697),i=t(20783),n=t.n(i),d=t(41223),c=t.n(d),u=t(53608),m=t(6880),p=t(96556),x=t(79338);let g=()=>{let e=(0,o.useRouter)(),[r,t]=(0,a.useState)(!0),[i,d]=(0,a.useState)({name:"",email:"",profilePicture:"/default_profile.png"}),[g,h]=(0,a.useState)(null),[b,f]=(0,a.useState)("profile"),[y,w]=(0,a.useState)(!1),[v,j]=(0,a.useState)(null),[N,P]=(0,a.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[k,C]=(0,a.useState)(!1),[S,_]=(0,a.useState)([]),[q,F]=(0,a.useState)(!1),[D,I]=(0,a.useState)(!1),[L,B]=(0,a.useState)(!1),[M,A]=(0,a.useState)(null),[E,R]=(0,a.useState)({x:0,y:0}),[$,z]=(0,a.useState)(1),[U,Z]=(0,a.useState)(null),[T,G]=(0,a.useState)(null),Y=()=>{w(!0)};(0,a.useEffect)(()=>{let r=localStorage.getItem("authToken"),s=localStorage.getItem("userId");if(!r||!s){l.toast.error("Please log in to view your profile"),e.push("/");return}(async()=>{try{let e=await u.Z.get(`/api/profile?userId=${s}`);e.data.success?d({id:e.data.user.id,email:e.data.user.email,name:e.data.user.name||"",role:e.data.user.role,profilePicture:e.data.user.profilePicture}):l.toast.error(e.data.message||"Failed to load profile")}catch(e){console.error("Profile fetch error:",e),l.toast.error("Failed to load profile data")}finally{t(!1)}})(),"favorites"===b&&H(r)},[e,b]);let H=async e=>{try{F(!0);let r=await u.Z.get("/api/favorites",{headers:{Authorization:`Bearer ${e}`}});r.data.success?_(r.data.favorites||[]):l.toast.error("Failed to load favorites")}catch(e){console.error("Error fetching favorites:",e),l.toast.error("Failed to load favorites")}finally{F(!1)}},W=async e=>{try{let r=localStorage.getItem("authToken");(await u.Z.delete(`/api/favorites?blogId=${e}`,{headers:{Authorization:`Bearer ${r}`}})).data.success?(_(S.filter(r=>r._id.toString()!==e)),l.toast.success("Removed from favorites")):l.toast.error("Failed to remove from favorites")}catch(e){console.error("Error removing favorite:",e),l.toast.error("Failed to remove from favorites")}},O=async(e,r)=>{let t=new(c());t.src=e;let s=document.createElement("canvas"),a=s.getContext("2d");return s.width=r.width,s.height=r.height,a.drawImage(t,r.x,r.y,r.width,r.height,0,0,r.width,r.height),new Promise(e=>{s.toBlob(r=>{e(r)},"image/jpeg")})},V=async()=>{try{if(!U)return;let e=await O(M,U),r=URL.createObjectURL(e);h(r);let t=new File([e],"cropped_profile.jpg",{type:"image/jpeg"});j(t),B(!1)}catch(e){console.error("Error applying crop:",e),l.toast.error("Failed to crop image")}},X=async e=>{e.preventDefault(),I(!1);try{let e=new FormData;e.append("userId",i.id),e.append("name",i.name),v&&e.append("profilePicture",v);let r=await u.Z.put("/api/profile",e);r.data.success?(localStorage.setItem("userName",r.data.user.name),localStorage.setItem("userProfilePicture",r.data.user.profilePicture),d({...i,name:r.data.user.name,profilePicture:r.data.user.profilePicture}),j(null),h(null),I(!0),l.toast.success("Profile updated successfully"),setTimeout(()=>{window.location.reload()},1e3)):l.toast.error(r.data.message||"Failed to update profile")}catch(e){console.error("Profile update error:",e),l.toast.error("Failed to update profile")}},J=e=>{P({...N,[e.target.name]:e.target.value})},K=async e=>{if(e.preventDefault(),N.newPassword!==N.confirmPassword){l.toast.error("New passwords do not match");return}try{let e=await u.Z.put("/api/password",{userId:i.id,currentPassword:N.currentPassword,newPassword:N.newPassword});e.data.success?(l.toast.success("Password updated successfully"),P({currentPassword:"",newPassword:"",confirmPassword:""}),C(!1)):l.toast.error(e.data.message||"Failed to update password")}catch(e){console.error("Password update error:",e),l.toast.error(e.response?.data?.message||"Failed to update password")}};return r?s.jsx("div",{className:"min-h-screen flex justify-center items-center",children:s.jsx("p",{children:"Loading profile..."})}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"bg-gray-200 py-5 px-5 md:px-12 lg:px-28",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[s.jsx(n(),{href:"/",children:s.jsx(c(),{src:m.L.logo,width:180,alt:"Mr.Blogger",className:"w-[130px] sm:w-auto"})}),s.jsx(n(),{href:"/",children:s.jsx("button",{className:"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]",children:"Back to Home"})})]}),(0,s.jsxs)("div",{className:"text-center my-16",children:[s.jsx("h1",{className:"text-3xl sm:text-5xl font-semibold",children:"Your Profile"}),s.jsx("p",{className:"mt-4 text-gray-600",children:"Manage your account and preferences"})]})]}),s.jsx("div",{className:"min-h-screen bg-gray-100 py-8",children:s.jsx("div",{className:"container mx-auto px-4 max-w-4xl",children:(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:[s.jsx("div",{className:"mb-6",children:(0,s.jsxs)("div",{className:"flex border-b border-gray-300",children:[s.jsx("button",{onClick:()=>f("profile"),className:`py-3 px-6 font-medium ${"profile"===b?"border-b-2 border-black text-black":"text-gray-500 hover:text-gray-700"}`,children:"Profile"}),s.jsx("button",{onClick:()=>f("favorites"),className:`py-3 px-6 font-medium ${"favorites"===b?"border-b-2 border-black text-black":"text-gray-500 hover:text-gray-700"}`,children:"My Favorites"})]})}),"profile"===b?(0,s.jsxs)("form",{onSubmit:X,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center mb-8",children:[(0,s.jsxs)("div",{className:"relative mb-4",children:[s.jsx(c(),{src:g||i.profilePicture,width:150,height:150,alt:"Profile",className:"rounded-full object-cover w-[150px] h-[150px] border-4 border-gray-200"}),s.jsx("label",{htmlFor:"profilePicture",className:"absolute bottom-0 right-0 bg-black text-white p-2 rounded-full cursor-pointer",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[s.jsx("path",{d:"M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"}),s.jsx("circle",{cx:"12",cy:"13",r:"4"})]})}),s.jsx("input",{type:"file",id:"profilePicture",onChange:e=>{let r=e.target.files[0];if(r){let e=new FileReader;e.onloadend=()=>{A(e.result),B(!0)},e.readAsDataURL(r)}},className:"hidden",accept:"image/*"})]}),s.jsx("p",{className:"text-sm text-gray-500",children:"Click the camera icon to change your profile picture"})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"Email"}),s.jsx("input",{type:"email",value:i.email,className:"w-full px-4 py-3 border border-gray-300 rounded-md bg-gray-100",disabled:!0}),s.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Email cannot be changed"})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"Display Name"}),s.jsx("input",{type:"text",value:i.name,onChange:e=>{d({...i,name:e.target.value})},className:"w-full px-4 py-3 border border-gray-300 rounded-md",placeholder:"Enter your name"})]}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4",children:[D&&s.jsx("div",{className:"fixed top-5 right-5 p-4 bg-white border-l-4 border-green-500 text-green-700 rounded shadow-lg z-50 max-w-xs animate-fade-in",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"mr-3",children:s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"font-bold",children:"Success!"}),s.jsx("p",{className:"text-sm",children:"Profile updated successfully."})]})]})}),s.jsx("button",{type:"submit",className:"px-6 py-3 bg-black text-white font-medium rounded-md hover:bg-gray-800 transition-colors",children:"Save Changes"}),s.jsx("button",{type:"button",onClick:()=>C(!k),className:"px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 transition-colors",children:"Change Password"}),"admin"===i.role&&s.jsx("button",{type:"button",onClick:r=>{r.preventDefault(),e.push("/admin")},className:"px-6 py-3 bg-black text-white font-medium rounded-md hover:bg-gray-800 transition-colors",children:"Dashboard"}),s.jsx("button",{type:"button",onClick:e=>{e.preventDefault(),Y()},className:"px-6 py-3 bg-red-600 text-white font-medium rounded-md hover:bg-red-700 transition-colors",children:"Logout"}),s.jsx("button",{type:"button",onClick:r=>{r.preventDefault(),e.push("/")},className:"px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 transition-colors",children:"Cancel"})]})]}):s.jsx("div",{children:q?s.jsx("div",{className:"flex justify-center items-center py-16",children:s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-black"})}):S.length>0?s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:S.map(e=>(0,s.jsxs)("div",{className:"bg-white rounded-lg overflow-hidden shadow-md border border-gray-200",children:[s.jsx("div",{className:"relative h-48",children:s.jsx(c(),{src:e.image||m.L.placeholder,alt:e.title,fill:!0,className:"object-cover"})}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[s.jsx("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 rounded-full",children:e.category}),s.jsx("button",{onClick:()=>W(e._id),className:"text-yellow-500 hover:text-yellow-700",title:"Remove from favorites",children:s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:s.jsx("polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"})})})]}),s.jsx("h2",{className:"text-lg font-semibold mb-2 line-clamp-2",children:e.title}),s.jsx("div",{className:"text-sm text-gray-600 mb-3 line-clamp-3",dangerouslySetInnerHTML:{__html:(0,p.A)(e.description,120)}}),s.jsx(n(),{href:`/blogs/${e._id}`,className:"inline-block px-3 py-1 bg-black text-white text-sm rounded hover:bg-gray-800 transition",children:"Read Article"})]})]},e._id))}):(0,s.jsxs)("div",{className:"bg-white rounded-lg p-8 text-center",children:[s.jsx("div",{className:"text-5xl mb-4",children:"⭐"}),s.jsx("h2",{className:"text-xl font-semibold mb-2",children:"No favorites yet"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"Start adding blogs to your favorites by clicking the star icon on blog posts."}),s.jsx(n(),{href:"/",className:"inline-block px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition",children:"Browse Blogs"})]})}),k&&"profile"===b&&(0,s.jsxs)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[s.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Change Password"}),(0,s.jsxs)("form",{onSubmit:K,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"Current Password"}),s.jsx("input",{type:"password",name:"currentPassword",value:N.currentPassword,onChange:J,className:"w-full px-4 py-3 border border-gray-300 rounded-md",required:!0})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"New Password"}),s.jsx("input",{type:"password",name:"newPassword",value:N.newPassword,onChange:J,className:"w-full px-4 py-3 border border-gray-300 rounded-md",required:!0})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"Confirm New Password"}),s.jsx("input",{type:"password",name:"confirmPassword",value:N.confirmPassword,onChange:J,className:"w-full px-4 py-3 border border-gray-300 rounded-md",required:!0})]}),(0,s.jsxs)("div",{className:"flex gap-4",children:[s.jsx("button",{type:"submit",className:"px-6 py-3 bg-black text-white font-medium rounded-md hover:bg-gray-800 transition-colors",children:"Update Password"}),s.jsx("button",{type:"button",onClick:()=>C(!1),className:"px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 transition-colors",children:"Cancel"})]})]})]})]})})}),y&&s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white p-6 rounded-md shadow-lg max-w-sm w-full",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Confirm Logout"}),s.jsx("p",{className:"mb-6",children:"Are you sure you want to log out? You will need to log in again to access your account."}),(0,s.jsxs)("div",{className:"flex justify-end gap-3",children:[s.jsx("button",{onClick:()=>{w(!1)},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"}),s.jsx("button",{onClick:()=>{localStorage.removeItem("authToken"),localStorage.removeItem("userRole"),localStorage.removeItem("userId"),localStorage.removeItem("userName"),localStorage.removeItem("userProfilePicture"),l.toast.success("Logged out successfully"),setTimeout(()=>{e.push("/")},300)},className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",children:"Logout"})]})]})}),L&&s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg max-w-2xl w-full",children:[s.jsx("h3",{className:"text-xl font-semibold mb-4",children:"Adjust Profile Picture"}),s.jsx("div",{className:"relative h-80 mb-4",children:s.jsx(x.ZP,{image:M,crop:E,zoom:$,aspect:1,cropShape:"round",onCropChange:R,onCropComplete:(e,r)=>{Z(r)},onZoomChange:z})}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Zoom: ",$.toFixed(1),"x"]}),s.jsx("input",{type:"range",min:1,max:3,step:.1,value:$,onChange:e=>z(parseFloat(e.target.value)),className:"w-full"})]}),(0,s.jsxs)("div",{className:"flex gap-3",children:[s.jsx("button",{type:"button",onClick:V,className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",children:"Apply"}),s.jsx("button",{type:"button",onClick:()=>{B(!1),A(null)},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"})]})]})})]})}},96556:(e,r,t)=>{"use strict";t.d(r,{A:()=>a,D:()=>s});let s=e=>{if(!e)return"";let r=e;return(r=(r=r.replace(/\{\{image:[^}]+\}\}/g,"")).replace(/\[\[[^\]]+\]\]/g,"")).replace(/\s+/g," ").trim()},a=(e,r=120)=>{let t=s(e);return t.length>r?t.substring(0,r)+"...":t}},71114:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>o,__esModule:()=>a,default:()=>l});let s=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\profile\page.jsx`),{__esModule:a,$$typeof:o}=s,l=s.default}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,3998,337,8468,9338,5757],()=>t(44017));module.exports=s})();
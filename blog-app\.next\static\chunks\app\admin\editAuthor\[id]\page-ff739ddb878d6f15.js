(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[379],{6511:function(e,a,t){Promise.resolve().then(t.bind(t,9444))},9444:function(e,a,t){"use strict";t.r(a);var r=t(7437),s=t(2265),l=t(4033),o=t(2173),n=t(7948),d=t(6691),i=t.n(d),c=t(5535);a.default=e=>{let{params:a}=e,t=(0,l.useRouter)(),[d,m]=(0,s.useState)(!0),[u,h]=(0,s.useState)({name:"",bio:"",image:null}),[p,g]=(0,s.useState)(null),[x,b]=(0,s.useState)(null),[y,f]=(0,s.useState)(!1),[j,v]=(0,s.useState)({x:0,y:0}),[N,w]=(0,s.useState)(1),[C,k]=(0,s.useState)(null),[E,S]=(0,s.useState)(null);(0,s.useEffect)(()=>{(async()=>{try{let e=await o.Z.get("/api/authors/".concat(a.id));if(e.data.success){let a=e.data.author;h({name:a.name,bio:a.bio||""}),g(a.image||"/author_img.png")}else n.toast.error("Failed to load author data"),t.push("/admin/settings")}catch(e){console.error("Error fetching author:",e),n.toast.error("Failed to load author data"),t.push("/admin/settings")}finally{m(!1)}})()},[a.id,t]);let A=e=>{let{name:a,value:t}=e.target;h(e=>({...e,[a]:t}))},F=(0,s.useCallback)((e,a)=>{k(a)},[]),_=e=>new Promise((a,t)=>{let r=new(i());r.addEventListener("load",()=>a(r)),r.addEventListener("error",e=>t(e)),r.src=e}),L=async(e,a)=>{let t=await _(e),r=document.createElement("canvas"),s=r.getContext("2d"),l=Math.max(t.width,t.height)/2*Math.sqrt(2)*2;r.width=l,r.height=l,s.drawImage(t,l/2-.5*t.width,l/2-.5*t.height);let o=s.getImageData(0,0,l,l);return r.width=a.width,r.height=a.height,s.putImageData(o,Math.round(0-l/2+.5*t.width-a.x),Math.round(0-l/2+.5*t.height-a.y)),new Promise(e=>{r.toBlob(a=>{e(a)},"image/jpeg")})},D=async()=>{try{if(!C)return;let e=await L(x,C),a=URL.createObjectURL(e);S(e),g(a),f(!1)}catch(e){console.error("Error applying crop:",e),n.toast.error("Failed to crop image")}},I=async e=>{if(e.preventDefault(),!u.name.trim()){n.toast.error("Author name is required");return}try{m(!0);let e=new FormData;if(e.append("name",u.name),e.append("bio",u.bio),E){let a=new File([E],"cropped_image.jpg",{type:"image/jpeg"});e.append("image",a)}let r=await o.Z.put("/api/authors/".concat(a.id),e);r.data.success?(n.toast.success("Author updated successfully"),t.push("/admin/settings")):n.toast.error(r.data.message||"Failed to update author")}catch(e){var r,s;console.error("Error updating author:",e),n.toast.error((null===(s=e.response)||void 0===s?void 0:null===(r=s.data)||void 0===r?void 0:r.message)||"Failed to update author")}finally{m(!1)}};return d?(0,r.jsx)("div",{className:"pt-5 px-5 sm:pt-12 sm:pl-16",children:(0,r.jsx)("p",{children:"Loading author data..."})}):(0,r.jsxs)("div",{className:"pt-5 px-5 sm:pt-12 sm:pl-16",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Edit Author"}),(0,r.jsx)("div",{className:"max-w-2xl bg-white p-6 rounded-lg border border-gray-300 shadow-md",children:y?(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Adjust Profile Image"}),(0,r.jsx)("div",{className:"relative h-80 mb-4",children:(0,r.jsx)(c.ZP,{image:x,crop:j,zoom:N,aspect:1,cropShape:"round",onCropChange:v,onCropComplete:F,onZoomChange:w})}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Zoom: ",N.toFixed(1),"x"]}),(0,r.jsx)("input",{type:"range",min:1,max:3,step:.1,value:N,onChange:e=>w(parseFloat(e.target.value)),className:"w-full"})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{type:"button",onClick:D,className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",children:"Apply"}),(0,r.jsx)("button",{type:"button",onClick:()=>{f(!1),b(null)},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"})]})]}):(0,r.jsxs)("form",{onSubmit:I,children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Author Name"}),(0,r.jsx)("input",{type:"text",name:"name",value:u.name,onChange:A,placeholder:"Enter author name",className:"w-full px-4 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Author Bio"}),(0,r.jsx)("textarea",{name:"bio",value:u.bio,onChange:A,placeholder:"Enter author bio",className:"w-full px-4 py-2 border border-gray-300 rounded-md",rows:3})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Author Image"}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-4",children:[p&&(0,r.jsx)("div",{children:(0,r.jsx)("img",{src:p,alt:"Author preview",className:"w-24 h-24 object-cover rounded-full border-2 border-gray-200"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{let a=e.target.files[0];if(a){let e=new FileReader;e.onload=()=>{b(e.result),f(!0)},e.readAsDataURL(a)}},className:"w-full px-4 py-2 border border-gray-300 rounded-md"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Select an image to upload and adjust"})]})]})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("button",{type:"submit",className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",disabled:d,children:"Update Author"}),(0,r.jsx)("button",{type:"button",onClick:()=>t.push("/admin/settings"),className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"})]})]})})]})}}},function(e){e.O(0,[580,691,442,971,938,744],function(){return e(e.s=6511)}),_N_E=e.O()}]);
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"a8e805603a4c\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL2dsb2JhbHMuY3NzP2NmNDciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJhOGU4MDU2MDNhNGNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./Components/EmailSubscriptionPopup.jsx":
/*!***********************************************!*\
  !*** ./Components/EmailSubscriptionPopup.jsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst EmailSubscriptionPopup = ()=>{\n    _s();\n    const [showPopup, setShowPopup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Don't show popup on admin pages\n        if (pathname && pathname.startsWith(\"/admin\")) {\n            return;\n        }\n        // Check if user has already subscribed (permanent dismissal)\n        const hasSubscribed = localStorage.getItem(\"emailSubscribed\");\n        if (hasSubscribed === \"true\") {\n            return;\n        }\n        // Check if user has permanently dismissed the popup (closed it twice)\n        const permanentlyDismissed = localStorage.getItem(\"emailPopupPermanentlyDismissed\");\n        if (permanentlyDismissed === \"true\") {\n            return;\n        }\n        // Check if user has closed the popup recently\n        const lastClosedTime = localStorage.getItem(\"emailPopupLastClosed\");\n        const closeCount = parseInt(localStorage.getItem(\"emailPopupCloseCount\") || \"0\");\n        const now = Date.now();\n        // If user has closed it once and it's been less than 5 minutes, don't show\n        if (lastClosedTime && closeCount >= 1 && now - parseInt(lastClosedTime) < 300000) {\n            return;\n        }\n        // Determine the delay based on whether this is first time or after first close\n        let delay;\n        if (closeCount === 0) {\n            // First time - show after 2 minutes\n            delay = 120000; // 2 minutes = 120000ms\n        } else {\n            // After first close - show after 5 minutes from last close\n            delay = Math.max(0, 300000 - (now - parseInt(lastClosedTime || \"0\")));\n        }\n        const timer = setTimeout(()=>{\n            setShowPopup(true);\n        }, delay);\n        // Cleanup timer on component unmount\n        return ()=>clearTimeout(timer);\n    }, [\n        pathname\n    ]);\n    const handleClose = ()=>{\n        setShowPopup(false);\n        // Remember when user closed the popup (timestamp)\n        localStorage.setItem(\"emailPopupLastClosed\", Date.now().toString());\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!email) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please enter your email address\");\n            return;\n        }\n        try {\n            setLoading(true);\n            const formData = new FormData();\n            formData.append(\"email\", email);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(\"/api/email\", formData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Successfully subscribed to our newsletter!\");\n                setShowPopup(false);\n                setEmail(\"\");\n                // Remember that user has subscribed\n                localStorage.setItem(\"emailSubscribed\", \"true\");\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Subscription failed. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Subscription error:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"An error occurred. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!showPopup) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-2xl max-w-md w-full relative overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleClose,\n                    className: \"absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10\",\n                    \"aria-label\": \"Close popup\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M6 18L18 6M6 6l12 12\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-800 mb-2\",\n                                    children: \"SUBSCRIBE NOW\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-sm\",\n                                    children: [\n                                        \"DON'T MISS OUT ON THE LATEST BLOG POSTS\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 54\n                                        }, undefined),\n                                        \"AND OFFERS.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-2\",\n                                    children: \"Be the first to get notified.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: email,\n                                        onChange: (e)=>setEmail(e.target.value),\n                                        placeholder: \"Email address\",\n                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"w-full bg-black text-white py-3 px-6 rounded-md hover:bg-gray-800 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: loading ? \"SUBSCRIBING...\" : \"SUBSCRIBE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 text-center mt-4\",\n                            children: \"You can unsubscribe at any time. We respect your privacy.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EmailSubscriptionPopup, \"MoxXD69UWL+dFz0n3UHaFaTj80M=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = EmailSubscriptionPopup;\n/* harmony default export */ __webpack_exports__[\"default\"] = (EmailSubscriptionPopup);\nvar _c;\n$RefreshReg$(_c, \"EmailSubscriptionPopup\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./Components/EmailSubscriptionPopup.jsx\n"));

/***/ })

});
"use strict";(()=>{var e={};e.id=1567,e.ids=[1567],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},12781:e=>{e.exports=require("stream")},73837:e=>{e.exports=require("util")},41236:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>w,originalPathname:()=>v,patchFetch:()=>b,requestAsyncStorage:()=>f,routeModule:()=>h,serverHooks:()=>x,staticGenerationAsyncStorage:()=>$,staticGenerationBailout:()=>S});var a={};r.r(a),r.d(a,{GET:()=>y,POST:()=>m});var s=r(95419),o=r(69108),n=r(99678),i=r(78070),c=r(91887),l=r(2299),u=r(6113),g=r.n(u),d=r(11497);let p=e=>{try{let t=e.headers.get("authorization");if(!t||!t.startsWith("Bearer "))return null;let r=t.split(" ")[1];if(!r)return null;return(0,d.W)(r)}catch(e){return console.error("Error getting user from token:",e),null}};async function m(e){try{await (0,c.n)();let{path:t,contentType:r,blogId:a,referrer:s}=await e.json(),o=p(e),n=o?.userId||null,u=e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"127.0.0.1",d=g().createHash("sha256").update(u+(process.env.IP_SALT||"salt")).digest("hex"),m=e.headers.get("user-agent")||"";return await l.Z.create({path:t,contentType:r,blogId:a,userId:n,ipHash:d,userAgent:m,referrer:s,timestamp:new Date}),i.Z.json({success:!0})}catch(e){return console.error("Error tracking analytics:",e),i.Z.json({success:!1,message:"Failed to track analytics"},{status:500})}}async function y(e){try{await (0,c.n)();let t=p(e);if(console.log("User data from token:",t),!t||"admin"!==t.role)return console.log("Unauthorized access attempt to analytics"),i.Z.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),a=r.get("period")||"7days",s=r.get("blogId");console.log("Fetching analytics for period:",a);let o=new Date,n=new Date;switch(a){case"24hours":n.setHours(n.getHours()-24);break;case"7days":default:n.setDate(n.getDate()-7);break;case"30days":n.setDate(n.getDate()-30);break;case"90days":n.setDate(n.getDate()-90)}let u={timestamp:{$gte:n,$lte:o}};s&&(u.blogId=s),console.log("Analytics query:",JSON.stringify(u));let g=await l.Z.countDocuments(u);console.log("Total views:",g);let d=await l.Z.distinct("ipHash",u).then(e=>e.length);console.log("Unique visitors:",d);let m=await l.Z.aggregate([{$match:u},{$group:{_id:"$contentType",count:{$sum:1}}}]);console.log("Views by type:",m);let y=await l.Z.aggregate([{$match:u},{$group:{_id:"$path",count:{$sum:1}}},{$sort:{count:-1}},{$limit:10}]);console.log("Top pages:",y);let h=await l.Z.aggregate([{$match:u},{$group:{_id:{$dateToString:{format:"%Y-%m-%d",date:"$timestamp"}},count:{$sum:1}}},{$sort:{_id:1}}]);console.log("Traffic over time:",h);let f=await l.Z.aggregate([{$match:{...u,referrer:{$ne:null,$ne:""}}},{$group:{_id:"$referrer",count:{$sum:1}}},{$sort:{count:-1}},{$limit:10}]);console.log("Top referrers:",f);let $={success:!0,data:{totalViews:g,uniqueVisitors:d,viewsByType:m,topPages:y,trafficOverTime:h,topReferrers:f}};return console.log("Analytics response data:",JSON.stringify($)),i.Z.json($)}catch(e){return console.error("Error fetching analytics:",e),i.Z.json({success:!1,message:"Failed to fetch analytics"},{status:500})}}let h=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/analytics/route",pathname:"/api/analytics",filename:"route",bundlePath:"app/api/analytics/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\analytics\\route.js",nextConfigOutput:"",userland:a}),{requestAsyncStorage:f,staticGenerationAsyncStorage:$,serverHooks:x,headerHooks:w,staticGenerationBailout:S}=h,v="/api/analytics/route";function b(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:$})}},91887:(e,t,r)=>{r.d(t,{n:()=>o});var a=r(11185),s=r.n(a);let o=async()=>{try{if(s().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await s().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},2299:(e,t,r)=>{r.d(t,{Z:()=>n});var a=r(11185),s=r.n(a);let o=new(s()).Schema({path:{type:String,required:!0,index:!0},contentType:{type:String,required:!0,enum:["blog","page","home","other"],default:"other"},blogId:{type:s().Schema.Types.ObjectId,ref:"blogs",index:!0},userId:{type:s().Schema.Types.ObjectId,ref:"users",index:!0},ipHash:{type:String,index:!0},userAgent:String,referrer:String,timestamp:{type:Date,default:Date.now,index:!0}});o.index({path:1,timestamp:1}),o.index({contentType:1,timestamp:1}),o.index({blogId:1,timestamp:1});let n=s().models.analytics||s().model("analytics",o)},11497:(e,t,r)=>{r.d(t,{V:()=>n,W:()=>i});var a=r(46082),s=r.n(a);let o=process.env.JWT_SECRET||"your-secret-key-here",n=e=>s().sign(e,o,{expiresIn:"7d"}),i=e=>{try{return s().verify(e,o)}catch(e){return console.error("Token verification error:",e.message),null}}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,2993,185],()=>r(41236));module.exports=a})();
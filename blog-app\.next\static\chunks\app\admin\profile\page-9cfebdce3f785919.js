(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[374],{833:function(e,a,s){Promise.resolve().then(s.bind(s,4934))},4934:function(e,a,s){"use strict";s.r(a);var r=s(7437),t=s(2173),l=s(6691),o=s.n(l),d=s(4033),n=s(2265),i=s(7948),c=s(5535);a.default=()=>{let e=(0,d.useRouter)(),[a,s]=(0,n.useState)(!0),[l,u]=(0,n.useState)({id:"",email:"",name:"",profilePicture:"/default_profile.png"}),[m,p]=(0,n.useState)(null),[h,b]=(0,n.useState)(null),[x,g]=(0,n.useState)(!1),[w,f]=(0,n.useState)(!1),[y,j]=(0,n.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[N,v]=(0,n.useState)(!1),[P,C]=(0,n.useState)(null),[k,S]=(0,n.useState)({x:0,y:0}),[I,F]=(0,n.useState)(1),[E,L]=(0,n.useState)(null);(0,n.useEffect)(()=>{(async()=>{try{let a=localStorage.getItem("userId");if(!a){e.push("/");return}let s=await t.Z.get("/api/profile",{params:{userId:a}});s.data.success?u(s.data.user):i.toast.error("Failed to load profile data")}catch(e){console.error("Profile fetch error:",e),i.toast.error("Failed to load profile data")}finally{s(!1)}})()},[e]);let _=async(e,a)=>{let s=new(o());s.src=e;let r=document.createElement("canvas"),t=r.getContext("2d");return r.width=a.width,r.height=a.height,t.drawImage(s,a.x,a.y,a.width,a.height,0,0,a.width,a.height),new Promise(e=>{r.toBlob(a=>{e(a)},"image/jpeg")})},R=async()=>{try{if(!E)return;let e=await _(P,E),a=URL.createObjectURL(e);b(a);let s=new File([e],"cropped_profile.jpg",{type:"image/jpeg"});p(s),v(!1)}catch(e){console.error("Error applying crop:",e),i.toast.error("Failed to crop image")}},Z=async e=>{e.preventDefault();try{let e=new FormData;e.append("userId",l.id),e.append("name",l.name),m&&e.append("profilePicture",m);let a=await t.Z.put("/api/profile",e);if(a.data.success){localStorage.setItem("userName",a.data.user.name),localStorage.setItem("userProfilePicture",a.data.user.profilePicture);let e=new CustomEvent("profileUpdate",{detail:{name:a.data.user.name,profilePicture:a.data.user.profilePicture}});window.dispatchEvent(e),i.toast.success("Profile updated successfully"),u({...l,name:a.data.user.name,profilePicture:a.data.user.profilePicture}),p(null),b(null)}else i.toast.error(a.data.message||"Failed to update profile")}catch(e){console.error("Profile update error:",e),i.toast.error("Failed to update profile")}},A=e=>{j({...y,[e.target.name]:e.target.value})},D=async e=>{if(e.preventDefault(),y.newPassword!==y.confirmPassword){i.toast.error("New passwords do not match");return}try{let e=await t.Z.put("/api/password",{userId:l.id,currentPassword:y.currentPassword,newPassword:y.newPassword});e.data.success?(i.toast.success("Password updated successfully"),j({currentPassword:"",newPassword:"",confirmPassword:""}),f(!1)):i.toast.error(e.data.message||"Failed to update password")}catch(e){var a,s;console.error("Password update error:",e),i.toast.error((null===(s=e.response)||void 0===s?void 0:null===(a=s.data)||void 0===a?void 0:a.message)||"Failed to update password")}};return a?(0,r.jsx)("div",{className:"pt-5 px-5 sm:pt-12 sm:pl-16",children:(0,r.jsx)("p",{children:"Loading profile data..."})}):(0,r.jsxs)("div",{className:"pt-5 px-5 sm:pt-12 sm:pl-16",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Admin Profile"}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,r.jsxs)("form",{onSubmit:Z,className:"max-w-2xl",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{className:"block text-gray-700 mb-2",children:"Profile Picture"}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(o(),{src:h||l.profilePicture,width:100,height:100,alt:"Profile",className:"rounded-full object-cover w-24 h-24 border-2 border-gray-300"}),(0,r.jsxs)("label",{className:"cursor-pointer bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-md border border-gray-300",children:["Change Picture",(0,r.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{let a=e.target.files[0];if(a){let e=new FileReader;e.onloadend=()=>{C(e.result),v(!0)},e.readAsDataURL(a)}},className:"hidden"})]})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-gray-700 mb-2",children:"Email"}),(0,r.jsx)("input",{type:"email",value:l.email,disabled:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-md bg-gray-50"})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-gray-700 mb-2",children:"Display Name"}),(0,r.jsx)("input",{type:"text",value:l.name,onChange:e=>{u({...l,name:e.target.value})},className:"w-full px-4 py-3 border border-gray-300 rounded-md",placeholder:"Enter your name"})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("button",{type:"submit",className:"mt-4 w-40 h-12 bg-black text-white",children:"Save Changes"}),(0,r.jsx)("button",{type:"button",onClick:()=>e.push("/admin"),className:"mt-4 w-40 h-12 border border-black",children:"Cancel"}),(0,r.jsx)("button",{type:"button",onClick:()=>{g(!0)},className:"mt-4 w-40 h-12 bg-red-600 text-white",children:"Logout"})]})]}),(0,r.jsxs)("div",{className:"mt-12 border-t pt-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Change Password"}),(0,r.jsx)("button",{type:"button",onClick:()=>f(!w),className:"mb-4 px-4 py-2 border border-gray-300 rounded-md",children:w?"Cancel":"Change Password"}),w&&(0,r.jsxs)("form",{onSubmit:D,className:"max-w-2xl",children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-gray-700 mb-2",children:"Current Password"}),(0,r.jsx)("input",{type:"password",name:"currentPassword",value:y.currentPassword,onChange:A,className:"w-full px-4 py-3 border border-gray-300 rounded-md",required:!0})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-gray-700 mb-2",children:"New Password"}),(0,r.jsx)("input",{type:"password",name:"newPassword",value:y.newPassword,onChange:A,className:"w-full px-4 py-3 border border-gray-300 rounded-md",required:!0})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-gray-700 mb-2",children:"Confirm New Password"}),(0,r.jsx)("input",{type:"password",name:"confirmPassword",value:y.confirmPassword,onChange:A,className:"w-full px-4 py-3 border border-gray-300 rounded-md",required:!0})]}),(0,r.jsx)("button",{type:"submit",className:"mt-4 w-40 h-12 bg-black text-white",children:"Update Password"})]})]})]}),x&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-6 rounded-md shadow-lg max-w-sm w-full",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Confirm Logout"}),(0,r.jsx)("p",{className:"mb-6",children:"Are you sure you want to log out? You will need to log in again to access the admin panel."}),(0,r.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,r.jsx)("button",{onClick:()=>{g(!1)},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"}),(0,r.jsx)("button",{onClick:()=>{localStorage.removeItem("authToken"),localStorage.removeItem("userRole"),localStorage.removeItem("userId"),localStorage.removeItem("userName"),localStorage.removeItem("userProfilePicture"),i.toast.success("Logged out successfully"),setTimeout(()=>{e.push("/")},300)},className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",children:"Logout"})]})]})}),N&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg max-w-2xl w-full",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Adjust Profile Picture"}),(0,r.jsx)("div",{className:"relative h-80 mb-4",children:(0,r.jsx)(c.ZP,{image:P,crop:k,zoom:I,aspect:1,cropShape:"round",onCropChange:S,onCropComplete:(e,a)=>{L(a)},onZoomChange:F})}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Zoom: ",I.toFixed(1),"x"]}),(0,r.jsx)("input",{type:"range",min:1,max:3,step:.1,value:I,onChange:e=>F(parseFloat(e.target.value)),className:"w-full"})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{type:"button",onClick:R,className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",children:"Apply"}),(0,r.jsx)("button",{type:"button",onClick:()=>{v(!1),C(null)},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"})]})]})})]})}}},function(e){e.O(0,[580,691,442,971,938,744],function(){return e(e.s=833)}),_N_E=e.O()}]);
"use strict";(()=>{var e={};e.id=5569,e.ids=[5569],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},61960:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>f,originalPathname:()=>h,patchFetch:()=>S,requestAsyncStorage:()=>p,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>y});var s={};t.r(s),t.d(s,{POST:()=>c});var o=t(95419),a=t(69108),n=t(99678),i=t(91887),u=t(96488),l=t(78070);async function c(e){try{let{email:r,password:t,role:s}=await e.json();if(await u.Z.findOne({email:r}))return l.Z.json({success:!1,message:"User with this email already exists"},{status:400});let o=await u.Z.create({email:r,password:t,role:s||"user"});return l.Z.json({success:!0,message:"Registration successful",user:{id:o._id,email:o.email,role:o.role}})}catch(e){return console.error("Registration error:",e),l.Z.json({success:!1,message:"Server error"},{status:500})}}(async()=>{await (0,i.n)()})();let d=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/register/route",pathname:"/api/register",filename:"route",bundlePath:"app/api/register/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\register\\route.js",nextConfigOutput:"",userland:s}),{requestAsyncStorage:p,staticGenerationAsyncStorage:g,serverHooks:m,headerHooks:f,staticGenerationBailout:y}=d,h="/api/register/route";function S(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:g})}},91887:(e,r,t)=>{t.d(r,{n:()=>a});var s=t(11185),o=t.n(s);let a=async()=>{try{if(o().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await o().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},96488:(e,r,t)=>{t.d(r,{Z:()=>n});var s=t(11185),o=t.n(s);let a=new(o()).Schema({email:{type:String,required:!0,unique:!0},password:{type:String,required:!0},role:{type:String,default:"user",enum:["user","admin"]},profilePicture:{type:String,default:"/default_profile.png"},name:{type:String,default:""},date:{type:Date,default:Date.now()}}),n=o().models.user||o().model("user",a)},78070:(e,r,t)=>{Object.defineProperty(r,"Z",{enumerable:!0,get:function(){return s.NextResponse}});let s=t(70457)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,2993],()=>t(61960));module.exports=s})();
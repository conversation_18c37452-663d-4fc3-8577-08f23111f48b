"use strict";(()=>{var e={};e.id=3207,e.ids=[3207],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},12781:e=>{e.exports=require("stream")},73837:e=>{e.exports=require("util")},6192:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>f,originalPathname:()=>S,patchFetch:()=>v,requestAsyncStorage:()=>y,routeModule:()=>m,serverHooks:()=>b,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>x});var a={};r.r(a),r.d(a,{GET:()=>g});var o=r(95419),n=r(69108),s=r(99678),i=r(78070),u=r(91887),l=r(2299),c=r(46082),d=r.n(c);let p=e=>{try{let t=e.headers.get("authorization");if(!t||!t.startsWith("Bearer "))return null;let r=t.split(" ")[1],a=d().verify(r,process.env.JWT_SECRET||"your-secret-key");return{userId:a.userId,email:a.email,role:a.role}}catch(e){return console.error("Error verifying token:",e),null}};async function g(e){try{if(await (0,u.n)(),!p(e))return i.Z.json({success:!1,message:"Authentication required"},{status:401});let{searchParams:t}=new URL(e.url),r=t.get("id");if(!r)return i.Z.json({success:!1,message:"Blog ID is required"},{status:400});let a=await l.Z.countDocuments({blogId:r}),o=await l.Z.distinct("ipHash",{blogId:r}).then(e=>e.length),n=await l.Z.aggregate([{$match:{blogId:r}},{$group:{_id:{$dateToString:{format:"%Y-%m-%d",date:"$timestamp"}},count:{$sum:1}}},{$sort:{_id:1}}]);return i.Z.json({success:!0,analytics:{totalViews:a,uniqueVisitors:o,viewsOverTime:n}})}catch(e){return console.error("Error fetching blog analytics:",e),i.Z.json({success:!1,message:"Failed to fetch blog analytics"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/blog/analytics/route",pathname:"/api/blog/analytics",filename:"route",bundlePath:"app/api/blog/analytics/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\blog\\analytics\\route.js",nextConfigOutput:"",userland:a}),{requestAsyncStorage:y,staticGenerationAsyncStorage:h,serverHooks:b,headerHooks:f,staticGenerationBailout:x}=m,S="/api/blog/analytics/route";function v(){return(0,s.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:h})}},91887:(e,t,r)=>{r.d(t,{n:()=>n});var a=r(11185),o=r.n(a);let n=async()=>{try{if(o().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await o().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},2299:(e,t,r)=>{r.d(t,{Z:()=>s});var a=r(11185),o=r.n(a);let n=new(o()).Schema({path:{type:String,required:!0,index:!0},contentType:{type:String,required:!0,enum:["blog","page","home","other"],default:"other"},blogId:{type:o().Schema.Types.ObjectId,ref:"blogs",index:!0},userId:{type:o().Schema.Types.ObjectId,ref:"users",index:!0},ipHash:{type:String,index:!0},userAgent:String,referrer:String,timestamp:{type:Date,default:Date.now,index:!0}});n.index({path:1,timestamp:1}),n.index({contentType:1,timestamp:1}),n.index({blogId:1,timestamp:1});let s=o().models.analytics||o().model("analytics",n)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,2993,185],()=>r(6192));module.exports=a})();
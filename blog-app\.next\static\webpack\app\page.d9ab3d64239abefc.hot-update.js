"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./Components/PaperRocketAnimation.jsx":
/*!*********************************************!*\
  !*** ./Components/PaperRocketAnimation.jsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst StickManAnimation = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-6c62068782816f71\" + \" \" + \"fixed inset-0 pointer-events-none z-10 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-6c62068782816f71\" + \" \" + \"stickman-container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    width: \"50\",\n                    height: \"60\",\n                    viewBox: \"0 0 100 120\",\n                    fill: \"none\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"jsx-6c62068782816f71\" + \" \" + \"stickman\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"50\",\n                            cy: \"15\",\n                            r: \"8\",\n                            fill: \"none\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"3\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-6c62068782816f71\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"50\",\n                            y1: \"23\",\n                            x2: \"50\",\n                            y2: \"70\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"3\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-6c62068782816f71\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"50\",\n                            y1: \"35\",\n                            x2: \"30\",\n                            y2: \"50\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"3\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-6c62068782816f71\" + \" \" + \"left-arm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"50\",\n                            y1: \"35\",\n                            x2: \"70\",\n                            y2: \"50\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"3\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-6c62068782816f71\" + \" \" + \"right-arm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"50\",\n                            y1: \"70\",\n                            x2: \"35\",\n                            y2: \"95\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"3\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-6c62068782816f71\" + \" \" + \"left-leg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"50\",\n                            y1: \"70\",\n                            x2: \"65\",\n                            y2: \"95\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"3\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-6c62068782816f71\" + \" \" + \"right-leg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"46\",\n                            cy: \"12\",\n                            r: \"1.5\",\n                            fill: \"#000000\",\n                            className: \"jsx-6c62068782816f71\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"54\",\n                            cy: \"12\",\n                            r: \"1.5\",\n                            fill: \"#000000\",\n                            className: \"jsx-6c62068782816f71\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M 45 17 Q 50 20 55 17\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"1.5\",\n                            fill: \"none\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-6c62068782816f71\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"6c62068782816f71\",\n                children: \".stickman-container.jsx-6c62068782816f71{position:absolute;-webkit-animation:stickmanWalk 20s linear infinite;-moz-animation:stickmanWalk 20s linear infinite;-o-animation:stickmanWalk 20s linear infinite;animation:stickmanWalk 20s linear infinite}.stickman.jsx-6c62068782816f71{-webkit-transform-origin:center;-moz-transform-origin:center;-ms-transform-origin:center;-o-transform-origin:center;transform-origin:center;-webkit-filter:drop-shadow(2px 2px 4px rgba(0,0,0,.3));filter:drop-shadow(2px 2px 4px rgba(0,0,0,.3))}.left-arm.jsx-6c62068782816f71{-webkit-animation:leftArmSwing.8s ease-in-out infinite;-moz-animation:leftArmSwing.8s ease-in-out infinite;-o-animation:leftArmSwing.8s ease-in-out infinite;animation:leftArmSwing.8s ease-in-out infinite;-webkit-transform-origin:50px 35px;-moz-transform-origin:50px 35px;-ms-transform-origin:50px 35px;-o-transform-origin:50px 35px;transform-origin:50px 35px}.right-arm.jsx-6c62068782816f71{-webkit-animation:rightArmSwing.8s ease-in-out infinite;-moz-animation:rightArmSwing.8s ease-in-out infinite;-o-animation:rightArmSwing.8s ease-in-out infinite;animation:rightArmSwing.8s ease-in-out infinite;-webkit-transform-origin:50px 35px;-moz-transform-origin:50px 35px;-ms-transform-origin:50px 35px;-o-transform-origin:50px 35px;transform-origin:50px 35px}.left-leg.jsx-6c62068782816f71{-webkit-animation:leftLegWalk.8s ease-in-out infinite;-moz-animation:leftLegWalk.8s ease-in-out infinite;-o-animation:leftLegWalk.8s ease-in-out infinite;animation:leftLegWalk.8s ease-in-out infinite;-webkit-transform-origin:50px 70px;-moz-transform-origin:50px 70px;-ms-transform-origin:50px 70px;-o-transform-origin:50px 70px;transform-origin:50px 70px}.right-leg.jsx-6c62068782816f71{-webkit-animation:rightLegWalk.8s ease-in-out infinite;-moz-animation:rightLegWalk.8s ease-in-out infinite;-o-animation:rightLegWalk.8s ease-in-out infinite;animation:rightLegWalk.8s ease-in-out infinite;-webkit-transform-origin:50px 70px;-moz-transform-origin:50px 70px;-ms-transform-origin:50px 70px;-o-transform-origin:50px 70px;transform-origin:50px 70px}@-webkit-keyframes stickmanWalk{0%{top:80%;left:-70px;-webkit-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}25%{top:20%;left:25%;-webkit-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}50%{top:20%;left:-webkit-calc(100% + 70px);left:calc(100% + 70px);-webkit-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}75%{top:80%;left:75%;-webkit-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}100%{top:80%;left:-70px;-webkit-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}}@-moz-keyframes stickmanWalk{0%{top:80%;left:-70px;-moz-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}25%{top:20%;left:25%;-moz-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}50%{top:20%;left:-moz-calc(100% + 70px);left:calc(100% + 70px);-moz-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}75%{top:80%;left:75%;-moz-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}100%{top:80%;left:-70px;-moz-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}}@-o-keyframes stickmanWalk{0%{top:80%;left:-70px;-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}25%{top:20%;left:25%;-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}50%{top:20%;left:calc(100% + 70px);-o-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}75%{top:80%;left:75%;-o-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}100%{top:80%;left:-70px;-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}}@keyframes stickmanWalk{0%{top:80%;left:-70px;-webkit-transform:translatey(-50%)scalex(1);-moz-transform:translatey(-50%)scalex(1);-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}25%{top:20%;left:25%;-webkit-transform:translatey(-50%)scalex(1);-moz-transform:translatey(-50%)scalex(1);-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}50%{top:20%;left:-webkit-calc(100% + 70px);left:-moz-calc(100% + 70px);left:calc(100% + 70px);-webkit-transform:translatey(-50%)scalex(-1);-moz-transform:translatey(-50%)scalex(-1);-o-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}75%{top:80%;left:75%;-webkit-transform:translatey(-50%)scalex(-1);-moz-transform:translatey(-50%)scalex(-1);-o-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}100%{top:80%;left:-70px;-webkit-transform:translatey(-50%)scalex(1);-moz-transform:translatey(-50%)scalex(1);-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}}@-webkit-keyframes leftArmSwing{0%,100%{-webkit-transform:rotate(-20deg);transform:rotate(-20deg)}50%{-webkit-transform:rotate(20deg);transform:rotate(20deg)}}@-moz-keyframes leftArmSwing{0%,100%{-moz-transform:rotate(-20deg);transform:rotate(-20deg)}50%{-moz-transform:rotate(20deg);transform:rotate(20deg)}}@-o-keyframes leftArmSwing{0%,100%{-o-transform:rotate(-20deg);transform:rotate(-20deg)}50%{-o-transform:rotate(20deg);transform:rotate(20deg)}}@keyframes leftArmSwing{0%,100%{-webkit-transform:rotate(-20deg);-moz-transform:rotate(-20deg);-o-transform:rotate(-20deg);transform:rotate(-20deg)}50%{-webkit-transform:rotate(20deg);-moz-transform:rotate(20deg);-o-transform:rotate(20deg);transform:rotate(20deg)}}@-webkit-keyframes rightArmSwing{0%,100%{-webkit-transform:rotate(20deg);transform:rotate(20deg)}50%{-webkit-transform:rotate(-20deg);transform:rotate(-20deg)}}@-moz-keyframes rightArmSwing{0%,100%{-moz-transform:rotate(20deg);transform:rotate(20deg)}50%{-moz-transform:rotate(-20deg);transform:rotate(-20deg)}}@-o-keyframes rightArmSwing{0%,100%{-o-transform:rotate(20deg);transform:rotate(20deg)}50%{-o-transform:rotate(-20deg);transform:rotate(-20deg)}}@keyframes rightArmSwing{0%,100%{-webkit-transform:rotate(20deg);-moz-transform:rotate(20deg);-o-transform:rotate(20deg);transform:rotate(20deg)}50%{-webkit-transform:rotate(-20deg);-moz-transform:rotate(-20deg);-o-transform:rotate(-20deg);transform:rotate(-20deg)}}@-webkit-keyframes leftLegWalk{0%,100%{-webkit-transform:rotate(-15deg);transform:rotate(-15deg)}50%{-webkit-transform:rotate(15deg);transform:rotate(15deg)}}@-moz-keyframes leftLegWalk{0%,100%{-moz-transform:rotate(-15deg);transform:rotate(-15deg)}50%{-moz-transform:rotate(15deg);transform:rotate(15deg)}}@-o-keyframes leftLegWalk{0%,100%{-o-transform:rotate(-15deg);transform:rotate(-15deg)}50%{-o-transform:rotate(15deg);transform:rotate(15deg)}}@keyframes leftLegWalk{0%,100%{-webkit-transform:rotate(-15deg);-moz-transform:rotate(-15deg);-o-transform:rotate(-15deg);transform:rotate(-15deg)}50%{-webkit-transform:rotate(15deg);-moz-transform:rotate(15deg);-o-transform:rotate(15deg);transform:rotate(15deg)}}@-webkit-keyframes rightLegWalk{0%,100%{-webkit-transform:rotate(15deg);transform:rotate(15deg)}50%{-webkit-transform:rotate(-15deg);transform:rotate(-15deg)}}@-moz-keyframes rightLegWalk{0%,100%{-moz-transform:rotate(15deg);transform:rotate(15deg)}50%{-moz-transform:rotate(-15deg);transform:rotate(-15deg)}}@-o-keyframes rightLegWalk{0%,100%{-o-transform:rotate(15deg);transform:rotate(15deg)}50%{-o-transform:rotate(-15deg);transform:rotate(-15deg)}}@keyframes rightLegWalk{0%,100%{-webkit-transform:rotate(15deg);-moz-transform:rotate(15deg);-o-transform:rotate(15deg);transform:rotate(15deg)}50%{-webkit-transform:rotate(-15deg);-moz-transform:rotate(-15deg);-o-transform:rotate(-15deg);transform:rotate(-15deg)}}@media(max-width:768px){.stickman.jsx-6c62068782816f71{width:40px;height:48px}@-webkit-keyframes stickmanWalk{0%{top:70%;left:-60px;-webkit-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}25%{top:30%;left:30%;-webkit-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}50%{top:30%;left:-webkit-calc(100% + 60px);left:calc(100% + 60px);-webkit-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}75%{top:70%;left:70%;-webkit-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}100%{top:70%;left:-60px;-webkit-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}}@-moz-keyframes stickmanWalk{0%{top:70%;left:-60px;-moz-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}25%{top:30%;left:30%;-moz-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}50%{top:30%;left:-moz-calc(100% + 60px);left:calc(100% + 60px);-moz-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}75%{top:70%;left:70%;-moz-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}100%{top:70%;left:-60px;-moz-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}}@-o-keyframes stickmanWalk{0%{top:70%;left:-60px;-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}25%{top:30%;left:30%;-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}50%{top:30%;left:calc(100% + 60px);-o-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}75%{top:70%;left:70%;-o-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}100%{top:70%;left:-60px;-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}}@keyframes stickmanWalk{0%{top:70%;left:-60px;-webkit-transform:translatey(-50%)scalex(1);-moz-transform:translatey(-50%)scalex(1);-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}25%{top:30%;left:30%;-webkit-transform:translatey(-50%)scalex(1);-moz-transform:translatey(-50%)scalex(1);-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}50%{top:30%;left:-webkit-calc(100% + 60px);left:-moz-calc(100% + 60px);left:calc(100% + 60px);-webkit-transform:translatey(-50%)scalex(-1);-moz-transform:translatey(-50%)scalex(-1);-o-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}75%{top:70%;left:70%;-webkit-transform:translatey(-50%)scalex(-1);-moz-transform:translatey(-50%)scalex(-1);-o-transform:translatey(-50%)scalex(-1);transform:translatey(-50%)scalex(-1)}100%{top:70%;left:-60px;-webkit-transform:translatey(-50%)scalex(1);-moz-transform:translatey(-50%)scalex(1);-o-transform:translatey(-50%)scalex(1);transform:translatey(-50%)scalex(1)}}}.stickman-container.jsx-6c62068782816f71:hover{-webkit-animation-play-state:paused;-moz-animation-play-state:paused;-o-animation-play-state:paused;animation-play-state:paused}.stickman-container.jsx-6c62068782816f71:hover .left-arm.jsx-6c62068782816f71,.stickman-container.jsx-6c62068782816f71:hover .right-arm.jsx-6c62068782816f71,.stickman-container.jsx-6c62068782816f71:hover .left-leg.jsx-6c62068782816f71,.stickman-container.jsx-6c62068782816f71:hover .right-leg.jsx-6c62068782816f71{-webkit-animation-play-state:paused;-moz-animation-play-state:paused;-o-animation-play-state:paused;animation-play-state:paused}.stickman.jsx-6c62068782816f71 circle.jsx-6c62068782816f71:first-child{-webkit-animation:headBob.4s ease-in-out infinite;-moz-animation:headBob.4s ease-in-out infinite;-o-animation:headBob.4s ease-in-out infinite;animation:headBob.4s ease-in-out infinite;-webkit-transform-origin:center;-moz-transform-origin:center;-ms-transform-origin:center;-o-transform-origin:center;transform-origin:center}@-webkit-keyframes headBob{0%,100%{-webkit-transform:translatey(0px);transform:translatey(0px)}50%{-webkit-transform:translatey(-2px);transform:translatey(-2px)}}@-moz-keyframes headBob{0%,100%{-moz-transform:translatey(0px);transform:translatey(0px)}50%{-moz-transform:translatey(-2px);transform:translatey(-2px)}}@-o-keyframes headBob{0%,100%{-o-transform:translatey(0px);transform:translatey(0px)}50%{-o-transform:translatey(-2px);transform:translatey(-2px)}}@keyframes headBob{0%,100%{-webkit-transform:translatey(0px);-moz-transform:translatey(0px);-o-transform:translatey(0px);transform:translatey(0px)}50%{-webkit-transform:translatey(-2px);-moz-transform:translatey(-2px);-o-transform:translatey(-2px);transform:translatey(-2px)}}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n_c = StickManAnimation;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PaperRocketAnimation);\nvar _c;\n$RefreshReg$(_c, \"StickManAnimation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./Components/PaperRocketAnimation.jsx\n"));

/***/ })

});
(()=>{var e={};e.id=6140,e.ids=[6140],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},97790:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>n});var s=a(50482),r=a(69108),i=a(62563),o=a.n(i),l=a(68300),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);a.d(t,d);let n=["",{children:["admin",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,51444)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\settings\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,32143)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,39459)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\settings\\page.jsx"],m="/admin/settings/page",u={require:a,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/settings/page",pathname:"/admin/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},89081:(e,t,a)=>{Promise.resolve().then(a.bind(a,15736))},15736:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var s=a(95344),r=a(53608),i=a(3729),o=a(69697),l=a(22254),d=a(20783),n=a.n(d),c=a(79338);let m=()=>{(0,l.useRouter)();let[e,t]=(0,i.useState)([]),[a,d]=(0,i.useState)([]),[m,u]=(0,i.useState)(""),[x,p]=(0,i.useState)({name:"",image:null,bio:""}),[h,g]=(0,i.useState)(!1),[b,y]=(0,i.useState)("categories"),[j,f]=(0,i.useState)(null),[v,N]=(0,i.useState)(!1),[w,C]=(0,i.useState)(null),[k,S]=(0,i.useState)({x:0,y:0}),[q,_]=(0,i.useState)(1),[A,P]=(0,i.useState)(null),[E,D]=(0,i.useState)(null);(0,i.useEffect)(()=>{F(),M()},[]);let F=async()=>{try{let e=await r.Z.get("/api/categories");e.data.success&&t(e.data.categories)}catch(e){console.error("Error fetching categories:",e),o.toast.error("Failed to load categories")}},M=async()=>{try{let e=await r.Z.get("/api/authors");e.data.success&&d(e.data.authors)}catch(e){console.error("Error fetching authors:",e),o.toast.error("Failed to load authors")}},B=async e=>{if(e.preventDefault(),m.trim())try{g(!0);let e=await r.Z.post("/api/categories",{name:m});e.data.success?(o.toast.success("Category added successfully"),u(""),e.data.categories?t(e.data.categories):await F()):o.toast.error(e.data.message||"Failed to add category")}catch(e){console.error("Error adding category:",e),e.response&&409===e.response.status?o.toast.error("Category already exists"):o.toast.error(e.response?.data?.message||"Failed to add category")}finally{g(!1)}},Z=async a=>{try{let s=await r.Z.delete(`/api/categories?id=${a}`);s.data.success?(o.toast.success("Category deleted successfully"),t(e.filter(e=>e._id!==a))):o.toast.error(s.data.message||"Failed to delete category")}catch(e){console.error("Error deleting category:",e),o.toast.error(e.response?.data?.message||"Failed to delete category")}},U=e=>{let{name:t,value:a}=e.target;p({...x,[t]:a})},$=(0,i.useCallback)((e,t)=>{P(t)},[]),G=e=>new Promise((t,a)=>{let s=new Image;s.addEventListener("load",()=>t(s)),s.addEventListener("error",e=>a(e)),s.src=e}),I=async(e,t)=>{let a=await G(e),s=document.createElement("canvas"),r=s.getContext("2d"),i=Math.max(a.width,a.height)/2*Math.sqrt(2)*2;s.width=i,s.height=i,r.drawImage(a,i/2-.5*a.width,i/2-.5*a.height);let o=r.getImageData(0,0,i,i);return s.width=t.width,s.height=t.height,r.putImageData(o,Math.round(0-i/2+.5*a.width-t.x),Math.round(0-i/2+.5*a.height-t.y)),new Promise(e=>{s.toBlob(t=>{e(t)},"image/jpeg")})},R=async()=>{try{if(!A)return;let e=await I(w,A),t=URL.createObjectURL(e);D(e),f(t),N(!1)}catch(e){console.error("Error applying crop:",e),o.toast.error("Failed to crop image")}},L=async e=>{if(e.preventDefault(),!x.name.trim()){o.toast.error("Author name is required");return}try{g(!0);let e=new FormData;if(e.append("name",x.name),e.append("bio",x.bio),E){let t=new File([E],"cropped_image.jpg",{type:"image/jpeg"});e.append("image",t)}let t=await r.Z.post("/api/authors",e);t.data.success?(o.toast.success("Author added successfully"),p({name:"",image:null,bio:""}),f(null),D(null),await M()):o.toast.error(t.data.message||"Failed to add author")}catch(e){console.error("Error adding author:",e),o.toast.error(e.response?.data?.message||"Failed to add author")}finally{g(!1)}},Y=async e=>{try{let t=await r.Z.delete(`/api/authors?id=${e}`);t.data.success?(o.toast.success("Author deleted successfully"),d(a.filter(t=>t._id!==e))):o.toast.error(t.data.message||"Failed to delete author")}catch(e){console.error("Error deleting author:",e),o.toast.error(e.response?.data?.message||"Failed to delete author")}};return(0,s.jsxs)("div",{className:"pt-5 px-5 sm:pt-12 sm:pl-16",children:[s.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Settings"}),(0,s.jsxs)("div",{className:"flex border-b mb-6",children:[s.jsx("button",{className:`py-2 px-4 ${"categories"===b?"border-b-2 border-black font-medium":"text-gray-500"}`,onClick:()=>y("categories"),children:"Categories"}),s.jsx("button",{className:`py-2 px-4 ${"authors"===b?"border-b-2 border-black font-medium":"text-gray-500"}`,onClick:()=>y("authors"),children:"Authors"})]}),"categories"===b&&s.jsx("div",{className:"max-w-3xl",children:(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md mb-8",children:[s.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Manage Blog Categories"}),s.jsx("form",{onSubmit:B,className:"mb-6",children:(0,s.jsxs)("div",{className:"flex gap-3",children:[s.jsx("input",{type:"text",value:m,onChange:e=>u(e.target.value),placeholder:"Enter new category name",className:"flex-1 px-4 py-2 border border-gray-300 rounded-md",required:!0}),s.jsx("button",{type:"submit",className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",disabled:h,children:"Add Category"})]})}),s.jsx("div",{className:"border rounded-md overflow-hidden",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[s.jsx("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category Name"}),s.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsxs)("tbody",{className:"bg-white divide-y divide-gray-200",children:[e.map(e=>(0,s.jsxs)("tr",{children:[s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.name})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:s.jsx("button",{onClick:()=>Z(e._id),className:"text-red-600 hover:text-red-900",children:"Delete"})})]},e._id)),0===e.length&&s.jsx("tr",{children:s.jsx("td",{colSpan:"2",className:"px-6 py-4 text-center text-sm text-gray-500",children:"No categories found"})})]})]})})]})}),"authors"===b&&s.jsx("div",{className:"max-w-3xl",children:(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md mb-8",children:[s.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Manage Blog Authors"}),v?(0,s.jsxs)("div",{className:"mb-6 max-w-2xl",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Adjust Profile Image"}),s.jsx("div",{className:"relative h-80 mb-4",children:s.jsx(c.ZP,{image:w,crop:k,zoom:q,aspect:1,cropShape:"round",onCropChange:S,onCropComplete:$,onZoomChange:_})}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Zoom: ",q.toFixed(1),"x"]}),s.jsx("input",{type:"range",min:1,max:3,step:.1,value:q,onChange:e=>_(parseFloat(e.target.value)),className:"w-full"})]}),(0,s.jsxs)("div",{className:"flex gap-3",children:[s.jsx("button",{type:"button",onClick:R,className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",children:"Apply"}),s.jsx("button",{type:"button",onClick:()=>{N(!1),C(null)},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"})]})]}):(0,s.jsxs)("form",{onSubmit:L,className:"mb-6",children:[(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Author Name"}),s.jsx("input",{type:"text",name:"name",value:x.name,onChange:U,placeholder:"Enter author name",className:"w-full px-4 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Author Bio"}),s.jsx("textarea",{name:"bio",value:x.bio,onChange:U,placeholder:"Enter author bio",className:"w-full px-4 py-2 border border-gray-300 rounded-md",rows:3})]}),(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Author Image"}),(0,s.jsxs)("div",{className:"flex items-center",children:[j&&s.jsx("div",{className:"mr-4",children:s.jsx("img",{src:j,alt:"Author preview",className:"w-16 h-16 object-cover rounded-full border-2 border-gray-200"})}),s.jsx("input",{type:"file",accept:"image/*",onChange:e=>{let t=e.target.files[0];if(t){let e=new FileReader;e.onload=()=>{C(e.result),N(!0)},e.readAsDataURL(t)}},className:"w-full px-4 py-2 border border-gray-300 rounded-md"})]})]}),s.jsx("button",{type:"submit",className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",disabled:h,children:"Add Author"})]}),s.jsx("div",{className:"border rounded-md overflow-hidden",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[s.jsx("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Author"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Bio"}),s.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsxs)("tbody",{className:"bg-white divide-y divide-gray-200",children:[a.map(e=>(0,s.jsxs)("tr",{children:[s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:s.jsx("img",{className:"h-10 w-10 rounded-full object-cover",src:e.image||"/author_img.png",alt:e.name})}),s.jsx("div",{className:"ml-4",children:s.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.name})})]})}),s.jsx("td",{className:"px-6 py-4",children:s.jsx("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:e.bio||"No bio available"})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,s.jsxs)("div",{className:"flex gap-3 justify-end",children:[s.jsx(n(),{href:`/admin/editAuthor/${e._id}`,className:"cursor-pointer text-blue-600 hover:underline",children:"Edit"}),s.jsx("button",{onClick:()=>Y(e._id),className:"cursor-pointer text-red-600 hover:underline",children:"Delete"})]})})]},e._id)),0===a.length&&s.jsx("tr",{children:s.jsx("td",{colSpan:"3",className:"px-6 py-4 text-center text-sm text-gray-500",children:"No authors found"})})]})]})})]})})]})}},51444:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>i,__esModule:()=>r,default:()=>o});let s=(0,a(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\admin\settings\page.jsx`),{__esModule:r,$$typeof:i}=s,o=s.default}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[1638,3998,337,8468,9338,5757,7388],()=>a(97790));module.exports=s})();
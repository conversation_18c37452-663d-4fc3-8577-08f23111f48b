(()=>{var e={};e.id=7243,e.ids=[7243],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},22804:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(50482),a=s(69108),i=s(62563),l=s.n(i),n=s(68300),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let c=["",{children:["admin",{children:["reactions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8362)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\reactions\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,32143)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,39459)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\reactions\\page.jsx"],x="/admin/reactions/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/reactions/page",pathname:"/admin/reactions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},21886:(e,t,s)=>{Promise.resolve().then(s.bind(s,47280))},47280:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(95344),a=s(3729),i=s(53608),l=s(69697),n=s(20783),o=s.n(n);s(41223);let c=()=>{let[e,t]=(0,a.useState)([]),[s,n]=(0,a.useState)(!0),[c,d]=(0,a.useState)("most");(0,a.useEffect)(()=>{x()},[]);let x=async()=>{try{n(!0);let e=(await i.Z.get("/api/blog")).data.blogs||[],s=await Promise.all(e.map(async e=>{try{let t=await i.Z.get(`/api/blog/likes?id=${e._id}`);return{...e,likeCount:t.data.success?t.data.count:0}}catch(t){return console.error(`Error fetching likes for blog ${e._id}:`,t),{...e,likeCount:0}}}));t(s)}catch(e){console.error("Error fetching blogs with likes:",e),l.toast.error("Failed to load blogs with reactions")}finally{n(!1)}},p=e=>{d(e)},m=[...e].sort((e,t)=>"most"===c?t.likeCount-e.likeCount:e.likeCount-t.likeCount);return(0,r.jsxs)("div",{className:"flex-1 pt-5 px-5 sm:pt-12 sm:pl-16",children:[r.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Blog Reactions"}),r.jsx("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"flex space-x-4 mb-4",children:[r.jsx("button",{onClick:()=>p("most"),className:`px-4 py-2 rounded-md ${"most"===c?"bg-black text-white":"bg-gray-200 text-gray-800 hover:bg-gray-300"}`,children:"Most Liked"}),r.jsx("button",{onClick:()=>p("least"),className:`px-4 py-2 rounded-md ${"least"===c?"bg-black text-white":"bg-gray-200 text-gray-800 hover:bg-gray-300"}`,children:"Least Liked"})]})}),s?r.jsx("div",{className:"text-center py-8",children:r.jsx("p",{children:"Loading blog reactions..."})}):0===m.length?r.jsx("div",{className:"text-center py-8 bg-white rounded-lg shadow p-6",children:r.jsx("p",{className:"text-gray-500",children:"No blogs found."})}):r.jsx("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[r.jsx("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Blog"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Author"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Likes"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:m.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:r.jsx("img",{className:"h-10 w-10 rounded-md object-cover",src:e.image,alt:e.title})}),r.jsx("div",{className:"ml-4",children:r.jsx("div",{className:"text-sm font-medium text-gray-900 max-w-xs truncate",children:e.title})})]})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"flex-shrink-0 h-8 w-8",children:r.jsx("img",{className:"h-8 w-8 rounded-full object-cover",src:e.authorImg,alt:e.author})}),r.jsx("div",{className:"ml-2 text-sm text-gray-900",children:e.author})]})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800",children:e.category})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.date).toLocaleDateString()}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",className:"text-red-500 mr-2",children:r.jsx("path",{d:"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"})}),r.jsx("span",{className:"text-sm font-medium",children:e.likeCount})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[r.jsx(o(),{href:`/blogs/${e._id}`,className:"text-blue-600 hover:text-blue-900 mr-4",target:"_blank",children:"View"}),r.jsx(o(),{href:`/admin/editBlog/${e._id}`,className:"text-indigo-600 hover:text-indigo-900",children:"Edit"})]})]},e._id))})]})})]})}},8362:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>l});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\admin\reactions\page.jsx`),{__esModule:a,$$typeof:i}=r,l=r.default}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,3998,337,8468,5757,7388],()=>s(22804));module.exports=r})();
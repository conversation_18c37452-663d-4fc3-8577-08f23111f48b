(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[140],{9147:function(e,t,a){Promise.resolve().then(a.bind(a,2272))},2272:function(e,t,a){"use strict";a.r(t);var s=a(7437),r=a(2173),l=a(2265),d=a(7948),o=a(4033),i=a(1396),c=a.n(i),n=a(5535);t.default=()=>{(0,o.useRouter)();let[e,t]=(0,l.useState)([]),[a,i]=(0,l.useState)([]),[m,u]=(0,l.useState)(""),[h,x]=(0,l.useState)({name:"",image:null,bio:""}),[g,p]=(0,l.useState)(!1),[b,y]=(0,l.useState)("categories"),[j,f]=(0,l.useState)(null),[v,N]=(0,l.useState)(!1),[w,C]=(0,l.useState)(null),[k,A]=(0,l.useState)({x:0,y:0}),[S,E]=(0,l.useState)(1),[_,F]=(0,l.useState)(null),[Z,D]=(0,l.useState)(null);(0,l.useEffect)(()=>{M(),P()},[]);let M=async()=>{try{let e=await r.Z.get("/api/categories");e.data.success&&t(e.data.categories)}catch(e){console.error("Error fetching categories:",e),d.toast.error("Failed to load categories")}},P=async()=>{try{let e=await r.Z.get("/api/authors");e.data.success&&i(e.data.authors)}catch(e){console.error("Error fetching authors:",e),d.toast.error("Failed to load authors")}},I=async e=>{if(e.preventDefault(),m.trim())try{p(!0);let e=await r.Z.post("/api/categories",{name:m});e.data.success?(d.toast.success("Category added successfully"),u(""),e.data.categories?t(e.data.categories):await M()):d.toast.error(e.data.message||"Failed to add category")}catch(e){if(console.error("Error adding category:",e),e.response&&409===e.response.status)d.toast.error("Category already exists");else{var a,s;d.toast.error((null===(s=e.response)||void 0===s?void 0:null===(a=s.data)||void 0===a?void 0:a.message)||"Failed to add category")}}finally{p(!1)}},R=async a=>{try{let s=await r.Z.delete("/api/categories?id=".concat(a));s.data.success?(d.toast.success("Category deleted successfully"),t(e.filter(e=>e._id!==a))):d.toast.error(s.data.message||"Failed to delete category")}catch(e){var s,l;console.error("Error deleting category:",e),d.toast.error((null===(l=e.response)||void 0===l?void 0:null===(s=l.data)||void 0===s?void 0:s.message)||"Failed to delete category")}},B=e=>{let{name:t,value:a}=e.target;x({...h,[t]:a})},L=(0,l.useCallback)((e,t)=>{F(t)},[]),O=e=>new Promise((t,a)=>{let s=new Image;s.addEventListener("load",()=>t(s)),s.addEventListener("error",e=>a(e)),s.src=e}),q=async(e,t)=>{let a=await O(e),s=document.createElement("canvas"),r=s.getContext("2d"),l=Math.max(a.width,a.height)/2*Math.sqrt(2)*2;s.width=l,s.height=l,r.drawImage(a,l/2-.5*a.width,l/2-.5*a.height);let d=r.getImageData(0,0,l,l);return s.width=t.width,s.height=t.height,r.putImageData(d,Math.round(0-l/2+.5*a.width-t.x),Math.round(0-l/2+.5*a.height-t.y)),new Promise(e=>{s.toBlob(t=>{e(t)},"image/jpeg")})},U=async()=>{try{if(!_)return;let e=await q(w,_),t=URL.createObjectURL(e);D(e),f(t),N(!1)}catch(e){console.error("Error applying crop:",e),d.toast.error("Failed to crop image")}},z=async e=>{if(e.preventDefault(),!h.name.trim()){d.toast.error("Author name is required");return}try{p(!0);let e=new FormData;if(e.append("name",h.name),e.append("bio",h.bio),Z){let t=new File([Z],"cropped_image.jpg",{type:"image/jpeg"});e.append("image",t)}let t=await r.Z.post("/api/authors",e);t.data.success?(d.toast.success("Author added successfully"),x({name:"",image:null,bio:""}),f(null),D(null),await P()):d.toast.error(t.data.message||"Failed to add author")}catch(e){var t,a;console.error("Error adding author:",e),d.toast.error((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"Failed to add author")}finally{p(!1)}},G=async e=>{try{let t=await r.Z.delete("/api/authors?id=".concat(e));t.data.success?(d.toast.success("Author deleted successfully"),i(a.filter(t=>t._id!==e))):d.toast.error(t.data.message||"Failed to delete author")}catch(e){var t,s;console.error("Error deleting author:",e),d.toast.error((null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.message)||"Failed to delete author")}};return(0,s.jsxs)("div",{className:"pt-5 px-5 sm:pt-12 sm:pl-16",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Settings"}),(0,s.jsxs)("div",{className:"flex border-b mb-6",children:[(0,s.jsx)("button",{className:"py-2 px-4 ".concat("categories"===b?"border-b-2 border-black font-medium":"text-gray-500"),onClick:()=>y("categories"),children:"Categories"}),(0,s.jsx)("button",{className:"py-2 px-4 ".concat("authors"===b?"border-b-2 border-black font-medium":"text-gray-500"),onClick:()=>y("authors"),children:"Authors"})]}),"categories"===b&&(0,s.jsx)("div",{className:"max-w-3xl",children:(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md mb-8",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Manage Blog Categories"}),(0,s.jsx)("form",{onSubmit:I,className:"mb-6",children:(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)("input",{type:"text",value:m,onChange:e=>u(e.target.value),placeholder:"Enter new category name",className:"flex-1 px-4 py-2 border border-gray-300 rounded-md",required:!0}),(0,s.jsx)("button",{type:"submit",className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",disabled:g,children:"Add Category"})]})}),(0,s.jsx)("div",{className:"border rounded-md overflow-hidden",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category Name"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsxs)("tbody",{className:"bg-white divide-y divide-gray-200",children:[e.map(e=>(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,s.jsx)("button",{onClick:()=>R(e._id),className:"text-red-600 hover:text-red-900",children:"Delete"})})]},e._id)),0===e.length&&(0,s.jsx)("tr",{children:(0,s.jsx)("td",{colSpan:"2",className:"px-6 py-4 text-center text-sm text-gray-500",children:"No categories found"})})]})]})})]})}),"authors"===b&&(0,s.jsx)("div",{className:"max-w-3xl",children:(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-300 shadow-md mb-8",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Manage Blog Authors"}),v?(0,s.jsxs)("div",{className:"mb-6 max-w-2xl",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Adjust Profile Image"}),(0,s.jsx)("div",{className:"relative h-80 mb-4",children:(0,s.jsx)(n.ZP,{image:w,crop:k,zoom:S,aspect:1,cropShape:"round",onCropChange:A,onCropComplete:L,onZoomChange:E})}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Zoom: ",S.toFixed(1),"x"]}),(0,s.jsx)("input",{type:"range",min:1,max:3,step:.1,value:S,onChange:e=>E(parseFloat(e.target.value)),className:"w-full"})]}),(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)("button",{type:"button",onClick:U,className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",children:"Apply"}),(0,s.jsx)("button",{type:"button",onClick:()=>{N(!1),C(null)},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"})]})]}):(0,s.jsxs)("form",{onSubmit:z,className:"mb-6",children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Author Name"}),(0,s.jsx)("input",{type:"text",name:"name",value:h.name,onChange:B,placeholder:"Enter author name",className:"w-full px-4 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Author Bio"}),(0,s.jsx)("textarea",{name:"bio",value:h.bio,onChange:B,placeholder:"Enter author bio",className:"w-full px-4 py-2 border border-gray-300 rounded-md",rows:3})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Author Image"}),(0,s.jsxs)("div",{className:"flex items-center",children:[j&&(0,s.jsx)("div",{className:"mr-4",children:(0,s.jsx)("img",{src:j,alt:"Author preview",className:"w-16 h-16 object-cover rounded-full border-2 border-gray-200"})}),(0,s.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{let t=e.target.files[0];if(t){let e=new FileReader;e.onload=()=>{C(e.result),N(!0)},e.readAsDataURL(t)}},className:"w-full px-4 py-2 border border-gray-300 rounded-md"})]})]}),(0,s.jsx)("button",{type:"submit",className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",disabled:g,children:"Add Author"})]}),(0,s.jsx)("div",{className:"border rounded-md overflow-hidden",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Author"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Bio"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsxs)("tbody",{className:"bg-white divide-y divide-gray-200",children:[a.map(e=>(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,s.jsx)("img",{className:"h-10 w-10 rounded-full object-cover",src:e.image||"/author_img.png",alt:e.name})}),(0,s.jsx)("div",{className:"ml-4",children:(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name})})]})}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:e.bio||"No bio available"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,s.jsxs)("div",{className:"flex gap-3 justify-end",children:[(0,s.jsx)(c(),{href:"/admin/editAuthor/".concat(e._id),className:"cursor-pointer text-blue-600 hover:underline",children:"Edit"}),(0,s.jsx)("button",{onClick:()=>G(e._id),className:"cursor-pointer text-red-600 hover:underline",children:"Delete"})]})})]},e._id)),0===a.length&&(0,s.jsx)("tr",{children:(0,s.jsx)("td",{colSpan:"3",className:"px-6 py-4 text-center text-sm text-gray-500",children:"No authors found"})})]})]})})]})})]})}},6993:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return s}});let s=a(1024)._(a(2265)).default.createContext(null)}},function(e){e.O(0,[580,396,442,971,938,744],function(){return e(e.s=9147)}),_N_E=e.O()}]);
(()=>{var e={};e.id=8379,e.ids=[8379],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},86593:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,originalPathname:()=>p,pages:()=>u,routeModule:()=>m,tree:()=>d});var a=r(50482),s=r(69108),o=r(62563),i=r.n(o),n=r(68300),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d=["",{children:["admin",{children:["editAuthor",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,38207)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\editAuthor\\[id]\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,32143)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,39459)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\editAuthor\\[id]\\page.jsx"],p="/admin/editAuthor/[id]/page",c={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/admin/editAuthor/[id]/page",pathname:"/admin/editAuthor/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},91253:(e,t,r)=>{Promise.resolve().then(r.bind(r,9706))},9706:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var a=r(95344),s=r(3729),o=r(22254),i=r(53608),n=r(69697),l=r(41223),d=r.n(l),u=r(79338);let p=({params:e})=>{let t=(0,o.useRouter)(),[r,l]=(0,s.useState)(!0),[p,c]=(0,s.useState)({name:"",bio:"",image:null}),[m,h]=(0,s.useState)(null),[x,g]=(0,s.useState)(null),[b,y]=(0,s.useState)(!1),[j,f]=(0,s.useState)({x:0,y:0}),[v,w]=(0,s.useState)(1),[N,q]=(0,s.useState)(null),[C,S]=(0,s.useState)(null);(0,s.useEffect)(()=>{(async()=>{try{let r=await i.Z.get(`/api/authors/${e.id}`);if(r.data.success){let e=r.data.author;c({name:e.name,bio:e.bio||""}),h(e.image||"/author_img.png")}else n.toast.error("Failed to load author data"),t.push("/admin/settings")}catch(e){console.error("Error fetching author:",e),n.toast.error("Failed to load author data"),t.push("/admin/settings")}finally{l(!1)}})()},[e.id,t]);let A=e=>{let{name:t,value:r}=e.target;c(e=>({...e,[t]:r}))},P=(0,s.useCallback)((e,t)=>{q(t)},[]),_=e=>new Promise((t,r)=>{let a=new(d());a.addEventListener("load",()=>t(a)),a.addEventListener("error",e=>r(e)),a.src=e}),k=async(e,t)=>{let r=await _(e),a=document.createElement("canvas"),s=a.getContext("2d"),o=Math.max(r.width,r.height)/2*Math.sqrt(2)*2;a.width=o,a.height=o,s.drawImage(r,o/2-.5*r.width,o/2-.5*r.height);let i=s.getImageData(0,0,o,o);return a.width=t.width,a.height=t.height,s.putImageData(i,Math.round(0-o/2+.5*r.width-t.x),Math.round(0-o/2+.5*r.height-t.y)),new Promise(e=>{a.toBlob(t=>{e(t)},"image/jpeg")})},D=async()=>{try{if(!N)return;let e=await k(x,N),t=URL.createObjectURL(e);S(e),h(t),y(!1)}catch(e){console.error("Error applying crop:",e),n.toast.error("Failed to crop image")}},E=async r=>{if(r.preventDefault(),!p.name.trim()){n.toast.error("Author name is required");return}try{l(!0);let r=new FormData;if(r.append("name",p.name),r.append("bio",p.bio),C){let e=new File([C],"cropped_image.jpg",{type:"image/jpeg"});r.append("image",e)}let a=await i.Z.put(`/api/authors/${e.id}`,r);a.data.success?(n.toast.success("Author updated successfully"),t.push("/admin/settings")):n.toast.error(a.data.message||"Failed to update author")}catch(e){console.error("Error updating author:",e),n.toast.error(e.response?.data?.message||"Failed to update author")}finally{l(!1)}};return r?a.jsx("div",{className:"pt-5 px-5 sm:pt-12 sm:pl-16",children:a.jsx("p",{children:"Loading author data..."})}):(0,a.jsxs)("div",{className:"pt-5 px-5 sm:pt-12 sm:pl-16",children:[a.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Edit Author"}),a.jsx("div",{className:"max-w-2xl bg-white p-6 rounded-lg border border-gray-300 shadow-md",children:b?(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h2",{className:"text-lg font-semibold mb-4",children:"Adjust Profile Image"}),a.jsx("div",{className:"relative h-80 mb-4",children:a.jsx(u.ZP,{image:x,crop:j,zoom:v,aspect:1,cropShape:"round",onCropChange:f,onCropComplete:P,onZoomChange:w})}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Zoom: ",v.toFixed(1),"x"]}),a.jsx("input",{type:"range",min:1,max:3,step:.1,value:v,onChange:e=>w(parseFloat(e.target.value)),className:"w-full"})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[a.jsx("button",{type:"button",onClick:D,className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",children:"Apply"}),a.jsx("button",{type:"button",onClick:()=>{y(!1),g(null)},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"})]})]}):(0,a.jsxs)("form",{onSubmit:E,children:[(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Author Name"}),a.jsx("input",{type:"text",name:"name",value:p.name,onChange:A,placeholder:"Enter author name",className:"w-full px-4 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Author Bio"}),a.jsx("textarea",{name:"bio",value:p.bio,onChange:A,placeholder:"Enter author bio",className:"w-full px-4 py-2 border border-gray-300 rounded-md",rows:3})]}),(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Author Image"}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-4",children:[m&&a.jsx("div",{children:a.jsx("img",{src:m,alt:"Author preview",className:"w-24 h-24 object-cover rounded-full border-2 border-gray-200"})}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("input",{type:"file",accept:"image/*",onChange:e=>{let t=e.target.files[0];if(t){let e=new FileReader;e.onload=()=>{g(e.result),y(!0)},e.readAsDataURL(t)}},className:"w-full px-4 py-2 border border-gray-300 rounded-md"}),a.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Select an image to upload and adjust"})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[a.jsx("button",{type:"submit",className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",disabled:r,children:"Update Author"}),a.jsx("button",{type:"button",onClick:()=>t.push("/admin/settings"),className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"})]})]})})]})}},38207:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>o,__esModule:()=>s,default:()=>i});let a=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\admin\editAuthor\[id]\page.jsx`),{__esModule:s,$$typeof:o}=a,i=a.default}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,3998,337,8468,9338,5757,7388],()=>r(86593));module.exports=a})();
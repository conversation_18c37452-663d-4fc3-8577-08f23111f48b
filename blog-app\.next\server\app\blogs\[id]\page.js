(()=>{var e={};e.id=7429,e.ids=[7429],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},93747:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=s(50482),a=s(69108),o=s(62563),l=s.n(o),i=s(68300),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);s.d(t,n);let c=["",{children:["blogs",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,15185)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\blogs\\[id]\\page.jsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,39459)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\blogs\\[id]\\page.jsx"],u="/blogs/[id]/page",m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/blogs/[id]/page",pathname:"/blogs/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},81487:(e,t,s)=>{Promise.resolve().then(s.bind(s,96267))},28674:(e,t,s)=>{"use strict";s.d(t,{Z:()=>c});var r=s(95344),a=s(6880),o=s(41223),l=s.n(o),i=s(20783),n=s.n(i);s(3729);let c=()=>(0,r.jsxs)("div",{className:"flex justify-around flex-col gap-2 sm:gap-0 sm:flex-row bg-black py-5 items-center",children:[r.jsx(l(),{src:a.L.logo_light,alt:"Mr.Blogger",width:120}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"flex justify-center gap-4 mb-1",children:[r.jsx(n(),{href:"/about",className:"text-sm text-white hover:underline",children:"About Us"}),r.jsx(n(),{href:"/contact",className:"text-sm text-white hover:underline",children:"Contact Us"})]}),r.jsx("p",{className:"text-sm text-white",children:"All right reserved. Copyright @Mr.Blogger"})]}),(0,r.jsxs)("div",{className:"flex",children:[r.jsx(l(),{src:a.L.facebook_icon,alt:"",width:40}),r.jsx(l(),{src:a.L.twitter_icon,alt:"",width:40}),r.jsx(l(),{src:a.L.googleplus_icon,alt:"",width:40})]})]})},96267:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var r=s(95344),a=s(6880),o=s(28674),l=s(53608),i=s(41223),n=s.n(i),c=s(20783),d=s.n(c),u=s(3729),m=s(69697);s(45996);var x=s(22254),h=s(60267);let g=e=>(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",...e,children:[r.jsx("line",{x1:"5",y1:"12",x2:"19",y2:"12"}),r.jsx("polyline",{points:"12 5 19 12 12 19"})]}),p=({blogs:e,currentBlogId:t})=>{let s=e.filter(e=>e._id!==t).slice(0,12),[a,o]=(0,u.useState)(0),[l,i]=(0,u.useState)(!1),c=(0,u.useRef)(null),m=(0,u.useRef)(null),[x,h]=(0,u.useState)(4),[p,b]=(0,u.useState)(80);(0,u.useEffect)(()=>{let e=()=>{window.innerWidth<640?(h(1),b(40)):window.innerWidth<768?(h(2),b(60)):window.innerWidth<1024?(h(3),b(70)):(h(4),b(80))};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,u.useEffect)(()=>{if(!(s.length<=x))return m.current=setTimeout(()=>{l||f()},2e3),()=>{m.current&&clearTimeout(m.current)}},[a,s.length,x,l]);let w=()=>{if(!c.current)return`${100/s.length}%`;let e=(c.current.offsetWidth-p)/x;return`${e}px`},f=()=>{l||s.length<=x||(i(!0),o(e=>e===s.length-x?0:e+1),setTimeout(()=>{i(!1)},300))},y=()=>{l||s.length<=x||(i(!0),o(e=>0===e?s.length-x:e-1),setTimeout(()=>{i(!1)},300))},j=e=>{m.current&&clearTimeout(m.current),e(),m.current=setTimeout(()=>{f()},2e3)};return 0===s.length?null:(0,r.jsxs)("div",{className:"my-12 border-t border-gray-200 pt-10",children:[r.jsx("h2",{className:"text-xl sm:text-2xl font-bold mb-6 text-center",children:"Trending Articles"}),(0,r.jsxs)("div",{className:"relative px-4",children:[s.length>x&&(0,r.jsxs)(r.Fragment,{children:[r.jsx("button",{onClick:()=>j(y),className:"absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full p-2 shadow-md","aria-label":"Previous",children:r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),r.jsx("button",{onClick:()=>j(f),className:"absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full p-2 shadow-md","aria-label":"Next",children:r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),r.jsx("div",{className:"overflow-hidden",ref:c,style:{paddingRight:`${p}px`},children:r.jsx("div",{className:"flex transition-transform duration-300 ease-in-out",style:{transform:`translateX(-${(()=>{if(!c.current)return`${100/x*a}%`;let e=(c.current.offsetWidth-p)/x;return`${a*e}px`})()})`},children:s.map(e=>r.jsx("div",{className:"pr-4 flex-shrink-0",style:{width:w()},children:(0,r.jsxs)("div",{className:"bg-white border border-gray-200 transition-all hover:shadow-md h-full",children:[r.jsx(d(),{href:`/blogs/${e._id}`,children:r.jsx("div",{className:"relative h-36 border-b border-gray-200",children:r.jsx(n(),{src:e.image,alt:e.title,fill:!0,className:"object-cover"})})}),(0,r.jsxs)("div",{className:"p-3",children:[r.jsx("p",{className:"px-1.5 py-0.5 mb-1.5 inline-block bg-gray-100 text-gray-800 text-xs",children:e.category}),r.jsx("h3",{className:"text-sm font-medium mb-1.5 line-clamp-2",children:e.title}),(0,r.jsxs)(d(),{href:`/blogs/${e._id}`,className:"inline-flex items-center text-xs font-semibold text-gray-700",children:["Read more ",r.jsx(g,{className:"ml-1 w-3 h-3"})]})]})]})},e._id))})}),s.length>x&&r.jsx("div",{className:"flex justify-center mt-4",children:Array.from({length:s.length-x+1}).map((e,t)=>r.jsx("button",{onClick:()=>j(()=>{i(!0),o(t),setTimeout(()=>i(!1),500)}),className:`h-2 w-2 mx-1 rounded-full ${a===t?"bg-gray-800":"bg-gray-300"}`,"aria-label":`Go to slide ${t+1}`},t))})]})]})};function b({params:e}){let t=(0,x.useRouter)(),[s,i]=(0,u.useState)(null),[c,g]=(0,u.useState)(!1),[b,w]=(0,u.useState)({email:"",password:""}),[f,y]=(0,u.useState)(!1),[j,v]=(0,u.useState)({email:"",password:"",confirmPassword:"",role:"user"}),[k,N]=(0,u.useState)(!1),[C,S]=(0,u.useState)(""),[L,P]=(0,u.useState)(!1),[_,I]=(0,u.useState)("/default_profile.png"),[$,q]=(0,u.useState)(""),[A,M]=(0,u.useState)(!1),[B,E]=(0,u.useState)(!1),[T,z]=(0,u.useState)(!1),[R,Z]=(0,u.useState)(!1),[D,F]=(0,u.useState)(!1),[W,G]=(0,u.useState)(!1),[U,H]=(0,u.useState)(!1),[Y,V]=(0,u.useState)(0),[O,X]=(0,u.useState)([]),K=e=>{w({...b,[e.target.name]:e.target.value})},J=e=>{v({...j,[e.target.name]:e.target.value})},Q=async e=>{e.preventDefault();try{let e=await l.Z.post("/api/auth",{email:b.email,password:b.password});e.data.success?(localStorage.setItem("authToken",e.data.token||"dummy-token"),localStorage.setItem("userRole",e.data.user.role),localStorage.setItem("userId",e.data.user.id),D?(localStorage.setItem("rememberedEmail",b.email),localStorage.setItem("rememberedPassword",b.password),localStorage.setItem("rememberMe","true")):(localStorage.removeItem("rememberedEmail"),localStorage.removeItem("rememberedPassword"),localStorage.removeItem("rememberMe")),"admin"===e.data.user.role?(m.toast.success("Login successful"),window.location.href="/admin"):(m.toast.success("Login successful"),window.location.href="/")):m.toast.error("Invalid credentials")}catch(e){console.error("Login error:",e),m.toast.error(e.response?.data?.message||"Login failed")}},ee=async e=>{if(e.preventDefault(),j.password!==j.confirmPassword){m.toast.error("Passwords do not match");return}try{let e=await l.Z.post("/api/register",{email:j.email,password:j.password,role:j.role});e.data.success?(m.toast.success("Registration successful! Please login."),y(!1),w({...b,email:j.email}),v({email:"",password:"",confirmPassword:"",role:"user"})):m.toast.error(e.data.message||"Registration failed")}catch(e){console.error("Registration error:",e),m.toast.error(e.response?.data?.message||"Registration failed")}},et=()=>{y(!f)},es=async()=>{i((await l.Z.get("/api/blog",{params:{id:e.id}})).data)};(0,u.useEffect)(()=>{es()},[]),(0,u.useEffect)(()=>{let e=localStorage.getItem("rememberedEmail"),t=localStorage.getItem("rememberedPassword"),s="true"===localStorage.getItem("rememberMe");e&&t&&s&&(w({email:e,password:t}),F(!0));let r=localStorage.getItem("authToken"),a=localStorage.getItem("userRole"),o=localStorage.getItem("userProfilePicture"),l=localStorage.getItem("userName");r&&(N(!0),S(a||"user"),o&&I(o),l&&q(l))},[]);let er=async t=>{try{let s=await l.Z.get(`/api/favorites?blogId=${e.id}`,{headers:{Authorization:`Bearer ${t}`}});s.data.success&&E(s.data.isFavorite)}catch(e){console.error("Error checking favorite status:",e)}},ea=async()=>{if(!k){g(!0);return}try{z(!0);let t=localStorage.getItem("authToken");console.log("Using token:",t),B?(await l.Z.delete(`/api/favorites?blogId=${e.id}`,{headers:{Authorization:`Bearer ${t}`}})).data.success&&(E(!1),m.toast.success("Removed from favorites")):(await l.Z.post("/api/favorites",{blogId:e.id},{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}})).data.success&&(E(!0),m.toast.success("Added to favorites"))}catch(e){console.error("Error toggling favorite:",e),e.response?.status===401?(m.toast.error("Please log in again to continue"),localStorage.removeItem("authToken"),N(!1),g(!0)):m.toast.error("Failed to update favorites")}finally{z(!1)}},eo=async t=>{try{let s=await l.Z.get(`/api/likes?blogId=${e.id}`,{headers:{Authorization:`Bearer ${t}`}});s.data.success&&G(s.data.isLiked)}catch(e){console.error("Error checking like status:",e)}},el=async()=>{try{let t=await l.Z.get(`/api/blog/likes?id=${e.id}`);t.data.success&&V(t.data.count)}catch(e){console.error("Error fetching likes count:",e)}},ei=async()=>{try{let e=await l.Z.get("/api/blog/trending");e.data.success&&X(e.data.blogs)}catch(e){console.error("Error fetching trending blogs:",e)}},en=async()=>{if(!k){g(!0);return}try{H(!0);let t=localStorage.getItem("authToken");W?(await l.Z.delete(`/api/likes?blogId=${e.id}`,{headers:{Authorization:`Bearer ${t}`}})).data.success&&(G(!1),V(e=>Math.max(0,e-1)),m.toast.success("Like removed")):(await l.Z.post("/api/likes",{blogId:e.id},{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}})).data.success&&(G(!0),V(e=>e+1),m.toast.success("Post liked"))}catch(e){console.error("Error toggling like:",e),e.response?.status===401?(m.toast.error("Please log in again to continue"),localStorage.removeItem("authToken"),N(!1),g(!0)):m.toast.error("Failed to update like")}finally{H(!1)}};return(0,u.useEffect)(()=>{let e=localStorage.getItem("authToken");e&&(N(!0),er(e),eo(e)),el(),ei()},[]),(0,u.useEffect)(()=>{s&&s._id&&(0,h.Z0)(`/blogs/${e.id}`,"blog",s._id)},[s,e.id]),s?(0,r.jsxs)(r.Fragment,{children:[r.jsx(m.ToastContainer,{position:"top-center",autoClose:3e3}),(0,r.jsxs)("div",{className:"bg-gray-200 py-5 px-5 md:px-12 lg:px-28",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx(d(),{href:"/",children:r.jsx(n(),{src:a.L.logo,width:180,alt:"",className:"w-[130px] sm:w-auto"})}),r.jsx("div",{className:"flex gap-3",children:k?r.jsx("div",{className:"relative",children:(0,r.jsxs)("button",{onClick:()=>t.push("/profile"),className:"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]",children:[r.jsx(n(),{src:_,width:24,height:24,alt:"Account",className:"w-6 h-6 rounded-full object-cover"}),r.jsx("span",{children:$||"Account"})]})}):(0,r.jsxs)("button",{onClick:()=>g(!0),className:"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]",children:["Get started ",r.jsx(n(),{src:a.L.arrow,alt:""})]})})]}),(0,r.jsxs)("div",{className:"text-center my-24",children:[r.jsx("h1",{className:"text-2xl sm:text-5xl font-semibold max-w-[700px] mx-auto",children:s.title}),r.jsx("div",{className:"flex justify-center mt-4",children:(0,r.jsxs)("button",{onClick:ea,disabled:T,className:`flex items-center gap-2 px-4 py-2 rounded-full ${B?"bg-yellow-100 text-yellow-700 border border-yellow-300":"bg-gray-100 text-gray-700 border border-gray-300"}`,children:[r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:B?"currentColor":"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:r.jsx("polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"})}),B?"Favorited":"Add to Favorites"]})}),r.jsx(n(),{className:"mx-auto mt-6 rounded-full object-cover w-16 h-16 border-2 border-white",src:s.authorImg,width:60,height:60,alt:""}),r.jsx("p",{className:"mt-1 pb-2 text-lg max-w-[740px] mx-auto",children:s.author})]})]}),(0,r.jsxs)("div",{className:"mx-5 max-w-[800px] md:mx-auto mt-[-100px] mb-10",children:[r.jsx(n(),{className:"border-4 border-white",src:s.image,width:800,height:480,alt:""}),r.jsx("div",{className:"blog-content",dangerouslySetInnerHTML:{__html:(e=>{if(!e)return"";let t=e;return(t=t.replace(/\[\[([a-f\d]{24})\|([^\]]+)\]\]/g,(e,t,s)=>`<a href="/blogs/${t}" class="text-blue-600 hover:underline">${s}</a>`)).replace(/\{\{image:([^|]+)\|([^}]+)\}\}/g,(e,t,s)=>`<div class="my-6 text-center">
        <img src="${t}" alt="${s}" class="max-w-full h-auto mx-auto rounded-lg shadow-md" style="max-height: 400px;" />
      </div>`)})(s.description)}}),r.jsx("div",{className:"mt-8 flex items-center",children:(0,r.jsxs)("button",{onClick:en,disabled:U,className:`flex items-center gap-2 px-4 py-2 rounded-full transition-all ${W?"bg-red-100 text-red-600 border border-red-300":"bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200"}`,children:[r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:W?"currentColor":"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:r.jsx("path",{d:"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"})}),W?"Liked":"Like"]})}),(0,r.jsxs)("div",{className:"my-24",children:[r.jsx("p",{className:"text-black font font-semibold my-4",children:"Share this article on social media"}),(0,r.jsxs)("div",{className:"flex",children:[r.jsx("button",{onClick:()=>{window.location.href="/"},className:"flex items-center justify-center w-[40px] h-[40px] bg-white rounded-full mr-3 shadow-lg transition-all border border-gray-200 hover:shadow-2xl",title:"Share on Facebook",children:r.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{d:"M22.675 0h-21.35C.595 0 0 .592 0 1.326v21.348C0 23.406.595 24 1.325 24h11.495v-9.294H9.692v-3.622h3.128V8.413c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.797.143v3.24l-1.918.001c-1.504 0-1.797.715-1.797 1.763v2.313h3.587l-.467 3.622h-3.12V24h6.116C23.406 24 24 23.406 24 22.674V1.326C24 .592 23.406 0 22.675 0"})})}),r.jsx("button",{onClick:()=>{window.location.href="/"},className:"flex items-center justify-center w-[40px] h-[40px] bg-white rounded-full mr-3 shadow-lg transition-all border border-gray-200 hover:shadow-2xl",title:"Share on Twitter",children:r.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{d:"M24 4.557a9.83 9.83 0 0 1-2.828.775 4.932 4.932 0 0 0 2.165-2.724c-.951.564-2.005.974-3.127 1.195a4.916 4.916 0 0 0-8.38 4.482C7.691 8.095 4.066 6.13 1.64 3.161c-.542.929-.856 2.01-.857 3.17 0 2.188 1.115 4.116 2.823 5.247a4.904 4.904 0 0 1-2.229-.616c-.054 2.281 1.581 4.415 3.949 4.89a4.936 4.936 0 0 1-2.224.084c.627 1.956 2.444 3.377 4.6 3.417A9.867 9.867 0 0 1 0 21.543a13.94 13.94 0 0 0 7.548 2.209c9.057 0 14.009-7.496 14.009-13.986 0-.21 0-.423-.016-.634A9.936 9.936 0 0 0 24 4.557z"})})}),r.jsx("button",{onClick:()=>{window.location.href="/"},className:"flex items-center justify-center w-[40px] h-[40px] bg-white rounded-full shadow-lg transition-all border border-gray-200 hover:shadow-2xl",title:"Share on Google Plus",children:(0,r.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 48 48",fill:"black",xmlns:"http://www.w3.org/2000/svg",children:[r.jsx("circle",{cx:"24",cy:"24",r:"24",fill:"black"}),r.jsx("text",{x:"13",y:"32","font-size":"18","font-family":"Arial, Helvetica, sans-serif",fill:"white","font-weight":"bold",children:"G+"})]})}),r.jsx("button",{onClick:()=>{try{let e=document.createElement("input");e.value=window.location.href,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e),m.toast.success("Link copied to clipboard!",{position:"top-center",autoClose:3e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0,progress:void 0}),console.log("Link copied to clipboard!")}catch(e){console.error("Copy failed:",e),m.toast.error("Failed to copy link")}},className:"flex items-center justify-center w-[40px] h-[40px] bg-white rounded-full ml-3 shadow-lg transition-all border border-gray-200 hover:shadow-2xl",title:"Copy link to clipboard",children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[r.jsx("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"}),r.jsx("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"})]})})]})]})]}),r.jsx(p,{blogs:O,currentBlogId:e.id}),r.jsx(o.Z,{}),A&&r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-6 rounded-md shadow-lg max-w-sm w-full",children:[r.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Confirm Logout"}),r.jsx("p",{className:"mb-6",children:"Are you sure you want to log out? You will need to log in again to access your account."}),(0,r.jsxs)("div",{className:"flex justify-end gap-3",children:[r.jsx("button",{onClick:()=>{M(!1)},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"}),r.jsx("button",{onClick:()=>{localStorage.removeItem("authToken"),localStorage.removeItem("userRole"),N(!1),S(""),M(!1),m.toast.success("Logged out successfully")},className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",children:"Logout"})]})]})}),c&&r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-8 rounded-md shadow-lg w-96",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:f?"Register":"Login"}),f?(0,r.jsxs)("form",{onSubmit:ee,children:[(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{className:"block text-gray-700 mb-2",children:"Email"}),r.jsx("input",{type:"email",name:"email",value:j.email,onChange:J,className:"w-full px-3 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{className:"block text-gray-700 mb-2",children:"Password"}),r.jsx("input",{type:"password",name:"password",value:j.password,onChange:J,className:"w-full px-3 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{className:"block text-gray-700 mb-2",children:"Confirm Password"}),r.jsx("input",{type:"password",name:"confirmPassword",value:j.confirmPassword,onChange:J,className:"w-full px-3 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("button",{type:"submit",className:"bg-black text-white px-4 py-2 rounded-md",children:"Register"}),r.jsx("button",{type:"button",onClick:()=>g(!1),className:"text-gray-600 px-4 py-2",children:"Cancel"})]}),r.jsx("div",{className:"mt-4 text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",r.jsx("button",{type:"button",onClick:et,className:"text-blue-600 hover:underline",children:"Login"})]})})]}):(0,r.jsxs)("form",{onSubmit:Q,children:[(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{className:"block text-gray-700 mb-2",children:"Email"}),r.jsx("input",{type:"email",name:"email",value:b.email,onChange:K,className:"w-full px-3 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("label",{className:"block text-gray-700 mb-2",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("input",{type:R?"text":"password",name:"password",value:b.password,onChange:K,className:"w-full px-3 py-2 border border-gray-300 rounded-md pr-10",required:!0}),r.jsx("button",{type:"button",onClick:()=>{Z(!R)},className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600",children:R?(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[r.jsx("path",{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}),r.jsx("path",{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"})]}):(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[r.jsx("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),r.jsx("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]})})]})]}),r.jsx("div",{className:"mb-4",children:(0,r.jsxs)("label",{className:"flex items-center",children:[r.jsx("input",{type:"checkbox",checked:D,onChange:e=>F(e.target.checked),className:"mr-2"}),r.jsx("span",{className:"text-sm text-gray-700",children:"Remember me"})]})}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("button",{type:"submit",className:"bg-black text-white px-4 py-2 rounded-md",children:"Login"}),r.jsx("button",{type:"button",onClick:()=>g(!1),className:"text-gray-600 px-4 py-2",children:"Cancel"})]}),r.jsx("div",{className:"mt-4 text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",r.jsx("button",{type:"button",onClick:et,className:"text-blue-600 hover:underline",children:"Register"})]})})]})]})})]}):null}},60267:(e,t,s)=>{"use strict";s.d(t,{Z0:()=>a,Z5:()=>l,uf:()=>o});var r=s(53608);let a=async(e,t="page",s=null)=>{try{console.log("Tracking page view:",{path:e,contentType:t,blogId:s});let a=document.referrer||null,o=await r.Z.post("/api/analytics",{path:e,contentType:t,blogId:s,referrer:a});console.log("Analytics tracking response:",o.data)}catch(e){console.error("Analytics error:",e)}},o=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),l=async(e="7days")=>{let t=localStorage.getItem("authToken");if(!t)throw Error("Authentication required");return(await r.Z.get(`/api/analytics?period=${e}`,{headers:{Authorization:`Bearer ${t}`}})).data}},15185:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>o,__esModule:()=>a,default:()=>l});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\blogs\[id]\page.jsx`),{__esModule:a,$$typeof:o}=r,l=r.default},45996:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,3998,337,8468,5757],()=>s(93747));module.exports=r})();
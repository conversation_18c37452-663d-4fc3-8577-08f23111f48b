/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./Components/Header.jsx":
/*!*******************************!*\
  !*** ./Components/Header.jsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _Assets_assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Assets/assets */ \"(app-pages-browser)/./Assets/assets.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _PaperRocketAnimation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PaperRocketAnimation */ \"(app-pages-browser)/./Components/PaperRocketAnimation.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst Header = (param)=>{\n    let { setSearchTerm } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [isLoggedIn, setIsLoggedIn] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [showLoginModal, setShowLoginModal] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isRegistering, setIsRegistering] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showAccountDropdown, setShowAccountDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [loginData, setLoginData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({\n        email: \"\",\n        password: \"\"\n    });\n    const [registerData, setRegisterData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        role: \"user\"\n    });\n    const [userProfilePicture, setUserProfilePicture] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"/default_profile.png\");\n    const [userName, setUserName] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [showLogoutConfirm, setShowLogoutConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showRegisterPassword, setShowRegisterPassword] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showRegisterConfirmPassword, setShowRegisterConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [rememberMe, setRememberMe] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isSearchMode, setIsSearchMode] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [localSearchTerm, setLocalSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [showTooltip, setShowTooltip] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    // Add this function to toggle password visibility\n    const togglePasswordVisibility = ()=>{\n        setShowPassword(!showPassword);\n    };\n    const toggleRegisterPasswordVisibility = ()=>{\n        setShowRegisterPassword(!showRegisterPassword);\n    };\n    const toggleRegisterConfirmPasswordVisibility = ()=>{\n        setShowRegisterConfirmPassword(!showRegisterConfirmPassword);\n    };\n    // Check if user is logged in on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        const authToken = localStorage.getItem(\"authToken\");\n        const storedUserRole = localStorage.getItem(\"userRole\");\n        const storedUserId = localStorage.getItem(\"userId\");\n        const storedProfilePicture = localStorage.getItem(\"userProfilePicture\");\n        const storedUserName = localStorage.getItem(\"userName\");\n        // Check for remembered credentials\n        const rememberedEmail = localStorage.getItem(\"rememberedEmail\");\n        const rememberedPassword = localStorage.getItem(\"rememberedPassword\");\n        const wasRemembered = localStorage.getItem(\"rememberMe\") === \"true\";\n        if (rememberedEmail && rememberedPassword && wasRemembered) {\n            setLoginData({\n                email: rememberedEmail,\n                password: rememberedPassword\n            });\n            setRememberMe(true);\n        }\n        if (authToken) {\n            setIsLoggedIn(true);\n            setUserRole(storedUserRole || \"user\");\n            if (storedProfilePicture) {\n                setUserProfilePicture(storedProfilePicture);\n            }\n            if (storedUserName) {\n                setUserName(storedUserName);\n            }\n        }\n    }, []);\n    const onSubmitHandler = async (e)=>{\n        // Existing email subscription code\n        e.preventDefault();\n        const formData = new FormData();\n        formData.append(\"email\", email);\n        const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].post(\"/api/email\", formData);\n        if (response.data.success) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(response.data.msg);\n            setEmail(\"\");\n        } else {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Error\");\n        }\n    };\n    const handleLoginChange = (e)=>{\n        setLoginData({\n            ...loginData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleRegisterChange = (e)=>{\n        setRegisterData({\n            ...registerData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleLoginSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].post(\"/api/auth\", {\n                email: loginData.email,\n                password: loginData.password\n            });\n            if (response.data.success) {\n                // Store auth data in localStorage\n                localStorage.setItem(\"authToken\", response.data.token || \"dummy-token\");\n                localStorage.setItem(\"userRole\", response.data.user.role);\n                localStorage.setItem(\"userId\", response.data.user.id);\n                localStorage.setItem(\"userProfilePicture\", response.data.user.profilePicture);\n                localStorage.setItem(\"userName\", response.data.user.name || \"\");\n                // Handle remember me functionality\n                if (rememberMe) {\n                    localStorage.setItem(\"rememberedEmail\", loginData.email);\n                    localStorage.setItem(\"rememberedPassword\", loginData.password);\n                    localStorage.setItem(\"rememberMe\", \"true\");\n                } else {\n                    localStorage.removeItem(\"rememberedEmail\");\n                    localStorage.removeItem(\"rememberedPassword\");\n                    localStorage.removeItem(\"rememberMe\");\n                }\n                // Update state\n                setIsLoggedIn(true);\n                setUserRole(response.data.user.role);\n                setUserProfilePicture(response.data.user.profilePicture);\n                setUserName(response.data.user.name || \"\");\n                setShowLoginModal(false);\n                // Check if user is admin\n                if (response.data.user.role === \"admin\") {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Login successful\");\n                    window.location.href = \"/admin\";\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Login successful\");\n                    // Redirect regular users to homepage or user dashboard\n                    window.location.href = \"/\";\n                }\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Invalid credentials\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Login error:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Login failed\");\n        }\n    };\n    const handleRegisterSubmit = async (e)=>{\n        e.preventDefault();\n        // Validate passwords match\n        if (registerData.password !== registerData.confirmPassword) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Passwords do not match\");\n            return;\n        }\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].post(\"/api/register\", {\n                email: registerData.email,\n                password: registerData.password,\n                role: registerData.role // Always \"user\" for public registration\n            });\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Registration successful! Please login.\");\n                // Switch back to login form\n                setIsRegistering(false);\n                // Pre-fill email in login form\n                setLoginData({\n                    ...loginData,\n                    email: registerData.email\n                });\n                // Reset register form\n                setRegisterData({\n                    email: \"\",\n                    password: \"\",\n                    confirmPassword: \"\",\n                    role: \"user\"\n                });\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(response.data.message || \"Registration failed\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Registration error:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Registration failed\");\n        }\n    };\n    const toggleForm = ()=>{\n        setIsRegistering(!isRegistering);\n    };\n    const handleLogoutClick = ()=>{\n        setShowLogoutConfirm(true);\n        setShowAccountDropdown(false);\n    };\n    const handleLogoutConfirm = ()=>{\n        // Clear auth data from localStorage\n        localStorage.removeItem(\"authToken\");\n        localStorage.removeItem(\"userRole\");\n        localStorage.removeItem(\"userId\");\n        localStorage.removeItem(\"userProfilePicture\");\n        localStorage.removeItem(\"userName\");\n        // Update state\n        setIsLoggedIn(false);\n        setUserRole(\"\");\n        setShowLogoutConfirm(false);\n        // Show toast and wait briefly before redirecting\n        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Logged out successfully\");\n        // Add a small delay before any navigation\n        setTimeout(()=>{\n            window.location.href = \"/\";\n        }, 300);\n    };\n    const handleLogoutCancel = ()=>{\n        setShowLogoutConfirm(false);\n    };\n    const toggleAccountDropdown = ()=>{\n        setShowAccountDropdown(!showAccountDropdown);\n    };\n    // Search handler (for now, just alert or log, you can wire to blog list)\n    const onSearchHandler = (e)=>{\n        e.preventDefault();\n        if (!localSearchTerm.trim()) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Please enter a search term\");\n            return;\n        }\n        setSearchTerm(localSearchTerm);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-5 px-5 md:px-12 lg:px-28\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.logo,\n                        width: 180,\n                        alt: \"\",\n                        className: \"w-[130px] sm:w-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/profile\"),\n                                className: \"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-solid border-black shadow-[-7px_7px_0px_#000000]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        src: userProfilePicture,\n                                        width: 24,\n                                        height: 24,\n                                        alt: \"Account\",\n                                        className: \"w-6 h-6 rounded-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: userName || \"Account\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 254,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowLoginModal(true),\n                            className: \"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-solid border-black shadow-[-7px_7px_0px_#000000]\",\n                            children: [\n                                \"Get started \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.arrow,\n                                    alt: \"\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 27\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 272,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, undefined),\n            showLoginModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-8 rounded-md shadow-lg w-96\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: isRegistering ? \"Register\" : \"Login\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, undefined),\n                        isRegistering ? // Registration Form\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleRegisterSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            name: \"email\",\n                                            value: registerData.email,\n                                            onChange: handleRegisterChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: showRegisterPassword ? \"text\" : \"password\",\n                                                    name: \"password\",\n                                                    value: registerData.password,\n                                                    onChange: handleRegisterChange,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md pr-10\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: toggleRegisterPasswordVisibility,\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600\",\n                                                    children: showRegisterPassword ? // Eye-slash icon (hidden)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 25\n                                                    }, undefined) : // Eye icon (visible)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: showRegisterConfirmPassword ? \"text\" : \"password\",\n                                                    name: \"confirmPassword\",\n                                                    value: registerData.confirmPassword,\n                                                    onChange: handleRegisterChange,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md pr-10\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: toggleRegisterConfirmPasswordVisibility,\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600\",\n                                                    children: showRegisterConfirmPassword ? // Eye-slash icon (hidden)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 25\n                                                    }, undefined) : // Eye icon (visible)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"bg-black text-white px-4 py-2 rounded-md\",\n                                            children: \"Register\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowLoginModal(false),\n                                            className: \"text-gray-600 px-4 py-2\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Already have an account?\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: toggleForm,\n                                                className: \"text-blue-600 hover:underline\",\n                                                children: \"Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 290,\n                            columnNumber: 15\n                        }, undefined) : // Login Form\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleLoginSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            name: \"email\",\n                                            value: loginData.email,\n                                            onChange: handleLoginChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    name: \"password\",\n                                                    value: loginData.password,\n                                                    onChange: handleLoginChange,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md pr-10\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: togglePasswordVisibility,\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600\",\n                                                    children: showPassword ? // Eye-slash icon (hidden)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 25\n                                                    }, undefined) : // Eye icon (visible)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: rememberMe,\n                                                onChange: (e)=>setRememberMe(e.target.checked),\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-700\",\n                                                children: \"Remember me\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"bg-black text-white px-4 py-2 rounded-md\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowLoginModal(false),\n                                            className: \"text-gray-600 px-4 py-2\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Don't have an account?\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: toggleForm,\n                                                className: \"text-blue-600 hover:underline\",\n                                                children: \"Register\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 396,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                    lineNumber: 285,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 284,\n                columnNumber: 9\n            }, undefined),\n            showLogoutConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-6 rounded-md shadow-lg max-w-sm w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Confirm Logout\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 488,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-6\",\n                            children: \"Are you sure you want to log out? You will need to log in again to access your account.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 489,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogoutCancel,\n                                    className: \"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogoutConfirm,\n                                    className: \"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800\",\n                                    children: \"Logout\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 490,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                    lineNumber: 487,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 486,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center my-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl sm:text-5xl font-medium\",\n                        children: \"Mr.Blogger\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 509,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-10 max-w-[740px] m-auto text-xs sm:text-base\",\n                        children: \"Discover insightful articles, trending tech news, startup journeys, and lifestyle stories — all in one place. Welcome to your daily dose of inspiration and knowledge.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 510,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: isSearchMode ? onSearchHandler : onSubmitHandler,\n                        className: \"flex justify-between max-w-[500px] scale-75 sm:scale-100 mx-auto mt-10 border border-black shadow-[-7px_7px_0px_#000000]\",\n                        action: \"\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                onChange: isSearchMode ? (e)=>{\n                                    setLocalSearchTerm(e.target.value);\n                                    if (e.target.value === \"\") setSearchTerm(\"\");\n                                } : (e)=>setEmail(e.target.value),\n                                value: isSearchMode ? localSearchTerm : email,\n                                type: isSearchMode ? \"text\" : \"email\",\n                                placeholder: isSearchMode ? \"Search blogs...\" : \"Enter your email\",\n                                className: \"pl-4 outline-none flex-1\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 516,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"border-l border-black py-4 px-4 sm:px-8 active:bg-gray-600 active:text-white\",\n                                children: isSearchMode ? \"Search\" : \"Subscribe\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 527,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            setIsSearchMode((prev)=>{\n                                                if (prev) {\n                                                    setLocalSearchTerm(\"\");\n                                                    setSearchTerm(\"\"); // Clear parent search when toggling back\n                                                }\n                                                return !prev;\n                                            });\n                                        },\n                                        onMouseEnter: ()=>setShowTooltip(true),\n                                        onMouseLeave: ()=>setShowTooltip(false),\n                                        className: \"flex items-center justify-center px-4 border-l border-black hover:bg-gray-100 transition-colors\",\n                                        style: {\n                                            minWidth: \"56px\"\n                                        },\n                                        children: isSearchMode ? // Mail/Envelope Icon\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            width: \"22\",\n                                            height: \"22\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                    x: \"3\",\n                                                    y: \"5\",\n                                                    width: \"18\",\n                                                    height: \"14\",\n                                                    rx: \"2\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                    points: \"3,7 12,13 21,7\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 17\n                                        }, undefined) : // Search Icon\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            width: \"22\",\n                                            height: \"22\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"11\",\n                                                    cy: \"11\",\n                                                    r: \"8\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    fill: \"none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"21\",\n                                                    y1: \"21\",\n                                                    x2: \"16.65\",\n                                                    y2: \"16.65\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showTooltip && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-10 left-1/2 -translate-x-1/2 bg-black text-white text-xs rounded px-3 py-1 shadow-lg animate-fade-in z-10 whitespace-nowrap\",\n                                        children: isSearchMode ? \"Switch to Subscribe mode\" : \"Switch to Search mode\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 533,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 511,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 508,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n        lineNumber: 249,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Header, \"iexpWvhRv7F7lDRJbJ4iOk3P4RQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = Header;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Header);\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./Components/Header.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./Components/PaperRocketAnimation.jsx":
/*!*********************************************!*\
  !*** ./Components/PaperRocketAnimation.jsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst PaperRocketAnimation = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-92b25fa310b88918\" + \" \" + \"fixed inset-0 pointer-events-none z-10 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-92b25fa310b88918\" + \" \" + \"rocket-container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    width: \"40\",\n                    height: \"40\",\n                    viewBox: \"0 0 100 100\",\n                    fill: \"none\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"jsx-92b25fa310b88918\" + \" \" + \"rocket\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M50 10 L60 70 L50 75 L40 70 Z\",\n                            fill: \"#000000\",\n                            stroke: \"#333333\",\n                            strokeWidth: \"1\",\n                            className: \"jsx-92b25fa310b88918\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M50 10 L45 25 L55 25 Z\",\n                            fill: \"#000000\",\n                            className: \"jsx-92b25fa310b88918\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M40 70 L35 80 L40 75 Z\",\n                            fill: \"#000000\",\n                            className: \"jsx-92b25fa310b88918\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M60 70 L65 80 L60 75 Z\",\n                            fill: \"#000000\",\n                            className: \"jsx-92b25fa310b88918\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"50\",\n                            cy: \"35\",\n                            r: \"6\",\n                            fill: \"#ffffff\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"1\",\n                            className: \"jsx-92b25fa310b88918\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                            x: \"47\",\n                            y: \"45\",\n                            width: \"6\",\n                            height: \"15\",\n                            fill: \"#333333\",\n                            className: \"jsx-92b25fa310b88918\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M45 75 L50 85 L55 75\",\n                            fill: \"#ff6b35\",\n                            opacity: \"0.8\",\n                            className: \"jsx-92b25fa310b88918\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M47 75 L50 82 L53 75\",\n                            fill: \"#ffaa00\",\n                            opacity: \"0.9\",\n                            className: \"jsx-92b25fa310b88918\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"92b25fa310b88918\",\n                children: '.rocket-container.jsx-92b25fa310b88918{position:absolute;-webkit-animation:rocketFlight 15s linear infinite;-moz-animation:rocketFlight 15s linear infinite;-o-animation:rocketFlight 15s linear infinite;animation:rocketFlight 15s linear infinite}.rocket.jsx-92b25fa310b88918{-webkit-transform-origin:center;-moz-transform-origin:center;-ms-transform-origin:center;-o-transform-origin:center;transform-origin:center;-webkit-filter:drop-shadow(2px 2px 4px rgba(0,0,0,.3));filter:drop-shadow(2px 2px 4px rgba(0,0,0,.3))}@-webkit-keyframes rocketFlight{0%{top:50%;left:-60px;-webkit-transform:translatey(-50%)rotate(45deg);transform:translatey(-50%)rotate(45deg)}25%{top:20%;left:25%;-webkit-transform:translatey(-50%)rotate(135deg);transform:translatey(-50%)rotate(135deg)}50%{top:50%;left:-webkit-calc(100% + 60px);left:calc(100% + 60px);-webkit-transform:translatey(-50%)rotate(225deg);transform:translatey(-50%)rotate(225deg)}75%{top:80%;left:75%;-webkit-transform:translatey(-50%)rotate(315deg);transform:translatey(-50%)rotate(315deg)}100%{top:50%;left:-60px;-webkit-transform:translatey(-50%)rotate(405deg);transform:translatey(-50%)rotate(405deg)}}@-moz-keyframes rocketFlight{0%{top:50%;left:-60px;-moz-transform:translatey(-50%)rotate(45deg);transform:translatey(-50%)rotate(45deg)}25%{top:20%;left:25%;-moz-transform:translatey(-50%)rotate(135deg);transform:translatey(-50%)rotate(135deg)}50%{top:50%;left:-moz-calc(100% + 60px);left:calc(100% + 60px);-moz-transform:translatey(-50%)rotate(225deg);transform:translatey(-50%)rotate(225deg)}75%{top:80%;left:75%;-moz-transform:translatey(-50%)rotate(315deg);transform:translatey(-50%)rotate(315deg)}100%{top:50%;left:-60px;-moz-transform:translatey(-50%)rotate(405deg);transform:translatey(-50%)rotate(405deg)}}@-o-keyframes rocketFlight{0%{top:50%;left:-60px;-o-transform:translatey(-50%)rotate(45deg);transform:translatey(-50%)rotate(45deg)}25%{top:20%;left:25%;-o-transform:translatey(-50%)rotate(135deg);transform:translatey(-50%)rotate(135deg)}50%{top:50%;left:calc(100% + 60px);-o-transform:translatey(-50%)rotate(225deg);transform:translatey(-50%)rotate(225deg)}75%{top:80%;left:75%;-o-transform:translatey(-50%)rotate(315deg);transform:translatey(-50%)rotate(315deg)}100%{top:50%;left:-60px;-o-transform:translatey(-50%)rotate(405deg);transform:translatey(-50%)rotate(405deg)}}@keyframes rocketFlight{0%{top:50%;left:-60px;-webkit-transform:translatey(-50%)rotate(45deg);-moz-transform:translatey(-50%)rotate(45deg);-o-transform:translatey(-50%)rotate(45deg);transform:translatey(-50%)rotate(45deg)}25%{top:20%;left:25%;-webkit-transform:translatey(-50%)rotate(135deg);-moz-transform:translatey(-50%)rotate(135deg);-o-transform:translatey(-50%)rotate(135deg);transform:translatey(-50%)rotate(135deg)}50%{top:50%;left:-webkit-calc(100% + 60px);left:-moz-calc(100% + 60px);left:calc(100% + 60px);-webkit-transform:translatey(-50%)rotate(225deg);-moz-transform:translatey(-50%)rotate(225deg);-o-transform:translatey(-50%)rotate(225deg);transform:translatey(-50%)rotate(225deg)}75%{top:80%;left:75%;-webkit-transform:translatey(-50%)rotate(315deg);-moz-transform:translatey(-50%)rotate(315deg);-o-transform:translatey(-50%)rotate(315deg);transform:translatey(-50%)rotate(315deg)}100%{top:50%;left:-60px;-webkit-transform:translatey(-50%)rotate(405deg);-moz-transform:translatey(-50%)rotate(405deg);-o-transform:translatey(-50%)rotate(405deg);transform:translatey(-50%)rotate(405deg)}}@media(max-width:768px){.rocket.jsx-92b25fa310b88918{width:30px;height:30px}@-webkit-keyframes rocketFlight{0%{top:60%;left:-50px;-webkit-transform:translatey(-50%)rotate(45deg);transform:translatey(-50%)rotate(45deg)}25%{top:30%;left:30%;-webkit-transform:translatey(-50%)rotate(135deg);transform:translatey(-50%)rotate(135deg)}50%{top:60%;left:-webkit-calc(100% + 50px);left:calc(100% + 50px);-webkit-transform:translatey(-50%)rotate(225deg);transform:translatey(-50%)rotate(225deg)}75%{top:70%;left:70%;-webkit-transform:translatey(-50%)rotate(315deg);transform:translatey(-50%)rotate(315deg)}100%{top:60%;left:-50px;-webkit-transform:translatey(-50%)rotate(405deg);transform:translatey(-50%)rotate(405deg)}}@-moz-keyframes rocketFlight{0%{top:60%;left:-50px;-moz-transform:translatey(-50%)rotate(45deg);transform:translatey(-50%)rotate(45deg)}25%{top:30%;left:30%;-moz-transform:translatey(-50%)rotate(135deg);transform:translatey(-50%)rotate(135deg)}50%{top:60%;left:-moz-calc(100% + 50px);left:calc(100% + 50px);-moz-transform:translatey(-50%)rotate(225deg);transform:translatey(-50%)rotate(225deg)}75%{top:70%;left:70%;-moz-transform:translatey(-50%)rotate(315deg);transform:translatey(-50%)rotate(315deg)}100%{top:60%;left:-50px;-moz-transform:translatey(-50%)rotate(405deg);transform:translatey(-50%)rotate(405deg)}}@-o-keyframes rocketFlight{0%{top:60%;left:-50px;-o-transform:translatey(-50%)rotate(45deg);transform:translatey(-50%)rotate(45deg)}25%{top:30%;left:30%;-o-transform:translatey(-50%)rotate(135deg);transform:translatey(-50%)rotate(135deg)}50%{top:60%;left:calc(100% + 50px);-o-transform:translatey(-50%)rotate(225deg);transform:translatey(-50%)rotate(225deg)}75%{top:70%;left:70%;-o-transform:translatey(-50%)rotate(315deg);transform:translatey(-50%)rotate(315deg)}100%{top:60%;left:-50px;-o-transform:translatey(-50%)rotate(405deg);transform:translatey(-50%)rotate(405deg)}}@keyframes rocketFlight{0%{top:60%;left:-50px;-webkit-transform:translatey(-50%)rotate(45deg);-moz-transform:translatey(-50%)rotate(45deg);-o-transform:translatey(-50%)rotate(45deg);transform:translatey(-50%)rotate(45deg)}25%{top:30%;left:30%;-webkit-transform:translatey(-50%)rotate(135deg);-moz-transform:translatey(-50%)rotate(135deg);-o-transform:translatey(-50%)rotate(135deg);transform:translatey(-50%)rotate(135deg)}50%{top:60%;left:-webkit-calc(100% + 50px);left:-moz-calc(100% + 50px);left:calc(100% + 50px);-webkit-transform:translatey(-50%)rotate(225deg);-moz-transform:translatey(-50%)rotate(225deg);-o-transform:translatey(-50%)rotate(225deg);transform:translatey(-50%)rotate(225deg)}75%{top:70%;left:70%;-webkit-transform:translatey(-50%)rotate(315deg);-moz-transform:translatey(-50%)rotate(315deg);-o-transform:translatey(-50%)rotate(315deg);transform:translatey(-50%)rotate(315deg)}100%{top:60%;left:-50px;-webkit-transform:translatey(-50%)rotate(405deg);-moz-transform:translatey(-50%)rotate(405deg);-o-transform:translatey(-50%)rotate(405deg);transform:translatey(-50%)rotate(405deg)}}}.rocket-container.jsx-92b25fa310b88918:hover{-webkit-animation-play-state:paused;-moz-animation-play-state:paused;-o-animation-play-state:paused;animation-play-state:paused}.rocket.jsx-92b25fa310b88918 path[fill=\"#ff6b35\"].jsx-92b25fa310b88918,.rocket.jsx-92b25fa310b88918 path[fill=\"#ffaa00\"].jsx-92b25fa310b88918{-webkit-animation:flameFlicker.3s ease-in-out infinite alternate;-moz-animation:flameFlicker.3s ease-in-out infinite alternate;-o-animation:flameFlicker.3s ease-in-out infinite alternate;animation:flameFlicker.3s ease-in-out infinite alternate}@-webkit-keyframes flameFlicker{0%{opacity:.6;-webkit-transform:scale(.9);transform:scale(.9)}100%{opacity:1;-webkit-transform:scale(1.1);transform:scale(1.1)}}@-moz-keyframes flameFlicker{0%{opacity:.6;-moz-transform:scale(.9);transform:scale(.9)}100%{opacity:1;-moz-transform:scale(1.1);transform:scale(1.1)}}@-o-keyframes flameFlicker{0%{opacity:.6;-o-transform:scale(.9);transform:scale(.9)}100%{opacity:1;-o-transform:scale(1.1);transform:scale(1.1)}}@keyframes flameFlicker{0%{opacity:.6;-webkit-transform:scale(.9);-moz-transform:scale(.9);-o-transform:scale(.9);transform:scale(.9)}100%{opacity:1;-webkit-transform:scale(1.1);-moz-transform:scale(1.1);-o-transform:scale(1.1);transform:scale(1.1)}}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PaperRocketAnimation;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PaperRocketAnimation);\nvar _c;\n$RefreshReg$(_c, \"PaperRocketAnimation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./Components/PaperRocketAnimation.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/styled-jsx/dist/index/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/styled-jsx/dist/index/index.js ***!
  \*****************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n__webpack_require__(/*! client-only */ \"(app-pages-browser)/./node_modules/next/dist/compiled/client-only/index.js\");\nvar React = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction _interopDefaultLegacy(e) {\n    return e && typeof e === \"object\" && \"default\" in e ? e : {\n        \"default\": e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefaultLegacy(React);\n_c = React__default;\n/*\nBased on Glamor's sheet\nhttps://github.com/threepointone/glamor/blob/667b480d31b3721a905021b26e1290ce92ca2879/src/sheet.js\n*/ function _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nvar isProd = typeof process !== \"undefined\" && process.env && \"development\" === \"production\";\nvar isString = function(o) {\n    return Object.prototype.toString.call(o) === \"[object String]\";\n};\nvar StyleSheet = /*#__PURE__*/ function() {\n    function StyleSheet(param) {\n        var ref = param === void 0 ? {} : param, _name = ref.name, name = _name === void 0 ? \"stylesheet\" : _name, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? isProd : _optimizeForSpeed;\n        invariant$1(isString(name), \"`name` must be a string\");\n        this._name = name;\n        this._deletedRulePlaceholder = \"#\" + name + \"-deleted-rule____{}\";\n        invariant$1(typeof optimizeForSpeed === \"boolean\", \"`optimizeForSpeed` must be a boolean\");\n        this._optimizeForSpeed = optimizeForSpeed;\n        this._serverSheet = undefined;\n        this._tags = [];\n        this._injected = false;\n        this._rulesCount = 0;\n        var node =  true && document.querySelector('meta[property=\"csp-nonce\"]');\n        this._nonce = node ? node.getAttribute(\"content\") : null;\n    }\n    var _proto = StyleSheet.prototype;\n    _proto.setOptimizeForSpeed = function setOptimizeForSpeed(bool) {\n        invariant$1(typeof bool === \"boolean\", \"`setOptimizeForSpeed` accepts a boolean\");\n        invariant$1(this._rulesCount === 0, \"optimizeForSpeed cannot be when rules have already been inserted\");\n        this.flush();\n        this._optimizeForSpeed = bool;\n        this.inject();\n    };\n    _proto.isOptimizeForSpeed = function isOptimizeForSpeed() {\n        return this._optimizeForSpeed;\n    };\n    _proto.inject = function inject() {\n        var _this = this;\n        invariant$1(!this._injected, \"sheet already injected\");\n        this._injected = true;\n        if ( true && this._optimizeForSpeed) {\n            this._tags[0] = this.makeStyleTag(this._name);\n            this._optimizeForSpeed = \"insertRule\" in this.getSheet();\n            if (!this._optimizeForSpeed) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: optimizeForSpeed mode not supported falling back to standard mode.\");\n                }\n                this.flush();\n                this._injected = true;\n            }\n            return;\n        }\n        this._serverSheet = {\n            cssRules: [],\n            insertRule: function(rule, index) {\n                if (typeof index === \"number\") {\n                    _this._serverSheet.cssRules[index] = {\n                        cssText: rule\n                    };\n                } else {\n                    _this._serverSheet.cssRules.push({\n                        cssText: rule\n                    });\n                }\n                return index;\n            },\n            deleteRule: function(index) {\n                _this._serverSheet.cssRules[index] = null;\n            }\n        };\n    };\n    _proto.getSheetForTag = function getSheetForTag(tag) {\n        if (tag.sheet) {\n            return tag.sheet;\n        }\n        // this weirdness brought to you by firefox\n        for(var i = 0; i < document.styleSheets.length; i++){\n            if (document.styleSheets[i].ownerNode === tag) {\n                return document.styleSheets[i];\n            }\n        }\n    };\n    _proto.getSheet = function getSheet() {\n        return this.getSheetForTag(this._tags[this._tags.length - 1]);\n    };\n    _proto.insertRule = function insertRule(rule, index) {\n        invariant$1(isString(rule), \"`insertRule` accepts only strings\");\n        if (false) {}\n        if (this._optimizeForSpeed) {\n            var sheet = this.getSheet();\n            if (typeof index !== \"number\") {\n                index = sheet.cssRules.length;\n            }\n            // this weirdness for perf, and chrome's weird bug\n            // https://stackoverflow.com/questions/20007992/chrome-suddenly-stopped-accepting-insertrule\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                return -1;\n            }\n        } else {\n            var insertionPoint = this._tags[index];\n            this._tags.push(this.makeStyleTag(this._name, rule, insertionPoint));\n        }\n        return this._rulesCount++;\n    };\n    _proto.replaceRule = function replaceRule(index, rule) {\n        if (this._optimizeForSpeed || \"object\" === \"undefined\") {\n            var sheet =  true ? this.getSheet() : 0;\n            if (!rule.trim()) {\n                rule = this._deletedRulePlaceholder;\n            }\n            if (!sheet.cssRules[index]) {\n                // @TBD Should we throw an error?\n                return index;\n            }\n            sheet.deleteRule(index);\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                // In order to preserve the indices we insert a deleteRulePlaceholder\n                sheet.insertRule(this._deletedRulePlaceholder, index);\n            }\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"old rule at index `\" + index + \"` not found\");\n            tag.textContent = rule;\n        }\n        return index;\n    };\n    _proto.deleteRule = function deleteRule(index) {\n        if (false) {}\n        if (this._optimizeForSpeed) {\n            this.replaceRule(index, \"\");\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"rule at index `\" + index + \"` not found\");\n            tag.parentNode.removeChild(tag);\n            this._tags[index] = null;\n        }\n    };\n    _proto.flush = function flush() {\n        this._injected = false;\n        this._rulesCount = 0;\n        if (true) {\n            this._tags.forEach(function(tag) {\n                return tag && tag.parentNode.removeChild(tag);\n            });\n            this._tags = [];\n        } else {}\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        if (false) {}\n        return this._tags.reduce(function(rules, tag) {\n            if (tag) {\n                rules = rules.concat(Array.prototype.map.call(_this.getSheetForTag(tag).cssRules, function(rule) {\n                    return rule.cssText === _this._deletedRulePlaceholder ? null : rule;\n                }));\n            } else {\n                rules.push(null);\n            }\n            return rules;\n        }, []);\n    };\n    _proto.makeStyleTag = function makeStyleTag(name, cssString, relativeToTag) {\n        if (cssString) {\n            invariant$1(isString(cssString), \"makeStyleTag accepts only strings as second parameter\");\n        }\n        var tag = document.createElement(\"style\");\n        if (this._nonce) tag.setAttribute(\"nonce\", this._nonce);\n        tag.type = \"text/css\";\n        tag.setAttribute(\"data-\" + name, \"\");\n        if (cssString) {\n            tag.appendChild(document.createTextNode(cssString));\n        }\n        var head = document.head || document.getElementsByTagName(\"head\")[0];\n        if (relativeToTag) {\n            head.insertBefore(tag, relativeToTag);\n        } else {\n            head.appendChild(tag);\n        }\n        return tag;\n    };\n    _createClass(StyleSheet, [\n        {\n            key: \"length\",\n            get: function get() {\n                return this._rulesCount;\n            }\n        }\n    ]);\n    return StyleSheet;\n}();\nfunction invariant$1(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheet: \" + message + \".\");\n    }\n}\nfunction hash(str) {\n    var _$hash = 5381, i = str.length;\n    while(i){\n        _$hash = _$hash * 33 ^ str.charCodeAt(--i);\n    }\n    /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */ return _$hash >>> 0;\n}\nvar stringHash = hash;\nvar sanitize = function(rule) {\n    return rule.replace(/\\/style/gi, \"\\\\/style\");\n};\nvar cache = {};\n/**\n * computeId\n *\n * Compute and memoize a jsx id from a basedId and optionally props.\n */ function computeId(baseId, props) {\n    if (!props) {\n        return \"jsx-\" + baseId;\n    }\n    var propsToString = String(props);\n    var key = baseId + propsToString;\n    if (!cache[key]) {\n        cache[key] = \"jsx-\" + stringHash(baseId + \"-\" + propsToString);\n    }\n    return cache[key];\n}\n/**\n * computeSelector\n *\n * Compute and memoize dynamic selectors.\n */ function computeSelector(id, css) {\n    var selectoPlaceholderRegexp = /__jsx-style-dynamic-selector/g;\n    // Sanitize SSR-ed CSS.\n    // Client side code doesn't need to be sanitized since we use\n    // document.createTextNode (dev) and the CSSOM api sheet.insertRule (prod).\n    if (false) {}\n    var idcss = id + css;\n    if (!cache[idcss]) {\n        cache[idcss] = css.replace(selectoPlaceholderRegexp, id);\n    }\n    return cache[idcss];\n}\nfunction mapRulesToStyle(cssRules, options) {\n    if (options === void 0) options = {};\n    return cssRules.map(function(args) {\n        var id = args[0];\n        var css = args[1];\n        return /*#__PURE__*/ React__default[\"default\"].createElement(\"style\", {\n            id: \"__\" + id,\n            // Avoid warnings upon render with a key\n            key: \"__\" + id,\n            nonce: options.nonce ? options.nonce : undefined,\n            dangerouslySetInnerHTML: {\n                __html: css\n            }\n        });\n    });\n}\nvar StyleSheetRegistry = /*#__PURE__*/ function() {\n    function StyleSheetRegistry(param) {\n        var ref = param === void 0 ? {} : param, _styleSheet = ref.styleSheet, styleSheet = _styleSheet === void 0 ? null : _styleSheet, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? false : _optimizeForSpeed;\n        this._sheet = styleSheet || new StyleSheet({\n            name: \"styled-jsx\",\n            optimizeForSpeed: optimizeForSpeed\n        });\n        this._sheet.inject();\n        if (styleSheet && typeof optimizeForSpeed === \"boolean\") {\n            this._sheet.setOptimizeForSpeed(optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    }\n    var _proto = StyleSheetRegistry.prototype;\n    _proto.add = function add(props) {\n        var _this = this;\n        if (undefined === this._optimizeForSpeed) {\n            this._optimizeForSpeed = Array.isArray(props.children);\n            this._sheet.setOptimizeForSpeed(this._optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        if ( true && !this._fromServer) {\n            this._fromServer = this.selectFromServer();\n            this._instancesCounts = Object.keys(this._fromServer).reduce(function(acc, tagName) {\n                acc[tagName] = 0;\n                return acc;\n            }, {});\n        }\n        var ref = this.getIdAndRules(props), styleId = ref.styleId, rules = ref.rules;\n        // Deduping: just increase the instances count.\n        if (styleId in this._instancesCounts) {\n            this._instancesCounts[styleId] += 1;\n            return;\n        }\n        var indices = rules.map(function(rule) {\n            return _this._sheet.insertRule(rule);\n        }) // Filter out invalid rules\n        .filter(function(index) {\n            return index !== -1;\n        });\n        this._indices[styleId] = indices;\n        this._instancesCounts[styleId] = 1;\n    };\n    _proto.remove = function remove(props) {\n        var _this = this;\n        var styleId = this.getIdAndRules(props).styleId;\n        invariant(styleId in this._instancesCounts, \"styleId: `\" + styleId + \"` not found\");\n        this._instancesCounts[styleId] -= 1;\n        if (this._instancesCounts[styleId] < 1) {\n            var tagFromServer = this._fromServer && this._fromServer[styleId];\n            if (tagFromServer) {\n                tagFromServer.parentNode.removeChild(tagFromServer);\n                delete this._fromServer[styleId];\n            } else {\n                this._indices[styleId].forEach(function(index) {\n                    return _this._sheet.deleteRule(index);\n                });\n                delete this._indices[styleId];\n            }\n            delete this._instancesCounts[styleId];\n        }\n    };\n    _proto.update = function update(props, nextProps) {\n        this.add(nextProps);\n        this.remove(props);\n    };\n    _proto.flush = function flush() {\n        this._sheet.flush();\n        this._sheet.inject();\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        var fromServer = this._fromServer ? Object.keys(this._fromServer).map(function(styleId) {\n            return [\n                styleId,\n                _this._fromServer[styleId]\n            ];\n        }) : [];\n        var cssRules = this._sheet.cssRules();\n        return fromServer.concat(Object.keys(this._indices).map(function(styleId) {\n            return [\n                styleId,\n                _this._indices[styleId].map(function(index) {\n                    return cssRules[index].cssText;\n                }).join(_this._optimizeForSpeed ? \"\" : \"\\n\")\n            ];\n        }) // filter out empty rules\n        .filter(function(rule) {\n            return Boolean(rule[1]);\n        }));\n    };\n    _proto.styles = function styles(options) {\n        return mapRulesToStyle(this.cssRules(), options);\n    };\n    _proto.getIdAndRules = function getIdAndRules(props) {\n        var css = props.children, dynamic = props.dynamic, id = props.id;\n        if (dynamic) {\n            var styleId = computeId(id, dynamic);\n            return {\n                styleId: styleId,\n                rules: Array.isArray(css) ? css.map(function(rule) {\n                    return computeSelector(styleId, rule);\n                }) : [\n                    computeSelector(styleId, css)\n                ]\n            };\n        }\n        return {\n            styleId: computeId(id),\n            rules: Array.isArray(css) ? css : [\n                css\n            ]\n        };\n    };\n    /**\n   * selectFromServer\n   *\n   * Collects style tags from the document with id __jsx-XXX\n   */ _proto.selectFromServer = function selectFromServer() {\n        var elements = Array.prototype.slice.call(document.querySelectorAll('[id^=\"__jsx-\"]'));\n        return elements.reduce(function(acc, element) {\n            var id = element.id.slice(2);\n            acc[id] = element;\n            return acc;\n        }, {});\n    };\n    return StyleSheetRegistry;\n}();\nfunction invariant(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheetRegistry: \" + message + \".\");\n    }\n}\nvar StyleSheetContext = /*#__PURE__*/ React.createContext(null);\nStyleSheetContext.displayName = \"StyleSheetContext\";\nfunction createStyleRegistry() {\n    return new StyleSheetRegistry();\n}\nfunction StyleRegistry(param) {\n    _s();\n    var configuredRegistry = param.registry, children = param.children;\n    var rootRegistry = React.useContext(StyleSheetContext);\n    var ref = React.useState(function() {\n        return rootRegistry || configuredRegistry || createStyleRegistry();\n    }), registry = ref[0];\n    return /*#__PURE__*/ React__default[\"default\"].createElement(StyleSheetContext.Provider, {\n        value: registry\n    }, children);\n}\n_s(StyleRegistry, \"vgRS4YV7PcSMQCYHzGaNuBIBcZQ=\");\n_c1 = StyleRegistry;\nfunction useStyleRegistry() {\n    _s1();\n    return React.useContext(StyleSheetContext);\n}\n_s1(useStyleRegistry, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n// Opt-into the new `useInsertionEffect` API in React 18, fallback to `useLayoutEffect`.\n// https://github.com/reactwg/react-18/discussions/110\nvar useInsertionEffect = React__default[\"default\"].useInsertionEffect || React__default[\"default\"].useLayoutEffect;\nvar defaultRegistry =  true ? createStyleRegistry() : 0;\nfunction JSXStyle(props) {\n    _s2();\n    var registry = defaultRegistry ? defaultRegistry : useStyleRegistry();\n    // If `registry` does not exist, we do nothing here.\n    if (!registry) {\n        return null;\n    }\n    if (false) {}\n    useInsertionEffect(function() {\n        registry.add(props);\n        return function() {\n            registry.remove(props);\n        };\n    // props.children can be string[], will be striped since id is identical\n    }, [\n        props.id,\n        String(props.dynamic)\n    ]);\n    return null;\n}\n_s2(JSXStyle, \"48Sqj1BUqkshsPdz6NEWXDn8pF4=\", false, function() {\n    return [\n        useStyleRegistry,\n        useInsertionEffect\n    ];\n});\n_c2 = JSXStyle;\nJSXStyle.dynamic = function(info) {\n    return info.map(function(tagInfo) {\n        var baseId = tagInfo[0];\n        var props = tagInfo[1];\n        return computeId(baseId, props);\n    }).join(\" \");\n};\nexports.StyleRegistry = StyleRegistry;\nexports.createStyleRegistry = createStyleRegistry;\nexports.style = JSXStyle;\nexports.useStyleRegistry = useStyleRegistry;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"React__default\");\n$RefreshReg$(_c1, \"StyleRegistry\");\n$RefreshReg$(_c2, \"JSXStyle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/styled-jsx/dist/index/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/styled-jsx/style.js":
/*!******************************************!*\
  !*** ./node_modules/styled-jsx/style.js ***!
  \******************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/index */ \"(app-pages-browser)/./node_modules/styled-jsx/dist/index/index.js\").style;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9zdHlsZWQtanN4L3N0eWxlLmpzIiwibWFwcGluZ3MiOiJBQUFBQSxtSUFBOEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL3N0eWxlZC1qc3gvc3R5bGUuanM/MzcwYiJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGlzdC9pbmRleCcpLnN0eWxlXG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsInJlcXVpcmUiLCJzdHlsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/styled-jsx/style.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/client-only/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/compiled/client-only/index.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {



/***/ })

});
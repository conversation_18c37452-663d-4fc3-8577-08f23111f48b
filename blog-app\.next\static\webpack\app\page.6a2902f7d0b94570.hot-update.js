"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./Components/PaperRocketAnimation.jsx":
/*!*********************************************!*\
  !*** ./Components/PaperRocketAnimation.jsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst StickManAnimation = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-92b25fa310b88918\" + \" \" + \"fixed inset-0 pointer-events-none z-10 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-92b25fa310b88918\" + \" \" + \"stickman-container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    width: \"50\",\n                    height: \"60\",\n                    viewBox: \"0 0 100 120\",\n                    fill: \"none\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"jsx-92b25fa310b88918\" + \" \" + \"stickman\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"50\",\n                            cy: \"15\",\n                            r: \"8\",\n                            fill: \"none\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"3\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-92b25fa310b88918\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"50\",\n                            y1: \"23\",\n                            x2: \"50\",\n                            y2: \"70\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"3\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-92b25fa310b88918\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"50\",\n                            y1: \"35\",\n                            x2: \"30\",\n                            y2: \"50\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"3\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-92b25fa310b88918\" + \" \" + \"left-arm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"50\",\n                            y1: \"35\",\n                            x2: \"70\",\n                            y2: \"50\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"3\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-92b25fa310b88918\" + \" \" + \"right-arm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"50\",\n                            y1: \"70\",\n                            x2: \"35\",\n                            y2: \"95\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"3\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-92b25fa310b88918\" + \" \" + \"left-leg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"50\",\n                            y1: \"70\",\n                            x2: \"65\",\n                            y2: \"95\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"3\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-92b25fa310b88918\" + \" \" + \"right-leg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"46\",\n                            cy: \"12\",\n                            r: \"1.5\",\n                            fill: \"#000000\",\n                            className: \"jsx-92b25fa310b88918\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"54\",\n                            cy: \"12\",\n                            r: \"1.5\",\n                            fill: \"#000000\",\n                            className: \"jsx-92b25fa310b88918\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M 45 17 Q 50 20 55 17\",\n                            stroke: \"#000000\",\n                            strokeWidth: \"1.5\",\n                            fill: \"none\",\n                            strokeLinecap: \"round\",\n                            className: \"jsx-92b25fa310b88918\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"92b25fa310b88918\",\n                children: '.rocket-container.jsx-92b25fa310b88918{position:absolute;-webkit-animation:rocketFlight 15s linear infinite;-moz-animation:rocketFlight 15s linear infinite;-o-animation:rocketFlight 15s linear infinite;animation:rocketFlight 15s linear infinite}.rocket.jsx-92b25fa310b88918{-webkit-transform-origin:center;-moz-transform-origin:center;-ms-transform-origin:center;-o-transform-origin:center;transform-origin:center;-webkit-filter:drop-shadow(2px 2px 4px rgba(0,0,0,.3));filter:drop-shadow(2px 2px 4px rgba(0,0,0,.3))}@-webkit-keyframes rocketFlight{0%{top:50%;left:-60px;-webkit-transform:translatey(-50%)rotate(45deg);transform:translatey(-50%)rotate(45deg)}25%{top:20%;left:25%;-webkit-transform:translatey(-50%)rotate(135deg);transform:translatey(-50%)rotate(135deg)}50%{top:50%;left:-webkit-calc(100% + 60px);left:calc(100% + 60px);-webkit-transform:translatey(-50%)rotate(225deg);transform:translatey(-50%)rotate(225deg)}75%{top:80%;left:75%;-webkit-transform:translatey(-50%)rotate(315deg);transform:translatey(-50%)rotate(315deg)}100%{top:50%;left:-60px;-webkit-transform:translatey(-50%)rotate(405deg);transform:translatey(-50%)rotate(405deg)}}@-moz-keyframes rocketFlight{0%{top:50%;left:-60px;-moz-transform:translatey(-50%)rotate(45deg);transform:translatey(-50%)rotate(45deg)}25%{top:20%;left:25%;-moz-transform:translatey(-50%)rotate(135deg);transform:translatey(-50%)rotate(135deg)}50%{top:50%;left:-moz-calc(100% + 60px);left:calc(100% + 60px);-moz-transform:translatey(-50%)rotate(225deg);transform:translatey(-50%)rotate(225deg)}75%{top:80%;left:75%;-moz-transform:translatey(-50%)rotate(315deg);transform:translatey(-50%)rotate(315deg)}100%{top:50%;left:-60px;-moz-transform:translatey(-50%)rotate(405deg);transform:translatey(-50%)rotate(405deg)}}@-o-keyframes rocketFlight{0%{top:50%;left:-60px;-o-transform:translatey(-50%)rotate(45deg);transform:translatey(-50%)rotate(45deg)}25%{top:20%;left:25%;-o-transform:translatey(-50%)rotate(135deg);transform:translatey(-50%)rotate(135deg)}50%{top:50%;left:calc(100% + 60px);-o-transform:translatey(-50%)rotate(225deg);transform:translatey(-50%)rotate(225deg)}75%{top:80%;left:75%;-o-transform:translatey(-50%)rotate(315deg);transform:translatey(-50%)rotate(315deg)}100%{top:50%;left:-60px;-o-transform:translatey(-50%)rotate(405deg);transform:translatey(-50%)rotate(405deg)}}@keyframes rocketFlight{0%{top:50%;left:-60px;-webkit-transform:translatey(-50%)rotate(45deg);-moz-transform:translatey(-50%)rotate(45deg);-o-transform:translatey(-50%)rotate(45deg);transform:translatey(-50%)rotate(45deg)}25%{top:20%;left:25%;-webkit-transform:translatey(-50%)rotate(135deg);-moz-transform:translatey(-50%)rotate(135deg);-o-transform:translatey(-50%)rotate(135deg);transform:translatey(-50%)rotate(135deg)}50%{top:50%;left:-webkit-calc(100% + 60px);left:-moz-calc(100% + 60px);left:calc(100% + 60px);-webkit-transform:translatey(-50%)rotate(225deg);-moz-transform:translatey(-50%)rotate(225deg);-o-transform:translatey(-50%)rotate(225deg);transform:translatey(-50%)rotate(225deg)}75%{top:80%;left:75%;-webkit-transform:translatey(-50%)rotate(315deg);-moz-transform:translatey(-50%)rotate(315deg);-o-transform:translatey(-50%)rotate(315deg);transform:translatey(-50%)rotate(315deg)}100%{top:50%;left:-60px;-webkit-transform:translatey(-50%)rotate(405deg);-moz-transform:translatey(-50%)rotate(405deg);-o-transform:translatey(-50%)rotate(405deg);transform:translatey(-50%)rotate(405deg)}}@media(max-width:768px){.rocket.jsx-92b25fa310b88918{width:30px;height:30px}@-webkit-keyframes rocketFlight{0%{top:60%;left:-50px;-webkit-transform:translatey(-50%)rotate(45deg);transform:translatey(-50%)rotate(45deg)}25%{top:30%;left:30%;-webkit-transform:translatey(-50%)rotate(135deg);transform:translatey(-50%)rotate(135deg)}50%{top:60%;left:-webkit-calc(100% + 50px);left:calc(100% + 50px);-webkit-transform:translatey(-50%)rotate(225deg);transform:translatey(-50%)rotate(225deg)}75%{top:70%;left:70%;-webkit-transform:translatey(-50%)rotate(315deg);transform:translatey(-50%)rotate(315deg)}100%{top:60%;left:-50px;-webkit-transform:translatey(-50%)rotate(405deg);transform:translatey(-50%)rotate(405deg)}}@-moz-keyframes rocketFlight{0%{top:60%;left:-50px;-moz-transform:translatey(-50%)rotate(45deg);transform:translatey(-50%)rotate(45deg)}25%{top:30%;left:30%;-moz-transform:translatey(-50%)rotate(135deg);transform:translatey(-50%)rotate(135deg)}50%{top:60%;left:-moz-calc(100% + 50px);left:calc(100% + 50px);-moz-transform:translatey(-50%)rotate(225deg);transform:translatey(-50%)rotate(225deg)}75%{top:70%;left:70%;-moz-transform:translatey(-50%)rotate(315deg);transform:translatey(-50%)rotate(315deg)}100%{top:60%;left:-50px;-moz-transform:translatey(-50%)rotate(405deg);transform:translatey(-50%)rotate(405deg)}}@-o-keyframes rocketFlight{0%{top:60%;left:-50px;-o-transform:translatey(-50%)rotate(45deg);transform:translatey(-50%)rotate(45deg)}25%{top:30%;left:30%;-o-transform:translatey(-50%)rotate(135deg);transform:translatey(-50%)rotate(135deg)}50%{top:60%;left:calc(100% + 50px);-o-transform:translatey(-50%)rotate(225deg);transform:translatey(-50%)rotate(225deg)}75%{top:70%;left:70%;-o-transform:translatey(-50%)rotate(315deg);transform:translatey(-50%)rotate(315deg)}100%{top:60%;left:-50px;-o-transform:translatey(-50%)rotate(405deg);transform:translatey(-50%)rotate(405deg)}}@keyframes rocketFlight{0%{top:60%;left:-50px;-webkit-transform:translatey(-50%)rotate(45deg);-moz-transform:translatey(-50%)rotate(45deg);-o-transform:translatey(-50%)rotate(45deg);transform:translatey(-50%)rotate(45deg)}25%{top:30%;left:30%;-webkit-transform:translatey(-50%)rotate(135deg);-moz-transform:translatey(-50%)rotate(135deg);-o-transform:translatey(-50%)rotate(135deg);transform:translatey(-50%)rotate(135deg)}50%{top:60%;left:-webkit-calc(100% + 50px);left:-moz-calc(100% + 50px);left:calc(100% + 50px);-webkit-transform:translatey(-50%)rotate(225deg);-moz-transform:translatey(-50%)rotate(225deg);-o-transform:translatey(-50%)rotate(225deg);transform:translatey(-50%)rotate(225deg)}75%{top:70%;left:70%;-webkit-transform:translatey(-50%)rotate(315deg);-moz-transform:translatey(-50%)rotate(315deg);-o-transform:translatey(-50%)rotate(315deg);transform:translatey(-50%)rotate(315deg)}100%{top:60%;left:-50px;-webkit-transform:translatey(-50%)rotate(405deg);-moz-transform:translatey(-50%)rotate(405deg);-o-transform:translatey(-50%)rotate(405deg);transform:translatey(-50%)rotate(405deg)}}}.rocket-container.jsx-92b25fa310b88918:hover{-webkit-animation-play-state:paused;-moz-animation-play-state:paused;-o-animation-play-state:paused;animation-play-state:paused}.rocket.jsx-92b25fa310b88918 path[fill=\"#ff6b35\"].jsx-92b25fa310b88918,.rocket.jsx-92b25fa310b88918 path[fill=\"#ffaa00\"].jsx-92b25fa310b88918{-webkit-animation:flameFlicker.3s ease-in-out infinite alternate;-moz-animation:flameFlicker.3s ease-in-out infinite alternate;-o-animation:flameFlicker.3s ease-in-out infinite alternate;animation:flameFlicker.3s ease-in-out infinite alternate}@-webkit-keyframes flameFlicker{0%{opacity:.6;-webkit-transform:scale(.9);transform:scale(.9)}100%{opacity:1;-webkit-transform:scale(1.1);transform:scale(1.1)}}@-moz-keyframes flameFlicker{0%{opacity:.6;-moz-transform:scale(.9);transform:scale(.9)}100%{opacity:1;-moz-transform:scale(1.1);transform:scale(1.1)}}@-o-keyframes flameFlicker{0%{opacity:.6;-o-transform:scale(.9);transform:scale(.9)}100%{opacity:1;-o-transform:scale(1.1);transform:scale(1.1)}}@keyframes flameFlicker{0%{opacity:.6;-webkit-transform:scale(.9);-moz-transform:scale(.9);-o-transform:scale(.9);transform:scale(.9)}100%{opacity:1;-webkit-transform:scale(1.1);-moz-transform:scale(1.1);-o-transform:scale(1.1);transform:scale(1.1)}}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\PaperRocketAnimation.jsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n_c = StickManAnimation;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PaperRocketAnimation);\nvar _c;\n$RefreshReg$(_c, \"StickManAnimation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./Components/PaperRocketAnimation.jsx\n"));

/***/ })

});
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[670],{7330:function(e,o,n){Promise.resolve().then(n.bind(n,4528))},4528:function(e,o,n){"use strict";n.r(o);var r=n(7437),t=n(2265),l=n(2173);o.default=()=>{let[e,o]=(0,t.useState)(null),[n,a]=(0,t.useState)(!0),[s,c]=(0,t.useState)(null);return(0,t.useEffect)(()=>{(async()=>{try{a(!0);let e=localStorage.getItem("authToken");if(!e){c("No token found in localStorage"),a(!1);return}console.log("Token from localStorage:",e);let n=await l.Z.get("/api/debug",{headers:{Authorization:"Bearer ".concat(e)}});o(n.data)}catch(o){var e,n;console.error("Token check error:",o),c((null===(n=o.response)||void 0===n?void 0:null===(e=n.data)||void 0===e?void 0:e.message)||"Failed to verify token")}finally{a(!1)}})()},[]),(0,r.jsxs)("div",{className:"container mx-auto p-8",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Token Debug Page"}),n?(0,r.jsx)("div",{children:"Checking token..."}):s?(0,r.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:[(0,r.jsx)("p",{className:"font-bold",children:"Error"}),(0,r.jsx)("p",{children:s})]}):e?(0,r.jsxs)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded",children:[(0,r.jsx)("p",{className:"font-bold",children:"Token is valid"}),(0,r.jsx)("pre",{className:"mt-2 bg-gray-100 p-4 rounded overflow-auto",children:JSON.stringify(e,null,2)})]}):(0,r.jsx)("div",{children:"No token information available"}),(0,r.jsxs)("div",{className:"mt-8",children:[(0,r.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Manual Token Check"}),(0,r.jsx)("button",{onClick:async()=>{let e=localStorage.getItem("authToken");console.log("Current token:",e);try{let o=await l.Z.get("/api/debug",{headers:{Authorization:"Bearer ".concat(e)}});console.log("Token check result:",o.data),alert("Token is valid. Check console for details.")}catch(e){console.error("Token check failed:",e),alert("Token check failed. Check console for details.")}},className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded",children:"Check Current Token"})]})]})}}},function(e){e.O(0,[580,971,938,744],function(){return e(e.s=7330)}),_N_E=e.O()}]);
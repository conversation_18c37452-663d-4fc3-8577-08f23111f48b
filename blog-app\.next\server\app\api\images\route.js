"use strict";(()=>{var e={};e.id=7312,e.ids=[7312],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},12319:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>h,originalPathname:()=>f,patchFetch:()=>v,requestAsyncStorage:()=>g,routeModule:()=>p,serverHooks:()=>m,staticGenerationAsyncStorage:()=>d,staticGenerationBailout:()=>y});var a={};r.r(a),r.d(a,{GET:()=>c});var o=r(95419),n=r(69108),s=r(99678),i=r(78070),l=r(91887),u=r(31833);async function c(e){try{await (0,l.n)();let t=e.nextUrl.searchParams.get("blogId"),r=parseInt(e.nextUrl.searchParams.get("page"))||1,a=parseInt(e.nextUrl.searchParams.get("limit"))||20,o=e.nextUrl.searchParams.get("search")||"",n={};t&&"all"!==t&&(n.blogId=t),o&&(n.filename={$regex:o,$options:"i"});let s=(r-1)*a,c=await u.Z.find(n).select("-data").sort({uploadDate:-1}).skip(s).limit(a),p=await u.Z.countDocuments(n),g=Math.ceil(p/a);return i.Z.json({success:!0,images:c,pagination:{currentPage:r,totalPages:g,totalImages:p,hasNextPage:r<g,hasPrevPage:r>1}})}catch(e){return console.error("Error retrieving images:",e),i.Z.json({success:!1,message:"Failed to retrieve images"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/images/route",pathname:"/api/images",filename:"route",bundlePath:"app/api/images/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\images\\route.js",nextConfigOutput:"",userland:a}),{requestAsyncStorage:g,staticGenerationAsyncStorage:d,serverHooks:m,headerHooks:h,staticGenerationBailout:y}=p,f="/api/images/route";function v(){return(0,s.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:d})}},91887:(e,t,r)=>{r.d(t,{n:()=>n});var a=r(11185),o=r.n(a);let n=async()=>{try{if(o().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await o().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},31833:(e,t,r)=>{r.d(t,{Z:()=>s});var a=r(11185),o=r.n(a);let n=new(o()).Schema({filename:{type:String,required:!0},path:{type:String,required:!0},url:{type:String,required:!0},contentType:{type:String,required:!0},size:{type:Number,required:!0},data:{type:Buffer,required:!0},blogId:{type:o().Schema.Types.Mixed,ref:"blog",default:null},uploadDate:{type:Date,default:Date.now}}),s=o().models.image||o().model("image",n)},78070:(e,t,r)=>{Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return a.NextResponse}});let a=r(70457)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,2993],()=>r(12319));module.exports=a})();
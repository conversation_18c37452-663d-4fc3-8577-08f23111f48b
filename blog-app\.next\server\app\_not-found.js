(()=>{var e={};e.id=9165,e.ids=[9165],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},46009:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(50482),o=r(69108),a=r(62563),i=r.n(a),l=r(68300),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(t,n);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,39459)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],d=[],u="/_not-found",p={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/_not-found",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},83338:(e,t,r)=>{Promise.resolve().then(r.bind(r,97211)),Promise.resolve().then(r.bind(r,98419)),Promise.resolve().then(r.bind(r,69697))},21416:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,26840,23)),Promise.resolve().then(r.t.bind(r,38771,23)),Promise.resolve().then(r.t.bind(r,13225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,43982,23))},97211:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(95344),o=r(3729),a=r(69697),i=r(8014);let l=(e,t,r={})=>{i.Z.set(e,t,r)},n=e=>i.Z.get(e),c=()=>"accepted"===i.Z.get("cookie-consent"),d=()=>{c()},u=()=>{c()},p=()=>{let[e,t]=(0,o.useState)(!1);return((0,o.useEffect)(()=>{let e=n("cookie-consent");if(e)"accepted"===e&&(d(),u());else{let e=setTimeout(()=>{t(!0)},1e4);return()=>clearTimeout(e)}},[]),e)?s.jsx("div",{className:"fixed bottom-0 left-0 right-0 bg-white shadow-lg z-50 border-t border-gray-200 p-4 md:p-6 animate-fade-in",children:s.jsx("div",{className:"max-w-6xl mx-auto",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-4",children:[(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("h3",{className:"text-lg font-semibold mb-2",children:"We use cookies"}),(0,s.jsxs)("p",{className:"text-gray-600 text-sm md:text-base",children:['We use cookies to enhance your browsing experience, serve personalized ads or content, and analyze our traffic. By clicking "Accept All", you consent to our use of cookies.',s.jsx("a",{href:"/privacy-policy",className:"text-blue-600 hover:underline ml-1",children:"Read our Cookie Policy"})]})]}),(0,s.jsxs)("div",{className:"flex gap-3",children:[s.jsx("button",{onClick:()=>{l("cookie-consent","declined",{expires:365}),t(!1),a.toast.info("Cookies declined. Some features may be limited.")},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 text-sm md:text-base",children:"Decline"}),s.jsx("button",{onClick:()=>{l("cookie-consent","accepted",{expires:365}),d(),u(),t(!1),a.toast.success("Cookie preferences saved")},className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 text-sm md:text-base",children:"Accept All"})]})]})})}):null}},98419:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(95344),o=r(3729),a=r(53608),i=r(69697),l=r(22254);let n=()=>{let[e,t]=(0,o.useState)(!1),[r,n]=(0,o.useState)(""),[c,d]=(0,o.useState)(!1),u=(0,l.usePathname)();(0,o.useEffect)(()=>{if(u&&u.startsWith("/admin")||"true"===localStorage.getItem("emailSubscribed")||"true"===localStorage.getItem("emailPopupPermanentlyDismissed"))return;let e=localStorage.getItem("emailPopupLastClosed"),r=parseInt(localStorage.getItem("emailPopupCloseCount")||"0"),s=Date.now();if(e&&r>=1&&s-parseInt(e)<3e5)return;let o=setTimeout(()=>{t(!0)},0===r?12e4:Math.max(0,3e5-(s-parseInt(e||"0"))));return()=>clearTimeout(o)},[u]);let p=async e=>{if(e.preventDefault(),!r){i.toast.error("Please enter your email address");return}try{d(!0);let e=new FormData;e.append("email",r),(await a.Z.post("/api/email",e)).data.success?(i.toast.success("Successfully subscribed to our newsletter!"),t(!1),n(""),localStorage.setItem("emailSubscribed","true"),localStorage.removeItem("emailPopupCloseCount"),localStorage.removeItem("emailPopupLastClosed"),localStorage.removeItem("emailPopupPermanentlyDismissed")):i.toast.error("Subscription failed. Please try again.")}catch(e){console.error("Subscription error:",e),i.toast.error("An error occurred. Please try again.")}finally{d(!1)}};return e?s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-2xl max-w-md w-full relative overflow-hidden",children:[s.jsx("button",{onClick:()=>{t(!1);let e=parseInt(localStorage.getItem("emailPopupCloseCount")||"0")+1;localStorage.setItem("emailPopupCloseCount",e.toString()),localStorage.setItem("emailPopupLastClosed",Date.now().toString()),e>=2&&localStorage.setItem("emailPopupPermanentlyDismissed","true")},className:"absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10","aria-label":"Close popup",children:s.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,s.jsxs)("div",{className:"p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"SUBSCRIBE NOW"}),(0,s.jsxs)("p",{className:"text-gray-600 text-sm",children:["DON'T MISS OUT ON THE LATEST BLOG POSTS",s.jsx("br",{}),"AND OFFERS."]}),s.jsx("p",{className:"text-xs text-gray-500 mt-2",children:"Be the first to get notified."})]}),(0,s.jsxs)("form",{onSubmit:p,className:"space-y-4",children:[s.jsx("div",{children:s.jsx("input",{type:"email",value:r,onChange:e=>n(e.target.value),placeholder:"Email address",className:"w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent",required:!0})}),s.jsx("button",{type:"submit",disabled:c,className:"w-full bg-black text-white py-3 px-6 rounded-md hover:bg-gray-800 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:c?"SUBSCRIBING...":"SUBSCRIBE"})]}),s.jsx("p",{className:"text-xs text-gray-500 text-center mt-4",children:"You can unsubscribe at any time. We respect your privacy."})]})]})}):null}},39459:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,metadata:()=>b});var s=r(25036),o=r(57495),a=r.n(o);r(67272);var i=r(86843);let l=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\Components\CookieConsent.jsx`),{__esModule:n,$$typeof:c}=l,d=l.default,u=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\Components\EmailSubscriptionPopup.jsx`),{__esModule:p,$$typeof:m}=u,x=u.default;var g=r(23222);r(97001);let b={title:"Mr.Blogger",description:"A blog platform by Mr.Blogger"};function h({children:e}){return(0,s.jsxs)("html",{lang:"en",children:[s.jsx("head",{children:s.jsx("link",{rel:"stylesheet",href:"/build/tailwind.css"})}),(0,s.jsxs)("body",{className:a().className,children:[e,s.jsx(d,{}),s.jsx(x,{}),s.jsx(g.Ix,{position:"top-center",autoClose:3e3})]})]})}},67272:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,3998],()=>r(46009));module.exports=s})();
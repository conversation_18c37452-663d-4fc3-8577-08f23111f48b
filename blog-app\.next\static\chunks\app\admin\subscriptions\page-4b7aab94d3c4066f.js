(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[296],{1670:function(e,t,n){Promise.resolve().then(n.bind(n,7212))},7212:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return i}});var a=n(7437),o=n(2265),s=e=>{let{email:t,mongoId:n,deleteEmail:o,date:s}=e,r=new Date(s);return(0,a.jsxs)("tr",{className:"bg-white border-b text-left",children:[(0,a.jsx)("th",{scope:"row",className:"px-6 py-4 font-medium text-gray-900 whitespace-nowrap",children:t||"No Email"}),(0,a.jsx)("td",{className:"px-6 py-4 hidden sm:block",children:r.toDateString()}),(0,a.jsx)("td",{className:"px-6 py-4 cursor-pointer",onClick:()=>o(n),children:"x"})]})},r=n(2173),l=n(7948),i=()=>{let[e,t]=(0,o.useState)([]),n=async()=>{t((await r.Z.get("/api/email")).data.emails)},i=async e=>{let t=await r.Z.delete("/api/email",{params:{id:e}});t.data.success?(l.toast.success(t.data.msg),n()):l.toast.error("Error")};return(0,o.useEffect)(()=>{n()},[]),(0,a.jsxs)("div",{className:"flex-1 pt-5 px-5 sm:pt-12 sm:pl-16",children:[(0,a.jsx)("h1",{children:"All Subscription"}),(0,a.jsx)("div",{className:"relative w-full h-[80vh] overflow-x-auto mt-4 border border-gray-400 scollbar-hide",children:(0,a.jsxs)("table",{className:"w-full text-sm text-gray-500",children:[(0,a.jsx)("thead",{className:"text-xs text-left text-gray-700 uppercase bg-gray-50 ",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Email Subscription"}),(0,a.jsx)("th",{scope:"col",className:"hidden sm:block px-6 py-3",children:"Date"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Action"})]})}),(0,a.jsx)("tbody",{children:e.map((e,t)=>(0,a.jsx)(s,{mongoId:e._id,deleteEmail:i,email:e.email,date:e.date},t))})]})})]})}},7948:function(e,t,n){"use strict";n.r(t),n.d(t,{Bounce:function(){return M},Flip:function(){return j},Icons:function(){return A},Slide:function(){return O},ToastContainer:function(){return z},Zoom:function(){return B},collapseToast:function(){return d},cssTransition:function(){return u},toast:function(){return k},useToast:function(){return _},useToastContainer:function(){return b}});var a=n(2265),o=function(){for(var e,t,n=0,a="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=function e(t){var n,a,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t)){var s=t.length;for(n=0;n<s;n++)t[n]&&(a=e(t[n]))&&(o&&(o+=" "),o+=a)}else for(a in t)t[a]&&(o&&(o+=" "),o+=a)}return o}(e))&&(a&&(a+=" "),a+=t);return a};let s=e=>"number"==typeof e&&!isNaN(e),r=e=>"string"==typeof e,l=e=>"function"==typeof e,i=e=>r(e)||l(e)?e:null,c=e=>(0,a.isValidElement)(e)||r(e)||l(e)||s(e);function d(e,t,n){void 0===n&&(n=300);let{scrollHeight:a,style:o}=e;requestAnimationFrame(()=>{o.minHeight="initial",o.height=a+"px",o.transition=`all ${n}ms`,requestAnimationFrame(()=>{o.height="0",o.padding="0",o.margin="0",setTimeout(t,n)})})}function u(e){let{enter:t,exit:n,appendPosition:o=!1,collapse:s=!0,collapseDuration:r=300}=e;return function(e){let{children:l,position:i,preventExitTransition:c,done:u,nodeRef:p,isIn:f,playToast:m}=e,g=o?`${t}--${i}`:t,y=o?`${n}--${i}`:n,h=(0,a.useRef)(0);return(0,a.useLayoutEffect)(()=>{let e=p.current,t=g.split(" "),n=a=>{a.target===p.current&&(m(),e.removeEventListener("animationend",n),e.removeEventListener("animationcancel",n),0===h.current&&"animationcancel"!==a.type&&e.classList.remove(...t))};e.classList.add(...t),e.addEventListener("animationend",n),e.addEventListener("animationcancel",n)},[]),(0,a.useEffect)(()=>{let e=p.current,t=()=>{e.removeEventListener("animationend",t),s?d(e,u,r):u()};f||(c?t():(h.current=1,e.className+=` ${y}`,e.addEventListener("animationend",t)))},[f]),a.createElement(a.Fragment,null,l)}}function p(e,t){return null!=e?{content:e.content,containerId:e.props.containerId,id:e.props.toastId,theme:e.props.theme,type:e.props.type,data:e.props.data||{},isLoading:e.props.isLoading,icon:e.props.icon,status:t}:{}}let f=new Map,m=[],g=new Set,y=e=>g.forEach(t=>t(e)),h=()=>f.size>0;function v(e,t){var n;if(t)return!(null==(n=f.get(t))||!n.isToastActive(e));let a=!1;return f.forEach(t=>{t.isToastActive(e)&&(a=!0)}),a}function E(e,t){c(e)&&(h()||m.push({content:e,options:t}),f.forEach(n=>{n.buildToast(e,t)}))}function T(e,t){f.forEach(n=>{null!=t&&null!=t&&t.containerId?(null==t?void 0:t.containerId)===n.id&&n.toggle(e,null==t?void 0:t.id):n.toggle(e,null==t?void 0:t.id)})}function b(e){let{subscribe:t,getSnapshot:n,setProps:o}=(0,a.useRef)(function(e){let t=e.containerId||1;return{subscribe(n){let o=function(e,t,n){let o=1,d=0,u=[],f=[],m=[],g=t,y=new Map,h=new Set,v=()=>{m=Array.from(y.values()),h.forEach(e=>e())},E=e=>{f=null==e?[]:f.filter(t=>t!==e),v()},T=e=>{let{toastId:t,onOpen:o,updateId:s,children:r}=e.props,i=null==s;e.staleId&&y.delete(e.staleId),y.set(t,e),f=[...f,e.props.toastId].filter(t=>t!==e.staleId),v(),n(p(e,i?"added":"updated")),i&&l(o)&&o((0,a.isValidElement)(r)&&r.props)};return{id:e,props:g,observe:e=>(h.add(e),()=>h.delete(e)),toggle:(e,t)=>{y.forEach(n=>{null!=t&&t!==n.props.toastId||l(n.toggle)&&n.toggle(e)})},removeToast:E,toasts:y,clearQueue:()=>{d-=u.length,u=[]},buildToast:(t,f)=>{var m,h;if((t=>{let{containerId:n,toastId:a,updateId:o}=t,s=y.has(a)&&null==o;return(n?n!==e:1!==e)||s})(f))return;let{toastId:b,updateId:_,data:I,staleId:x,delay:C}=f,N=()=>{E(b)},L=null==_;L&&d++;let w={...g,style:g.toastStyle,key:o++,...Object.fromEntries(Object.entries(f).filter(e=>{let[t,n]=e;return null!=n})),toastId:b,updateId:_,data:I,closeToast:N,isIn:!1,className:i(f.className||g.toastClassName),bodyClassName:i(f.bodyClassName||g.bodyClassName),progressClassName:i(f.progressClassName||g.progressClassName),autoClose:!f.isLoading&&(m=f.autoClose,h=g.autoClose,!1===m||s(m)&&m>0?m:h),deleteToast(){let e=y.get(b),{onClose:t,children:o}=e.props;l(t)&&t((0,a.isValidElement)(o)&&o.props),n(p(e,"removed")),y.delete(b),--d<0&&(d=0),u.length>0?T(u.shift()):v()}};w.closeButton=g.closeButton,!1===f.closeButton||c(f.closeButton)?w.closeButton=f.closeButton:!0===f.closeButton&&(w.closeButton=!c(g.closeButton)||g.closeButton);let k=t;(0,a.isValidElement)(t)&&!r(t.type)?k=(0,a.cloneElement)(t,{closeToast:N,toastProps:w,data:I}):l(t)&&(k=t({closeToast:N,toastProps:w,data:I}));let $={content:k,props:w,staleId:x};g.limit&&g.limit>0&&d>g.limit&&L?u.push($):s(C)?setTimeout(()=>{T($)},C):T($)},setProps(e){g=e},setToggle:(e,t)=>{y.get(e).toggle=t},isToastActive:e=>f.some(t=>t===e),getSnapshot:()=>g.newestOnTop?m.reverse():m}}(t,e,y);f.set(t,o);let d=o.observe(n);return m.forEach(e=>E(e.content,e.options)),m=[],()=>{d(),f.delete(t)}},setProps(e){var n;null==(n=f.get(t))||n.setProps(e)},getSnapshot(){var e;return null==(e=f.get(t))?void 0:e.getSnapshot()}}}(e)).current;o(e);let d=(0,a.useSyncExternalStore)(t,n,n);return{getToastToRender:function(e){if(!d)return[];let t=new Map;return d.forEach(e=>{let{position:n}=e.props;t.has(n)||t.set(n,[]),t.get(n).push(e)}),Array.from(t,t=>e(t[0],t[1]))},isToastActive:v,count:null==d?void 0:d.length}}function _(e){var t,n;let[o,s]=(0,a.useState)(!1),[r,l]=(0,a.useState)(!1),i=(0,a.useRef)(null),c=(0,a.useRef)({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:d,pauseOnHover:u,closeToast:p,onClick:m,closeOnClick:g}=e;function y(){s(!0)}function h(){s(!1)}function v(t){let n=i.current;c.canDrag&&n&&(c.didMove=!0,o&&h(),c.delta="x"===e.draggableDirection?t.clientX-c.start:t.clientY-c.start,c.start!==t.clientX&&(c.canCloseOnClick=!1),n.style.transform=`translate3d(${"x"===e.draggableDirection?`${c.delta}px, var(--y)`:`0, calc(${c.delta}px + var(--y))`},0)`,n.style.opacity=""+(1-Math.abs(c.delta/c.removalDistance)))}function E(){document.removeEventListener("pointermove",v),document.removeEventListener("pointerup",E);let t=i.current;if(c.canDrag&&c.didMove&&t){if(c.canDrag=!1,Math.abs(c.delta)>c.removalDistance)return l(!0),e.closeToast(),void e.collapseAll();t.style.transition="transform 0.2s, opacity 0.2s",t.style.removeProperty("transform"),t.style.removeProperty("opacity")}}null==(n=f.get((t={id:e.toastId,containerId:e.containerId,fn:s}).containerId||1))||n.setToggle(t.id,t.fn),(0,a.useEffect)(()=>{if(e.pauseOnFocusLoss)return document.hasFocus()||h(),window.addEventListener("focus",y),window.addEventListener("blur",h),()=>{window.removeEventListener("focus",y),window.removeEventListener("blur",h)}},[e.pauseOnFocusLoss]);let T={onPointerDown:function(t){if(!0===e.draggable||e.draggable===t.pointerType){c.didMove=!1,document.addEventListener("pointermove",v),document.addEventListener("pointerup",E);let n=i.current;c.canCloseOnClick=!0,c.canDrag=!0,n.style.transition="none","x"===e.draggableDirection?(c.start=t.clientX,c.removalDistance=n.offsetWidth*(e.draggablePercent/100)):(c.start=t.clientY,c.removalDistance=n.offsetHeight*(80===e.draggablePercent?1.5*e.draggablePercent:e.draggablePercent)/100)}},onPointerUp:function(t){let{top:n,bottom:a,left:o,right:s}=i.current.getBoundingClientRect();"touchend"!==t.nativeEvent.type&&e.pauseOnHover&&t.clientX>=o&&t.clientX<=s&&t.clientY>=n&&t.clientY<=a?h():y()}};return d&&u&&(T.onMouseEnter=h,e.stacked||(T.onMouseLeave=y)),g&&(T.onClick=e=>{m&&m(e),c.canCloseOnClick&&p()}),{playToast:y,pauseToast:h,isRunning:o,preventExitTransition:r,toastRef:i,eventHandlers:T}}function I(e){let{delay:t,isRunning:n,closeToast:s,type:r="default",hide:i,className:c,style:d,controlledProgress:u,progress:p,rtl:f,isIn:m,theme:g}=e,y=i||u&&0===p,h={...d,animationDuration:`${t}ms`,animationPlayState:n?"running":"paused"};u&&(h.transform=`scaleX(${p})`);let v=o("Toastify__progress-bar",u?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${g}`,`Toastify__progress-bar--${r}`,{"Toastify__progress-bar--rtl":f}),E=l(c)?c({rtl:f,type:r,defaultClassName:v}):o(v,c);return a.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":y},a.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${g} Toastify__progress-bar--${r}`}),a.createElement("div",{role:"progressbar","aria-hidden":y?"true":"false","aria-label":"notification timer",className:E,style:h,[u&&p>=1?"onTransitionEnd":"onAnimationEnd"]:u&&p<1?null:()=>{m&&s()}}))}let x=1,C=()=>""+x++;function N(e,t){return E(e,t),t.toastId}function L(e,t){return{...t,type:t&&t.type||e,toastId:t&&(r(t.toastId)||s(t.toastId))?t.toastId:C()}}function w(e){return(t,n)=>N(t,L(e,n))}function k(e,t){return N(e,L("default",t))}k.loading=(e,t)=>N(e,L("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...t})),k.promise=function(e,t,n){let a,{pending:o,error:s,success:i}=t;o&&(a=r(o)?k.loading(o,n):k.loading(o.render,{...n,...o}));let c={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},d=(e,t,o)=>{if(null==t)return void k.dismiss(a);let s={type:e,...c,...n,data:o},l=r(t)?{render:t}:t;return a?k.update(a,{...s,...l}):k(l.render,{...s,...l}),o},u=l(e)?e():e;return u.then(e=>d("success",i,e)).catch(e=>d("error",s,e)),u},k.success=w("success"),k.info=w("info"),k.error=w("error"),k.warning=w("warning"),k.warn=k.warning,k.dark=(e,t)=>N(e,L("default",{theme:"dark",...t})),k.dismiss=function(e){var t,n;h()?null==e||r(t=e)||s(t)?f.forEach(t=>{t.removeToast(e)}):e&&("containerId"in e||"id"in e)&&((null==(n=f.get(e.containerId))?void 0:n.removeToast(e.id))||f.forEach(t=>{t.removeToast(e.id)})):m=m.filter(t=>null!=e&&t.options.toastId!==e)},k.clearWaitingQueue=function(e){void 0===e&&(e={}),f.forEach(t=>{!t.props.limit||e.containerId&&t.id!==e.containerId||t.clearQueue()})},k.isActive=v,k.update=function(e,t){void 0===t&&(t={});let n=((e,t)=>{var n;let{containerId:a}=t;return null==(n=f.get(a||1))?void 0:n.toasts.get(e)})(e,t);if(n){let{props:a,content:o}=n,s={delay:100,...a,...t,toastId:t.toastId||e,updateId:C()};s.toastId!==e&&(s.staleId=e);let r=s.render||o;delete s.render,N(r,s)}},k.done=e=>{k.update(e,{progress:1})},k.onChange=function(e){return g.add(e),()=>{g.delete(e)}},k.play=e=>T(!0,e),k.pause=e=>T(!1,e);let $="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,P=e=>{let{theme:t,type:n,isLoading:o,...s}=e;return a.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:"colored"===t?"currentColor":`var(--toastify-icon-color-${n})`,...s})},A={info:function(e){return a.createElement(P,{...e},a.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))},warning:function(e){return a.createElement(P,{...e},a.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))},success:function(e){return a.createElement(P,{...e},a.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))},error:function(e){return a.createElement(P,{...e},a.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))},spinner:function(){return a.createElement("div",{className:"Toastify__spinner"})}},S=e=>{let{isRunning:t,preventExitTransition:n,toastRef:s,eventHandlers:r,playToast:i}=_(e),{closeButton:c,children:d,autoClose:u,onClick:p,type:f,hideProgressBar:m,closeToast:g,transition:y,position:h,className:v,style:E,bodyClassName:T,bodyStyle:b,progressClassName:x,progressStyle:C,updateId:N,role:L,progress:w,rtl:k,toastId:$,deleteToast:P,isIn:S,isLoading:D,closeOnClick:M,theme:O}=e,B=o("Toastify__toast",`Toastify__toast-theme--${O}`,`Toastify__toast--${f}`,{"Toastify__toast--rtl":k},{"Toastify__toast--close-on-click":M}),j=l(v)?v({rtl:k,position:h,type:f,defaultClassName:B}):o(B,v),R=function(e){let{theme:t,type:n,isLoading:o,icon:s}=e,r=null,i={theme:t,type:n,isLoading:o};return!1===s||(l(s)?r=s(i):(0,a.isValidElement)(s)?r=(0,a.cloneElement)(s,i):o?r=A.spinner():n in A&&(r=A[n](i))),r}(e),z=!!w||!u,H={closeToast:g,type:f,theme:O},F=null;return!1===c||(F=l(c)?c(H):(0,a.isValidElement)(c)?(0,a.cloneElement)(c,H):function(e){let{closeToast:t,theme:n,ariaLabel:o="close"}=e;return a.createElement("button",{className:`Toastify__close-button Toastify__close-button--${n}`,type:"button",onClick:e=>{e.stopPropagation(),t(e)},"aria-label":o},a.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},a.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}(H)),a.createElement(y,{isIn:S,done:P,position:h,preventExitTransition:n,nodeRef:s,playToast:i},a.createElement("div",{id:$,onClick:p,"data-in":S,className:j,...r,style:E,ref:s},a.createElement("div",{...S&&{role:L},className:l(T)?T({type:f}):o("Toastify__toast-body",T),style:b},null!=R&&a.createElement("div",{className:o("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!D})},R),a.createElement("div",null,d)),F,a.createElement(I,{...N&&!z?{key:`pb-${N}`}:{},rtl:k,theme:O,delay:u,isRunning:t,isIn:S,closeToast:g,hide:m,type:f,style:C,className:x,controlledProgress:z,progress:w||0})))},D=function(e,t){return void 0===t&&(t=!1),{enter:`Toastify--animate Toastify__${e}-enter`,exit:`Toastify--animate Toastify__${e}-exit`,appendPosition:t}},M=u(D("bounce",!0)),O=u(D("slide",!0)),B=u(D("zoom")),j=u(D("flip")),R={position:"top-right",transition:M,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light"};function z(e){let t={...R,...e},n=e.stacked,[s,r]=(0,a.useState)(!0),c=(0,a.useRef)(null),{getToastToRender:d,isToastActive:u,count:p}=b(t),{className:f,style:m,rtl:g,containerId:y}=t;function h(){n&&(r(!0),k.play())}return $(()=>{if(n){var e;let n=c.current.querySelectorAll('[data-in="true"]'),a=null==(e=t.position)?void 0:e.includes("top"),o=0,r=0;Array.from(n).reverse().forEach((e,t)=>{e.classList.add("Toastify__toast--stacked"),t>0&&(e.dataset.collapsed=`${s}`),e.dataset.pos||(e.dataset.pos=a?"top":"bot");let n=o*(s?.2:1)+(s?0:12*t);e.style.setProperty("--y",`${a?n:-1*n}px`),e.style.setProperty("--g","12"),e.style.setProperty("--s",""+(1-(s?r:0))),o+=e.offsetHeight,r+=.025})}},[s,p,n]),a.createElement("div",{ref:c,className:"Toastify",id:y,onMouseEnter:()=>{n&&(r(!1),k.pause())},onMouseLeave:h},d((e,t)=>{let s=t.length?{...m}:{...m,pointerEvents:"none"};return a.createElement("div",{className:function(e){let t=o("Toastify__toast-container",`Toastify__toast-container--${e}`,{"Toastify__toast-container--rtl":g});return l(f)?f({position:e,rtl:g,defaultClassName:t}):o(t,i(f))}(e),style:s,key:`container-${e}`},t.map(e=>{let{content:t,props:o}=e;return a.createElement(S,{...o,stacked:n,collapseAll:h,isIn:u(o.toastId,o.containerId),style:o.style,key:`toast-${o.key}`},t)}))}))}}},function(e){e.O(0,[580,971,938,744],function(){return e(e.s=1670)}),_N_E=e.O()}]);
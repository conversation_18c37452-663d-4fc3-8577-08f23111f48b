"use strict";(()=>{var e={};e.id=4121,e.ids=[4121],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},12781:e=>{e.exports=require("stream")},73837:e=>{e.exports=require("util")},17390:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>b,originalPathname:()=>A,patchFetch:()=>Z,requestAsyncStorage:()=>S,routeModule:()=>I,serverHooks:()=>q,staticGenerationAsyncStorage:()=>j,staticGenerationBailout:()=>x});var s={};t.r(s),t.d(s,{DELETE:()=>y,GET:()=>v,POST:()=>h});var o=t(95419),n=t(69108),a=t(99678),i=t(91887),u=t(11185),d=t.n(u);let c=new(d()).Schema({userId:{type:d().Schema.Types.ObjectId,ref:"user",required:!0},blogId:{type:d().Schema.Types.ObjectId,ref:"blog",required:!0},createdAt:{type:Date,default:Date.now}});c.index({userId:1,blogId:1},{unique:!0});let l=d().models.favorite||d().model("favorite",c);var g=t(42244),f=t(78070),p=t(11497);(0,i.n)();let m=e=>{try{let r=e.headers.get("Authorization");if(!r||!r.startsWith("Bearer "))return console.log("Missing or invalid Authorization header"),null;let t=r.split(" ")[1];if(!t)return console.log("No token found in Authorization header"),null;let s=(0,p.W)(t);if(!s||!s.userId)return console.log("Invalid token payload:",s),null;return s}catch(e){return console.error("Error extracting user from token:",e),null}};async function v(e){try{let r=m(e);if(!r)return f.Z.json({success:!1,message:"Authentication required"},{status:401});let t=e.nextUrl.searchParams.get("blogId");if(t){let e=await l.findOne({userId:r.userId,blogId:t});return f.Z.json({success:!0,isFavorite:!!e})}let s=await l.find({userId:r.userId}).sort({createdAt:-1}),o=s.map(e=>e.blogId),n=(await g.Z.find({_id:{$in:o}})).map(e=>{let r=s.find(r=>r.blogId.toString()===e._id.toString());return{...e.toObject(),favoriteId:r._id,favoritedAt:r.createdAt}});return f.Z.json({success:!0,favorites:n})}catch(e){return console.error("Error fetching favorites:",e),f.Z.json({success:!1,message:"Failed to fetch favorites"},{status:500})}}async function h(e){try{let r=m(e);if(!r)return f.Z.json({success:!1,message:"Authentication required"},{status:401});let{blogId:t}=await e.json();if(!await g.Z.findById(t))return f.Z.json({success:!1,message:"Blog not found"},{status:404});if(await l.findOne({userId:r.userId,blogId:t}))return f.Z.json({success:!0,message:"Blog already in favorites"});return await l.create({userId:r.userId,blogId:t}),f.Z.json({success:!0,message:"Blog added to favorites"})}catch(e){return console.error("Error adding favorite:",e),f.Z.json({success:!1,message:"Failed to add favorite"},{status:500})}}async function y(e){try{let r=m(e);if(!r)return f.Z.json({success:!1,message:"Authentication required"},{status:401});let t=e.nextUrl.searchParams.get("blogId");if(!await l.findOneAndDelete({userId:r.userId,blogId:t}))return f.Z.json({success:!1,message:"Favorite not found"},{status:404});return f.Z.json({success:!0,message:"Blog removed from favorites"})}catch(e){return console.error("Error removing favorite:",e),f.Z.json({success:!1,message:"Failed to remove favorite"},{status:500})}}let I=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/favorites/route",pathname:"/api/favorites",filename:"route",bundlePath:"app/api/favorites/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\favorites\\route.js",nextConfigOutput:"",userland:s}),{requestAsyncStorage:S,staticGenerationAsyncStorage:j,serverHooks:q,headerHooks:b,staticGenerationBailout:x}=I,A="/api/favorites/route";function Z(){return(0,a.patchFetch)({serverHooks:q,staticGenerationAsyncStorage:j})}},91887:(e,r,t)=>{t.d(r,{n:()=>n});var s=t(11185),o=t.n(s);let n=async()=>{try{if(o().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await o().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},42244:(e,r,t)=>{t.d(r,{Z:()=>a});var s=t(11185),o=t.n(s);let n=new(o()).Schema({title:{type:String,required:!0},description:{type:String,required:!0},category:{type:String,required:!0},author:{type:String,required:!0},image:{type:String,required:!0},authorImg:{type:String,required:!0},date:{type:Date,default:Date.now()}}),a=o().models.blog||o().model("blog",n)},11497:(e,r,t)=>{t.d(r,{V:()=>a,W:()=>i});var s=t(46082),o=t.n(s);let n=process.env.JWT_SECRET||"your-secret-key-here",a=e=>o().sign(e,n,{expiresIn:"7d"}),i=e=>{try{return o().verify(e,n)}catch(e){return console.error("Token verification error:",e.message),null}}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,2993,185],()=>t(17390));module.exports=s})();
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/favorites/route";
exports.ids = ["app/api/favorites/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ffavorites%2Froute&page=%2Fapi%2Ffavorites%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffavorites%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ffavorites%2Froute&page=%2Fapi%2Ffavorites%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffavorites%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_favorites_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/favorites/route.js */ \"(rsc)/./app/api/favorites/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/favorites/route\",\n        pathname: \"/api/favorites\",\n        filename: \"route\",\n        bundlePath: \"app/api/favorites/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\api\\\\favorites\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_favorites_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/favorites/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ffavorites%2Froute&page=%2Fapi%2Ffavorites%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffavorites%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/favorites/route.js":
/*!************************************!*\
  !*** ./app/api/favorites/route.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_config_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/config/db */ \"(rsc)/./lib/config/db.js\");\n/* harmony import */ var _lib_models_FavoriteModel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/models/FavoriteModel */ \"(rsc)/./lib/models/FavoriteModel.js\");\n/* harmony import */ var _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/models/BlogModel */ \"(rsc)/./lib/models/BlogModel.js\");\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_utils_token__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils/token */ \"(rsc)/./lib/utils/token.js\");\n\n\n\n\n\n// Connect to database\n(0,_lib_config_db__WEBPACK_IMPORTED_MODULE_0__.ConnectDB)();\n// Helper function to extract user ID from token\nconst getUserFromToken = (request)=>{\n    try {\n        const authHeader = request.headers.get(\"Authorization\");\n        if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n            console.log(\"Missing or invalid Authorization header\");\n            return null;\n        }\n        const token = authHeader.split(\" \")[1];\n        if (!token) {\n            console.log(\"No token found in Authorization header\");\n            return null;\n        }\n        const userData = (0,_lib_utils_token__WEBPACK_IMPORTED_MODULE_4__.verifyToken)(token);\n        if (!userData || !userData.userId) {\n            console.log(\"Invalid token payload:\", userData);\n            return null;\n        }\n        return userData;\n    } catch (error) {\n        console.error(\"Error extracting user from token:\", error);\n        return null;\n    }\n};\n// Get user's favorites\nasync function GET(request) {\n    try {\n        // Verify authentication\n        const userData = getUserFromToken(request);\n        if (!userData) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_3__[\"default\"].json({\n                success: false,\n                message: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Get query parameters\n        const blogId = request.nextUrl.searchParams.get(\"blogId\");\n        // If blogId is provided, check if this specific blog is favorited\n        if (blogId) {\n            const favorite = await _lib_models_FavoriteModel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findOne({\n                userId: userData.userId,\n                blogId\n            });\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_3__[\"default\"].json({\n                success: true,\n                isFavorite: !!favorite\n            });\n        }\n        // Otherwise, get all favorites with blog details\n        const favorites = await _lib_models_FavoriteModel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].find({\n            userId: userData.userId\n        }).sort({\n            createdAt: -1\n        });\n        // Get full blog details for each favorite\n        const blogIds = favorites.map((fav)=>fav.blogId);\n        const blogs = await _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find({\n            _id: {\n                $in: blogIds\n            }\n        });\n        // Map blogs to include favorite information\n        const favoritedBlogs = blogs.map((blog)=>{\n            const favorite = favorites.find((f)=>f.blogId.toString() === blog._id.toString());\n            return {\n                ...blog.toObject(),\n                favoriteId: favorite._id,\n                favoritedAt: favorite.createdAt\n            };\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_3__[\"default\"].json({\n            success: true,\n            favorites: favoritedBlogs\n        });\n    } catch (error) {\n        console.error(\"Error fetching favorites:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_3__[\"default\"].json({\n            success: false,\n            message: \"Failed to fetch favorites\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Add a favorite\nasync function POST(request) {\n    try {\n        // Verify authentication\n        const userData = getUserFromToken(request);\n        if (!userData) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_3__[\"default\"].json({\n                success: false,\n                message: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { blogId } = body;\n        // Check if blog exists\n        const blog = await _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].findById(blogId);\n        if (!blog) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_3__[\"default\"].json({\n                success: false,\n                message: \"Blog not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Check if already favorited\n        const existingFavorite = await _lib_models_FavoriteModel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findOne({\n            userId: userData.userId,\n            blogId\n        });\n        if (existingFavorite) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_3__[\"default\"].json({\n                success: true,\n                message: \"Blog already in favorites\"\n            });\n        }\n        // Create new favorite\n        await _lib_models_FavoriteModel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n            userId: userData.userId,\n            blogId\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_3__[\"default\"].json({\n            success: true,\n            message: \"Blog added to favorites\"\n        });\n    } catch (error) {\n        console.error(\"Error adding favorite:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_3__[\"default\"].json({\n            success: false,\n            message: \"Failed to add favorite\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Remove a favorite\nasync function DELETE(request) {\n    try {\n        // Verify authentication\n        const userData = getUserFromToken(request);\n        if (!userData) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_3__[\"default\"].json({\n                success: false,\n                message: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        const blogId = request.nextUrl.searchParams.get(\"blogId\");\n        // Delete the favorite\n        const result = await _lib_models_FavoriteModel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findOneAndDelete({\n            userId: userData.userId,\n            blogId\n        });\n        if (!result) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_3__[\"default\"].json({\n                success: false,\n                message: \"Favorite not found\"\n            }, {\n                status: 404\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_3__[\"default\"].json({\n            success: true,\n            message: \"Blog removed from favorites\"\n        });\n    } catch (error) {\n        console.error(\"Error removing favorite:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_3__[\"default\"].json({\n            success: false,\n            message: \"Failed to remove favorite\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/favorites/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/config/db.js":
/*!**************************!*\
  !*** ./lib/config/db.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectDB: () => (/* binding */ ConnectDB)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ConnectDB = async ()=>{\n    try {\n        // Check if already connected\n        if ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().connections)[0].readyState) {\n            console.log(\"DB Already Connected\");\n            return;\n        }\n        const connectionString = process.env.MONGODB_URI || \"mongodb+srv://subhashanas:<EMAIL>/blog-app\";\n        await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(connectionString, {\n            serverSelectionTimeoutMS: 5000,\n            socketTimeoutMS: 45000\n        });\n        console.log(\"DB Connected\");\n    } catch (error) {\n        console.error(\"DB Connection Error:\", error.message);\n    // Don't throw the error to prevent 500 responses\n    // The API will handle the case where DB is not connected\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/config/db.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/BlogModel.js":
/*!*********************************!*\
  !*** ./lib/models/BlogModel.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Schema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    title: {\n        type: String,\n        required: true\n    },\n    description: {\n        type: String,\n        required: true\n    },\n    category: {\n        type: String,\n        required: true\n    },\n    author: {\n        type: String,\n        required: true\n    },\n    image: {\n        type: String,\n        required: true\n    },\n    authorImg: {\n        type: String,\n        required: true\n    },\n    date: {\n        type: Date,\n        default: Date.now()\n    }\n});\nconst BlogModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).blog || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"blog\", Schema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BlogModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9kZWxzL0Jsb2dNb2RlbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0M7QUFFaEMsTUFBTUMsU0FBUyxJQUFJRCx3REFBZSxDQUFDO0lBQy9CRSxPQUFNO1FBQ0ZDLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBQyxhQUFZO1FBQ1JILE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBRSxVQUFTO1FBQ0xKLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBRyxRQUFPO1FBQ0hMLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBSSxPQUFNO1FBQ0ZOLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBSyxXQUFVO1FBQ05QLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBTSxNQUFLO1FBQ0RSLE1BQUtTO1FBQ0xDLFNBQVFELEtBQUtFLEdBQUc7SUFDcEI7QUFDSjtBQUVBLE1BQU1DLFlBQVlmLHdEQUFlLENBQUNpQixJQUFJLElBQUlqQixxREFBYyxDQUFDLFFBQU9DO0FBRWhFLGlFQUFlYyxTQUFTQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL2xpYi9tb2RlbHMvQmxvZ01vZGVsLmpzPzhmZGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG1vbmdvb3NlIGZyb20gXCJtb25nb29zZVwiO1xuXG5jb25zdCBTY2hlbWEgPSBuZXcgbW9uZ29vc2UuU2NoZW1hKHtcbiAgICB0aXRsZTp7XG4gICAgICAgIHR5cGU6U3RyaW5nLFxuICAgICAgICByZXF1aXJlZDp0cnVlXG4gICAgfSxcbiAgICBkZXNjcmlwdGlvbjp7XG4gICAgICAgIHR5cGU6U3RyaW5nLFxuICAgICAgICByZXF1aXJlZDp0cnVlXG4gICAgfSxcbiAgICBjYXRlZ29yeTp7XG4gICAgICAgIHR5cGU6U3RyaW5nLFxuICAgICAgICByZXF1aXJlZDp0cnVlXG4gICAgfSxcbiAgICBhdXRob3I6e1xuICAgICAgICB0eXBlOlN0cmluZyxcbiAgICAgICAgcmVxdWlyZWQ6dHJ1ZVxuICAgIH0sXG4gICAgaW1hZ2U6e1xuICAgICAgICB0eXBlOlN0cmluZyxcbiAgICAgICAgcmVxdWlyZWQ6dHJ1ZVxuICAgIH0sXG4gICAgYXV0aG9ySW1nOntcbiAgICAgICAgdHlwZTpTdHJpbmcsXG4gICAgICAgIHJlcXVpcmVkOnRydWVcbiAgICB9LFxuICAgIGRhdGU6e1xuICAgICAgICB0eXBlOkRhdGUsXG4gICAgICAgIGRlZmF1bHQ6RGF0ZS5ub3coKVxuICAgIH1cbn0pXG5cbmNvbnN0IEJsb2dNb2RlbCA9IG1vbmdvb3NlLm1vZGVscy5ibG9nIHx8IG1vbmdvb3NlLm1vZGVsKCdibG9nJyxTY2hlbWEpO1xuXG5leHBvcnQgZGVmYXVsdCBCbG9nTW9kZWw7Il0sIm5hbWVzIjpbIm1vbmdvb3NlIiwiU2NoZW1hIiwidGl0bGUiLCJ0eXBlIiwiU3RyaW5nIiwicmVxdWlyZWQiLCJkZXNjcmlwdGlvbiIsImNhdGVnb3J5IiwiYXV0aG9yIiwiaW1hZ2UiLCJhdXRob3JJbWciLCJkYXRlIiwiRGF0ZSIsImRlZmF1bHQiLCJub3ciLCJCbG9nTW9kZWwiLCJtb2RlbHMiLCJibG9nIiwibW9kZWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/BlogModel.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/FavoriteModel.js":
/*!*************************************!*\
  !*** ./lib/models/FavoriteModel.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Schema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    userId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"user\",\n        required: true\n    },\n    blogId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"blog\",\n        required: true\n    },\n    createdAt: {\n        type: Date,\n        default: Date.now\n    }\n});\n// Compound index to ensure a user can only favorite a blog once\nSchema.index({\n    userId: 1,\n    blogId: 1\n}, {\n    unique: true\n});\nconst FavoriteModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).favorite || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"favorite\", Schema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FavoriteModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9kZWxzL0Zhdm9yaXRlTW9kZWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdDO0FBRWhDLE1BQU1DLFNBQVMsSUFBSUQsd0RBQWUsQ0FBQztJQUMvQkUsUUFBUTtRQUNKQyxNQUFNSCx3REFBZSxDQUFDSSxLQUFLLENBQUNDLFFBQVE7UUFDcENDLEtBQUs7UUFDTEMsVUFBVTtJQUNkO0lBQ0FDLFFBQVE7UUFDSkwsTUFBTUgsd0RBQWUsQ0FBQ0ksS0FBSyxDQUFDQyxRQUFRO1FBQ3BDQyxLQUFLO1FBQ0xDLFVBQVU7SUFDZDtJQUNBRSxXQUFXO1FBQ1BOLE1BQU1PO1FBQ05DLFNBQVNELEtBQUtFLEdBQUc7SUFDckI7QUFDSjtBQUVBLGdFQUFnRTtBQUNoRVgsT0FBT1ksS0FBSyxDQUFDO0lBQUVYLFFBQVE7SUFBR00sUUFBUTtBQUFFLEdBQUc7SUFBRU0sUUFBUTtBQUFLO0FBRXRELE1BQU1DLGdCQUFnQmYsd0RBQWUsQ0FBQ2lCLFFBQVEsSUFBSWpCLHFEQUFjLENBQUMsWUFBWUM7QUFFN0UsaUVBQWVjLGFBQWFBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vbGliL21vZGVscy9GYXZvcml0ZU1vZGVsLmpzP2ViMjciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG1vbmdvb3NlIGZyb20gXCJtb25nb29zZVwiO1xuXG5jb25zdCBTY2hlbWEgPSBuZXcgbW9uZ29vc2UuU2NoZW1hKHtcbiAgICB1c2VySWQ6IHtcbiAgICAgICAgdHlwZTogbW9uZ29vc2UuU2NoZW1hLlR5cGVzLk9iamVjdElkLFxuICAgICAgICByZWY6ICd1c2VyJyxcbiAgICAgICAgcmVxdWlyZWQ6IHRydWVcbiAgICB9LFxuICAgIGJsb2dJZDoge1xuICAgICAgICB0eXBlOiBtb25nb29zZS5TY2hlbWEuVHlwZXMuT2JqZWN0SWQsXG4gICAgICAgIHJlZjogJ2Jsb2cnLFxuICAgICAgICByZXF1aXJlZDogdHJ1ZVxuICAgIH0sXG4gICAgY3JlYXRlZEF0OiB7XG4gICAgICAgIHR5cGU6IERhdGUsXG4gICAgICAgIGRlZmF1bHQ6IERhdGUubm93XG4gICAgfVxufSk7XG5cbi8vIENvbXBvdW5kIGluZGV4IHRvIGVuc3VyZSBhIHVzZXIgY2FuIG9ubHkgZmF2b3JpdGUgYSBibG9nIG9uY2VcblNjaGVtYS5pbmRleCh7IHVzZXJJZDogMSwgYmxvZ0lkOiAxIH0sIHsgdW5pcXVlOiB0cnVlIH0pO1xuXG5jb25zdCBGYXZvcml0ZU1vZGVsID0gbW9uZ29vc2UubW9kZWxzLmZhdm9yaXRlIHx8IG1vbmdvb3NlLm1vZGVsKCdmYXZvcml0ZScsIFNjaGVtYSk7XG5cbmV4cG9ydCBkZWZhdWx0IEZhdm9yaXRlTW9kZWw7XG4iXSwibmFtZXMiOlsibW9uZ29vc2UiLCJTY2hlbWEiLCJ1c2VySWQiLCJ0eXBlIiwiVHlwZXMiLCJPYmplY3RJZCIsInJlZiIsInJlcXVpcmVkIiwiYmxvZ0lkIiwiY3JlYXRlZEF0IiwiRGF0ZSIsImRlZmF1bHQiLCJub3ciLCJpbmRleCIsInVuaXF1ZSIsIkZhdm9yaXRlTW9kZWwiLCJtb2RlbHMiLCJmYXZvcml0ZSIsIm1vZGVsIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/FavoriteModel.js\n");

/***/ }),

/***/ "(rsc)/./lib/utils/token.js":
/*!****************************!*\
  !*** ./lib/utils/token.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n// Simple token utilities without external dependencies\n\n// Secret key for JWT signing - in production, use environment variables\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key-here\";\n// Create a token using JWT\nconst createToken = (payload)=>{\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n};\n// Verify a token using JWT\nconst verifyToken = (token)=>{\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n    } catch (error) {\n        console.error(\"Token verification error:\", error.message);\n        return null;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvdXRpbHMvdG9rZW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLHVEQUF1RDtBQUN4QjtBQUUvQix3RUFBd0U7QUFDeEUsTUFBTUMsYUFBYUMsUUFBUUMsR0FBRyxDQUFDRixVQUFVLElBQUk7QUFFN0MsMkJBQTJCO0FBQ3BCLE1BQU1HLGNBQWMsQ0FBQ0M7SUFDMUIsT0FBT0wsd0RBQVEsQ0FBQ0ssU0FBU0osWUFBWTtRQUFFTSxXQUFXO0lBQUs7QUFDekQsRUFBRTtBQUVGLDJCQUEyQjtBQUNwQixNQUFNQyxjQUFjLENBQUNDO0lBQzFCLElBQUk7UUFDRixPQUFPVCwwREFBVSxDQUFDUyxPQUFPUjtJQUMzQixFQUFFLE9BQU9VLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkEsTUFBTUUsT0FBTztRQUN4RCxPQUFPO0lBQ1Q7QUFDRixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL2xpYi91dGlscy90b2tlbi5qcz9iOTgxIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFNpbXBsZSB0b2tlbiB1dGlsaXRpZXMgd2l0aG91dCBleHRlcm5hbCBkZXBlbmRlbmNpZXNcbmltcG9ydCBqd3QgZnJvbSAnanNvbndlYnRva2VuJztcblxuLy8gU2VjcmV0IGtleSBmb3IgSldUIHNpZ25pbmcgLSBpbiBwcm9kdWN0aW9uLCB1c2UgZW52aXJvbm1lbnQgdmFyaWFibGVzXG5jb25zdCBKV1RfU0VDUkVUID0gcHJvY2Vzcy5lbnYuSldUX1NFQ1JFVCB8fCAneW91ci1zZWNyZXQta2V5LWhlcmUnO1xuXG4vLyBDcmVhdGUgYSB0b2tlbiB1c2luZyBKV1RcbmV4cG9ydCBjb25zdCBjcmVhdGVUb2tlbiA9IChwYXlsb2FkKSA9PiB7XG4gIHJldHVybiBqd3Quc2lnbihwYXlsb2FkLCBKV1RfU0VDUkVULCB7IGV4cGlyZXNJbjogJzdkJyB9KTtcbn07XG5cbi8vIFZlcmlmeSBhIHRva2VuIHVzaW5nIEpXVFxuZXhwb3J0IGNvbnN0IHZlcmlmeVRva2VuID0gKHRva2VuKSA9PiB7XG4gIHRyeSB7XG4gICAgcmV0dXJuIGp3dC52ZXJpZnkodG9rZW4sIEpXVF9TRUNSRVQpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJUb2tlbiB2ZXJpZmljYXRpb24gZXJyb3I6XCIsIGVycm9yLm1lc3NhZ2UpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59O1xuXG4iXSwibmFtZXMiOlsiand0IiwiSldUX1NFQ1JFVCIsInByb2Nlc3MiLCJlbnYiLCJjcmVhdGVUb2tlbiIsInBheWxvYWQiLCJzaWduIiwiZXhwaXJlc0luIiwidmVyaWZ5VG9rZW4iLCJ0b2tlbiIsInZlcmlmeSIsImVycm9yIiwiY29uc29sZSIsIm1lc3NhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils/token.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ms","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ffavorites%2Froute&page=%2Fapi%2Ffavorites%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffavorites%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
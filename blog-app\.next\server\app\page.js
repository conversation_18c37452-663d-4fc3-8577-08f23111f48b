(()=>{var e={};e.id=1931,e.ids=[1931],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},48935:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=s(50482),a=s(69108),l=s(62563),o=s.n(l),i=s(68300),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);s.d(t,n);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,62115)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\page.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,39459)),"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\page.jsx"],m="/page",u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},950:(e,t,s)=>{Promise.resolve().then(s.bind(s,90113))},28674:(e,t,s)=>{"use strict";s.d(t,{Z:()=>c});var r=s(95344),a=s(6880),l=s(41223),o=s.n(l),i=s(20783),n=s.n(i);s(3729);let c=()=>(0,r.jsxs)("div",{className:"flex justify-around flex-col gap-2 sm:gap-0 sm:flex-row bg-black py-5 items-center",children:[r.jsx(o(),{src:a.L.logo_light,alt:"Mr.Blogger",width:120}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"flex justify-center gap-4 mb-1",children:[r.jsx(n(),{href:"/about",className:"text-sm text-white hover:underline",children:"About Us"}),r.jsx(n(),{href:"/contact",className:"text-sm text-white hover:underline",children:"Contact Us"})]}),r.jsx("p",{className:"text-sm text-white",children:"All right reserved. Copyright @Mr.Blogger"})]}),(0,r.jsxs)("div",{className:"flex",children:[r.jsx(o(),{src:a.L.facebook_icon,alt:"",width:40}),r.jsx(o(),{src:a.L.twitter_icon,alt:"",width:40}),r.jsx(o(),{src:a.L.googleplus_icon,alt:"",width:40})]})]})},90113:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(95344),a=s(3729),l=s(6880),o=s(41223),i=s.n(o),n=s(20783),c=s.n(n),d=s(96556);let m=({title:e,description:t,category:s,image:a,id:o})=>(0,r.jsxs)("div",{className:"max-w-[330px] sm:max-w-[300px] bg-white border border-black transition-all hover:shadow-[-7px_7px_0px_#000000]",children:[r.jsx(c(),{href:`/blogs/${o}`,children:r.jsx(i(),{src:a,alt:"",width:400,height:400,className:"border-b border-black"})}),r.jsx("p",{className:"ml-5 mt-5 px-1 inline-block bg-black text-white text-sm",children:s}),(0,r.jsxs)("div",{className:"p-5",children:[r.jsx("h5",{className:"mb-2 text-lg font-medium tracking-tight text-gray-900",children:e}),r.jsx("p",{className:"mb-3 text-sm tracking-tight text-gray-700",dangerouslySetInnerHTML:{__html:(0,d.D)(t).slice(0,120)}}),(0,r.jsxs)(c(),{href:`/blogs/${o}`,className:"inline-flex items-center py-2 font-semibold text-center",children:["Read more ",r.jsx(i(),{src:l.L.arrow,className:"ml-2",alt:"",width:12})]})]})]});var u=s(53608);let x=({searchTerm:e=""})=>{let[t,s]=(0,a.useState)("All"),[l,o]=(0,a.useState)([]),[i,n]=(0,a.useState)([]),[c,d]=(0,a.useState)(!0),[x,p]=(0,a.useState)(1),[g,h]=(0,a.useState)(!1),[b,w]=(0,a.useState)(!1),f=(0,a.useRef)(null),j=async()=>{try{let e=await u.Z.get("/api/blog");e.data.blogs?o(e.data.blogs):o([]),d(!1)}catch(e){console.error("Error fetching blogs:",e),o([]),d(!1)}},v=async()=>{try{let e=await u.Z.get("/api/categories");e.data.success&&e.data.categories.length>0?n(e.data.categories):n([{_id:"1",name:"Startup"},{_id:"2",name:"Technology"},{_id:"3",name:"Lifestyle"}])}catch(e){console.error("Error fetching categories:",e),n([{_id:"1",name:"Startup"},{_id:"2",name:"Technology"},{_id:"3",name:"Lifestyle"}])}};(0,a.useEffect)(()=>{w(!0),j(),v()},[]),(0,a.useEffect)(()=>{p(1)},[t]),(0,a.useEffect)(()=>{if(!b)return;let e=e=>{f.current&&!f.current.contains(e.target)&&h(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[b]);let y=l.filter(s=>{let r="All"===t||s.category===t,a=""===e.trim()||s.title.toLowerCase().includes(e.toLowerCase())||s.description.toLowerCase().includes(e.toLowerCase());return r&&a}),N=Math.ceil(y.length/12),k=(x-1)*12,S=y.slice(k,k+12),C=e=>{s(e),h(!1)};return b?(0,r.jsxs)("div",{children:[r.jsx("div",{className:"flex justify-center my-10",children:(0,r.jsxs)("div",{className:"flex gap-4",children:[r.jsx("button",{onClick:()=>s("All"),className:`py-2 px-6 rounded-md transition-colors ${"All"===t?"bg-black text-white":"bg-gray-100 hover:bg-gray-200"}`,children:"All"}),(0,r.jsxs)("div",{className:"relative",ref:f,children:[(0,r.jsxs)("button",{onClick:()=>h(!g),className:`py-2 px-6 rounded-md flex items-center gap-2 transition-colors ${"All"!==t?"bg-black text-white":"bg-gray-100 hover:bg-gray-200"}`,children:["All"!==t?t:"Categories",r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:`h-4 w-4 transition-transform ${g?"rotate-180":""}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),g&&r.jsx("div",{className:"absolute z-10 mt-1 w-48 bg-white rounded-md shadow-lg py-1 max-h-60 overflow-auto",children:i.map(e=>r.jsx("button",{onClick:()=>C(e.name),className:`block w-full text-left px-4 py-2 text-sm ${t===e.name?"bg-gray-100 font-medium":"hover:bg-gray-50"}`,children:e.name},e._id))})]})]})}),c?r.jsx("div",{className:"text-center py-10",children:"Loading blogs..."}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-32 xl:mx-24",children:S.map((e,t)=>r.jsx(m,{id:e._id,image:e.image,title:e.title,description:e.description,category:e.category},e._id||t))}),N>1&&r.jsx("div",{className:"flex justify-center mt-0 mb-8 gap-2",children:Array.from({length:N},(e,t)=>r.jsx("button",{onClick:()=>p(t+1),className:`px-4 py-2 border border-black rounded ${x===t+1?"bg-black text-white":"bg-white text-black hover:bg-gray-100"}`,children:t+1},t))})]})]}):r.jsx("div",{className:"text-center py-10",children:"Loading..."})};var p=s(28674),g=s(22254),h=s(69697);let b=({setSearchTerm:e})=>{let t=(0,g.useRouter)(),[s,o]=(0,a.useState)(""),[n,c]=(0,a.useState)(!1),[d,m]=(0,a.useState)(""),[x,p]=(0,a.useState)(!1),[b,w]=(0,a.useState)(!1),[f,j]=(0,a.useState)(!1),[v,y]=(0,a.useState)({email:"",password:""}),[N,k]=(0,a.useState)({email:"",password:"",confirmPassword:"",role:"user"}),[S,C]=(0,a.useState)("/default_profile.png"),[_,L]=(0,a.useState)(""),[P,M]=(0,a.useState)(!1),[q,I]=(0,a.useState)(!1),[A,R]=(0,a.useState)(!1),[z,E]=(0,a.useState)(!1),[B,D]=(0,a.useState)(!1),[T,Z]=(0,a.useState)(!0),[$,W]=(0,a.useState)(""),[G,U]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=localStorage.getItem("authToken"),t=localStorage.getItem("userRole");localStorage.getItem("userId");let s=localStorage.getItem("userProfilePicture"),r=localStorage.getItem("userName"),a=localStorage.getItem("rememberedEmail"),l=localStorage.getItem("rememberedPassword"),o="true"===localStorage.getItem("rememberMe");a&&l&&o&&(y({email:a,password:l}),D(!0)),e&&(c(!0),m(t||"user"),s&&C(s),r&&L(r))},[]);let F=async e=>{e.preventDefault();let t=new FormData;t.append("email",s);let r=await u.Z.post("/api/email",t);r.data.success?(h.toast.success(r.data.msg),o("")):h.toast.error("Error")},Y=e=>{y({...v,[e.target.name]:e.target.value})},H=e=>{k({...N,[e.target.name]:e.target.value})},K=async e=>{e.preventDefault();try{let e=await u.Z.post("/api/auth",{email:v.email,password:v.password});e.data.success?(localStorage.setItem("authToken",e.data.token||"dummy-token"),localStorage.setItem("userRole",e.data.user.role),localStorage.setItem("userId",e.data.user.id),localStorage.setItem("userProfilePicture",e.data.user.profilePicture),localStorage.setItem("userName",e.data.user.name||""),B?(localStorage.setItem("rememberedEmail",v.email),localStorage.setItem("rememberedPassword",v.password),localStorage.setItem("rememberMe","true")):(localStorage.removeItem("rememberedEmail"),localStorage.removeItem("rememberedPassword"),localStorage.removeItem("rememberMe")),c(!0),m(e.data.user.role),C(e.data.user.profilePicture),L(e.data.user.name||""),p(!1),"admin"===e.data.user.role?(h.toast.success("Login successful"),window.location.href="/admin"):(h.toast.success("Login successful"),window.location.href="/")):h.toast.error("Invalid credentials")}catch(e){console.error("Login error:",e),h.toast.error(e.response?.data?.message||"Login failed")}},O=async e=>{if(e.preventDefault(),N.password!==N.confirmPassword){h.toast.error("Passwords do not match");return}try{let e=await u.Z.post("/api/register",{email:N.email,password:N.password,role:N.role});e.data.success?(h.toast.success("Registration successful! Please login."),w(!1),y({...v,email:N.email}),k({email:"",password:"",confirmPassword:"",role:"user"})):h.toast.error(e.data.message||"Registration failed")}catch(e){console.error("Registration error:",e),h.toast.error(e.response?.data?.message||"Registration failed")}},X=()=>{w(!b)};return(0,r.jsxs)("div",{className:"py-5 px-5 md:px-12 lg:px-28",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx(i(),{src:l.L.logo,width:180,alt:"",className:"w-[130px] sm:w-auto"}),r.jsx("div",{className:"flex gap-3",children:n?r.jsx("div",{className:"relative",children:(0,r.jsxs)("button",{onClick:()=>t.push("/profile"),className:"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-solid border-black shadow-[-7px_7px_0px_#000000]",children:[r.jsx(i(),{src:S,width:24,height:24,alt:"Account",className:"w-6 h-6 rounded-full object-cover"}),r.jsx("span",{children:_||"Account"})]})}):(0,r.jsxs)("button",{onClick:()=>p(!0),className:"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-solid border-black shadow-[-7px_7px_0px_#000000]",children:["Get started ",r.jsx(i(),{src:l.L.arrow,alt:""})]})})]}),x&&r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-8 rounded-md shadow-lg w-96",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:b?"Register":"Login"}),b?(0,r.jsxs)("form",{onSubmit:O,children:[(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{className:"block text-gray-700 mb-2",children:"Email"}),r.jsx("input",{type:"email",name:"email",value:N.email,onChange:H,className:"w-full px-3 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{className:"block text-gray-700 mb-2",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("input",{type:A?"text":"password",name:"password",value:N.password,onChange:H,className:"w-full px-3 py-2 border border-gray-300 rounded-md pr-10",required:!0}),r.jsx("button",{type:"button",onClick:()=>{R(!A)},className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600",children:A?(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[r.jsx("path",{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}),r.jsx("path",{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"})]}):(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[r.jsx("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),r.jsx("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]})})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("label",{className:"block text-gray-700 mb-2",children:"Confirm Password"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("input",{type:z?"text":"password",name:"confirmPassword",value:N.confirmPassword,onChange:H,className:"w-full px-3 py-2 border border-gray-300 rounded-md pr-10",required:!0}),r.jsx("button",{type:"button",onClick:()=>{E(!z)},className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600",children:z?(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[r.jsx("path",{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}),r.jsx("path",{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"})]}):(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[r.jsx("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),r.jsx("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]})})]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("button",{type:"submit",className:"bg-black text-white px-4 py-2 rounded-md",children:"Register"}),r.jsx("button",{type:"button",onClick:()=>p(!1),className:"text-gray-600 px-4 py-2",children:"Cancel"})]}),r.jsx("div",{className:"mt-4 text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",r.jsx("button",{type:"button",onClick:X,className:"text-blue-600 hover:underline",children:"Login"})]})})]}):(0,r.jsxs)("form",{onSubmit:K,children:[(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{className:"block text-gray-700 mb-2",children:"Email"}),r.jsx("input",{type:"email",name:"email",value:v.email,onChange:Y,className:"w-full px-3 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("label",{className:"block text-gray-700 mb-2",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("input",{type:q?"text":"password",name:"password",value:v.password,onChange:Y,className:"w-full px-3 py-2 border border-gray-300 rounded-md pr-10",required:!0}),r.jsx("button",{type:"button",onClick:()=>{I(!q)},className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600",children:q?(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[r.jsx("path",{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}),r.jsx("path",{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"})]}):(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[r.jsx("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),r.jsx("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]})})]})]}),r.jsx("div",{className:"mb-4",children:(0,r.jsxs)("label",{className:"flex items-center",children:[r.jsx("input",{type:"checkbox",checked:B,onChange:e=>D(e.target.checked),className:"mr-2"}),r.jsx("span",{className:"text-sm text-gray-700",children:"Remember me"})]})}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("button",{type:"submit",className:"bg-black text-white px-4 py-2 rounded-md",children:"Login"}),r.jsx("button",{type:"button",onClick:()=>p(!1),className:"text-gray-600 px-4 py-2",children:"Cancel"})]}),r.jsx("div",{className:"mt-4 text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",r.jsx("button",{type:"button",onClick:X,className:"text-blue-600 hover:underline",children:"Register"})]})})]})]})}),P&&r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-6 rounded-md shadow-lg max-w-sm w-full",children:[r.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Confirm Logout"}),r.jsx("p",{className:"mb-6",children:"Are you sure you want to log out? You will need to log in again to access your account."}),(0,r.jsxs)("div",{className:"flex justify-end gap-3",children:[r.jsx("button",{onClick:()=>{M(!1)},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"}),r.jsx("button",{onClick:()=>{localStorage.removeItem("authToken"),localStorage.removeItem("userRole"),localStorage.removeItem("userId"),localStorage.removeItem("userProfilePicture"),localStorage.removeItem("userName"),c(!1),m(""),M(!1),h.toast.success("Logged out successfully"),setTimeout(()=>{window.location.href="/"},300)},className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",children:"Logout"})]})]})}),(0,r.jsxs)("div",{className:"text-center my-8",children:[r.jsx("h1",{className:"text-3xl sm:text-5xl font-medium",children:"Mr.Blogger"}),r.jsx("p",{className:"mt-10 max-w-[740px] m-auto text-xs sm:text-base",children:"Discover insightful articles, trending tech news, startup journeys, and lifestyle stories — all in one place. Welcome to your daily dose of inspiration and knowledge."}),(0,r.jsxs)("form",{onSubmit:T?t=>{if(t.preventDefault(),!$.trim()){h.toast.error("Please enter a search term");return}e($)}:F,className:"flex justify-between max-w-[500px] scale-75 sm:scale-100 mx-auto mt-10 border border-black shadow-[-7px_7px_0px_#000000]",action:"",children:[r.jsx("input",{onChange:T?t=>{W(t.target.value),""===t.target.value&&e("")}:e=>o(e.target.value),value:T?$:s,type:T?"text":"email",placeholder:T?"Search blogs...":"Enter your email",className:"pl-4 outline-none flex-1",required:!0}),r.jsx("button",{type:"submit",className:"border-l border-black py-4 px-4 sm:px-8 active:bg-gray-600 active:text-white",children:T?"Search":"Subscribe"}),(0,r.jsxs)("div",{className:"relative flex items-center",children:[r.jsx("button",{type:"button",onClick:()=>{Z(t=>(t&&(W(""),e("")),!t))},onMouseEnter:()=>U(!0),onMouseLeave:()=>U(!1),className:"flex items-center justify-center px-4 border-l border-black hover:bg-gray-100 transition-colors",style:{minWidth:"56px"},children:T?(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"22",height:"22",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:"2",children:[r.jsx("rect",{x:"3",y:"5",width:"18",height:"14",rx:"2",fill:"none",stroke:"currentColor",strokeWidth:"2"}),r.jsx("polyline",{points:"3,7 12,13 21,7",fill:"none",stroke:"currentColor",strokeWidth:"2"})]}):(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"22",height:"22",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:"2",children:[r.jsx("circle",{cx:"11",cy:"11",r:"8",stroke:"currentColor",strokeWidth:"2",fill:"none"}),r.jsx("line",{x1:"21",y1:"21",x2:"16.65",y2:"16.65",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round"})]})}),G&&r.jsx("div",{className:"absolute -top-10 left-1/2 -translate-x-1/2 bg-black text-white text-xs rounded px-3 py-1 shadow-lg animate-fade-in z-10 whitespace-nowrap",children:T?"Switch to Subscribe mode":"Switch to Search mode"})]})]})]})]})};s(45996);var w=s(60267);function f(){let[e,t]=(0,a.useState)(""),[s,l]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{l(!0),(0,w.Z0)("/","home")},[]),s)?(0,r.jsxs)(r.Fragment,{children:[r.jsx(h.ToastContainer,{theme:"dark"}),r.jsx(b,{setSearchTerm:t}),r.jsx(x,{searchTerm:e}),r.jsx(p.Z,{})]}):r.jsx("div",{className:"min-h-screen flex items-center justify-center",children:r.jsx("div",{className:"text-xl",children:"Loading..."})})}},60267:(e,t,s)=>{"use strict";s.d(t,{Z0:()=>a,Z5:()=>o,uf:()=>l});var r=s(53608);let a=async(e,t="page",s=null)=>{try{console.log("Tracking page view:",{path:e,contentType:t,blogId:s});let a=document.referrer||null,l=await r.Z.post("/api/analytics",{path:e,contentType:t,blogId:s,referrer:a});console.log("Analytics tracking response:",l.data)}catch(e){console.error("Analytics error:",e)}},l=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),o=async(e="7days")=>{let t=localStorage.getItem("authToken");if(!t)throw Error("Authentication required");return(await r.Z.get(`/api/analytics?period=${e}`,{headers:{Authorization:`Bearer ${t}`}})).data}},96556:(e,t,s)=>{"use strict";s.d(t,{A:()=>a,D:()=>r});let r=e=>{if(!e)return"";let t=e;return(t=(t=t.replace(/\{\{image:[^}]+\}\}/g,"")).replace(/\[\[[^\]]+\]\]/g,"")).replace(/\s+/g," ").trim()},a=(e,t=120)=>{let s=r(e);return s.length>t?s.substring(0,t)+"...":s}},62115:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>l,__esModule:()=>a,default:()=>o});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\page.jsx`),{__esModule:a,$$typeof:l}=r,o=r.default},45996:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,3998,337,8468,5757],()=>s(48935));module.exports=r})();
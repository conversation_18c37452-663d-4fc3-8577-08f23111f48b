{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/admin/editAuthor/[id]", "regex": "^/admin/editAuthor/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/editAuthor/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/editBlog/[id]", "regex": "^/admin/editBlog/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/editBlog/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/authors/[id]", "regex": "^/api/authors/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/authors/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/images/[id]", "regex": "^/api/images/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/images/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/blogs/[id]", "regex": "^/blogs/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/blogs/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/admin", "regex": "^/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin(?:/)?$"}, {"page": "/admin/addBlog", "regex": "^/admin/addBlog(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/addBlog(?:/)?$"}, {"page": "/admin/addUser", "regex": "^/admin/addUser(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/addUser(?:/)?$"}, {"page": "/admin/blogList", "regex": "^/admin/blogList(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/blogList(?:/)?$"}, {"page": "/admin/feedback", "regex": "^/admin/feedback(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/feedback(?:/)?$"}, {"page": "/admin/profile", "regex": "^/admin/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/profile(?:/)?$"}, {"page": "/admin/reactions", "regex": "^/admin/reactions(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/reactions(?:/)?$"}, {"page": "/admin/settings", "regex": "^/admin/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/settings(?:/)?$"}, {"page": "/admin/subscriptions", "regex": "^/admin/subscriptions(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/subscriptions(?:/)?$"}, {"page": "/admin/traffic", "regex": "^/admin/traffic(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/traffic(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/debug", "regex": "^/debug(?:/)?$", "routeKeys": {}, "namedRegex": "^/debug(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/favorites", "regex": "^/favorites(?:/)?$", "routeKeys": {}, "namedRegex": "^/favorites(?:/)?$"}, {"page": "/login-test", "regex": "^/login\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/login\\-test(?:/)?$"}, {"page": "/privacy-policy", "regex": "^/privacy\\-policy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy\\-policy(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/profile/favorites", "regex": "^/profile/favorites(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile/favorites(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}
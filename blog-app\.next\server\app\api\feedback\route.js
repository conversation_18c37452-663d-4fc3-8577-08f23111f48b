"use strict";(()=>{var e={};e.id=4573,e.ids=[4573],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},13926:(e,t,s)=>{s.r(t),s.d(t,{headerHooks:()=>S,originalPathname:()=>w,patchFetch:()=>A,requestAsyncStorage:()=>k,routeModule:()=>y,serverHooks:()=>j,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>v});var r={};s.r(r),s.d(r,{DELETE:()=>m,GET:()=>g,POST:()=>f,PUT:()=>b});var a=s(95419),n=s(69108),o=s(99678),c=s(91887),u=s(78070),d=s(11185),i=s.n(d);(async()=>{await (0,c.n)()})();let l=new(i()).Schema({name:{type:String,required:!0},email:{type:String,required:!0},message:{type:String,required:!0},createdAt:{type:Date,default:Date.now},isRead:{type:Boolean,default:!1}}),p=i().models.Feedback||i().model("Feedback",l);async function f(e){try{let{name:t,email:s,message:r}=await e.json();if(!t||!s||!r)return u.Z.json({success:!1,message:"All fields are required"},{status:400});return await p.create({name:t,email:s,message:r}),u.Z.json({success:!0,message:"Feedback submitted successfully"})}catch(e){return console.error("Error submitting feedback:",e),u.Z.json({success:!1,message:"Failed to submit feedback"},{status:500})}}async function g(){try{let e=await p.find({}).sort({createdAt:-1});return u.Z.json({success:!0,feedbacks:e})}catch(e){return console.error("Error fetching feedbacks:",e),u.Z.json({success:!1,message:"Failed to fetch feedbacks"},{status:500})}}async function m(e){try{let t=e.nextUrl.searchParams.get("id");return await p.findByIdAndDelete(t),u.Z.json({success:!0,message:"Feedback deleted successfully"})}catch(e){return console.error("Error deleting feedback:",e),u.Z.json({success:!1,message:"Failed to delete feedback"},{status:500})}}async function b(e){try{let{id:t,isRead:s}=await e.json();return await p.findByIdAndUpdate(t,{isRead:s}),u.Z.json({success:!0,message:"Feedback updated successfully"})}catch(e){return console.error("Error updating feedback:",e),u.Z.json({success:!1,message:"Failed to update feedback"},{status:500})}}let y=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/feedback/route",pathname:"/api/feedback",filename:"route",bundlePath:"app/api/feedback/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\feedback\\route.js",nextConfigOutput:"",userland:r}),{requestAsyncStorage:k,staticGenerationAsyncStorage:h,serverHooks:j,headerHooks:S,staticGenerationBailout:v}=y,w="/api/feedback/route";function A(){return(0,o.patchFetch)({serverHooks:j,staticGenerationAsyncStorage:h})}},91887:(e,t,s)=>{s.d(t,{n:()=>n});var r=s(11185),a=s.n(r);let n=async()=>{try{if(a().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await a().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},78070:(e,t,s)=>{Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return r.NextResponse}});let r=s(70457)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,2993],()=>s(13926));module.exports=r})();
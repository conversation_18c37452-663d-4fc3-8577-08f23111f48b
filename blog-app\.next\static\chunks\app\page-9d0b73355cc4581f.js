(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{264:function(e,t,s){Promise.resolve().then(s.bind(s,6218))},6218:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return y}});var a=s(7437),r=s(2265),n=s(4257),o=s(6691),l=s.n(o),i=s(1396),c=s.n(i),d=s(8347),u=e=>{let{title:t,description:s,category:r,image:o,id:i}=e;return(0,a.jsxs)("div",{className:"max-w-[330px] sm:max-w-[300px] bg-white border border-black transition-all hover:shadow-[-7px_7px_0px_#000000]",children:[(0,a.jsx)(c(),{href:"/blogs/".concat(i),children:(0,a.jsx)(l(),{src:o,alt:"",width:400,height:400,className:"border-b border-black"})}),(0,a.jsx)("p",{className:"ml-5 mt-5 px-1 inline-block bg-black text-white text-sm",children:r}),(0,a.jsxs)("div",{className:"p-5",children:[(0,a.jsx)("h5",{className:"mb-2 text-lg font-medium tracking-tight text-gray-900",children:t}),(0,a.jsx)("p",{className:"mb-3 text-sm tracking-tight text-gray-700",dangerouslySetInnerHTML:{__html:(0,d.D)(s).slice(0,120)}}),(0,a.jsxs)(c(),{href:"/blogs/".concat(i),className:"inline-flex items-center py-2 font-semibold text-center",children:["Read more ",(0,a.jsx)(l(),{src:n.L.arrow,className:"ml-2",alt:"",width:12})]})]})]})},m=s(2173),p=e=>{let{searchTerm:t=""}=e,[s,n]=(0,r.useState)("All"),[o,l]=(0,r.useState)([]),[i,c]=(0,r.useState)([]),[d,p]=(0,r.useState)(!0),[g,h]=(0,r.useState)(1),[f,x]=(0,r.useState)(!1),[v,y]=(0,r.useState)(!1),b=(0,r.useRef)(null),w=async()=>{try{let e=await m.Z.get("/api/blog");e.data.blogs?l(e.data.blogs):l([]),p(!1)}catch(e){console.error("Error fetching blogs:",e),l([]),p(!1)}},j=async()=>{try{let e=await m.Z.get("/api/categories");e.data.success&&e.data.categories.length>0?c(e.data.categories):c([{_id:"1",name:"Startup"},{_id:"2",name:"Technology"},{_id:"3",name:"Lifestyle"}])}catch(e){console.error("Error fetching categories:",e),c([{_id:"1",name:"Startup"},{_id:"2",name:"Technology"},{_id:"3",name:"Lifestyle"}])}};(0,r.useEffect)(()=>{y(!0),w(),j()},[]),(0,r.useEffect)(()=>{h(1)},[s]),(0,r.useEffect)(()=>{if(!v)return;let e=e=>{b.current&&!b.current.contains(e.target)&&x(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[v]);let N=o.filter(e=>{let a="All"===s||e.category===s,r=""===t.trim()||e.title.toLowerCase().includes(t.toLowerCase())||e.description.toLowerCase().includes(t.toLowerCase());return a&&r}),k=Math.ceil(N.length/12),C=(g-1)*12,E=N.slice(C,C+12),T=e=>{n(e),x(!1)};return v?(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"flex justify-center my-10",children:(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)("button",{onClick:()=>n("All"),className:"py-2 px-6 rounded-md transition-colors ".concat("All"===s?"bg-black text-white":"bg-gray-100 hover:bg-gray-200"),children:"All"}),(0,a.jsxs)("div",{className:"relative",ref:b,children:[(0,a.jsxs)("button",{onClick:()=>x(!f),className:"py-2 px-6 rounded-md flex items-center gap-2 transition-colors ".concat("All"!==s?"bg-black text-white":"bg-gray-100 hover:bg-gray-200"),children:["All"!==s?s:"Categories",(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 transition-transform ".concat(f?"rotate-180":""),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),f&&(0,a.jsx)("div",{className:"absolute z-10 mt-1 w-48 bg-white rounded-md shadow-lg py-1 max-h-60 overflow-auto",children:i.map(e=>(0,a.jsx)("button",{onClick:()=>T(e.name),className:"block w-full text-left px-4 py-2 text-sm ".concat(s===e.name?"bg-gray-100 font-medium":"hover:bg-gray-50"),children:e.name},e._id))})]})]})}),d?(0,a.jsx)("div",{className:"text-center py-10",children:"Loading blogs..."}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-32 xl:mx-24",children:E.map((e,t)=>(0,a.jsx)(u,{id:e._id,image:e.image,title:e.title,description:e.description,category:e.category},e._id||t))}),k>1&&(0,a.jsx)("div",{className:"flex justify-center mt-0 mb-8 gap-2",children:Array.from({length:k},(e,t)=>(0,a.jsx)("button",{onClick:()=>h(t+1),className:"px-4 py-2 border border-black rounded ".concat(g===t+1?"bg-black text-white":"bg-white text-black hover:bg-gray-100"),children:t+1},t))})]})]}):(0,a.jsx)("div",{className:"text-center py-10",children:"Loading..."})},g=s(3637),h=s(4033),f=s(7948),x=e=>{let{setSearchTerm:t}=e,s=(0,h.useRouter)(),[o,i]=(0,r.useState)(""),[c,d]=(0,r.useState)(!1),[u,p]=(0,r.useState)(""),[g,x]=(0,r.useState)(!1),[v,y]=(0,r.useState)(!1),[b,w]=(0,r.useState)(!1),[j,N]=(0,r.useState)({email:"",password:""}),[k,C]=(0,r.useState)({email:"",password:"",confirmPassword:"",role:"user"}),[E,T]=(0,r.useState)("/default_profile.png"),[S,I]=(0,r.useState)(""),[_,L]=(0,r.useState)(!1),[P,M]=(0,r.useState)(!1),[A,R]=(0,r.useState)(!1),[z,B]=(0,r.useState)(!1),[$,D]=(0,r.useState)(!1),[O,F]=(0,r.useState)(!0),[Z,W]=(0,r.useState)(""),[q,H]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=localStorage.getItem("authToken"),t=localStorage.getItem("userRole");localStorage.getItem("userId");let s=localStorage.getItem("userProfilePicture"),a=localStorage.getItem("userName"),r=localStorage.getItem("rememberedEmail"),n=localStorage.getItem("rememberedPassword"),o="true"===localStorage.getItem("rememberMe");r&&n&&o&&(N({email:r,password:n}),D(!0)),e&&(d(!0),p(t||"user"),s&&T(s),a&&I(a))},[]);let V=async e=>{e.preventDefault();let t=new FormData;t.append("email",o);let s=await m.Z.post("/api/email",t);s.data.success?(f.toast.success(s.data.msg),i("")):f.toast.error("Error")},X=e=>{N({...j,[e.target.name]:e.target.value})},Y=e=>{C({...k,[e.target.name]:e.target.value})},Q=async e=>{e.preventDefault();try{let e=await m.Z.post("/api/auth",{email:j.email,password:j.password});e.data.success?(localStorage.setItem("authToken",e.data.token||"dummy-token"),localStorage.setItem("userRole",e.data.user.role),localStorage.setItem("userId",e.data.user.id),localStorage.setItem("userProfilePicture",e.data.user.profilePicture),localStorage.setItem("userName",e.data.user.name||""),$?(localStorage.setItem("rememberedEmail",j.email),localStorage.setItem("rememberedPassword",j.password),localStorage.setItem("rememberMe","true")):(localStorage.removeItem("rememberedEmail"),localStorage.removeItem("rememberedPassword"),localStorage.removeItem("rememberMe")),d(!0),p(e.data.user.role),T(e.data.user.profilePicture),I(e.data.user.name||""),x(!1),"admin"===e.data.user.role?(f.toast.success("Login successful"),window.location.href="/admin"):(f.toast.success("Login successful"),window.location.href="/")):f.toast.error("Invalid credentials")}catch(e){var t,s;console.error("Login error:",e),f.toast.error((null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.message)||"Login failed")}},G=async e=>{if(e.preventDefault(),k.password!==k.confirmPassword){f.toast.error("Passwords do not match");return}try{let e=await m.Z.post("/api/register",{email:k.email,password:k.password,role:k.role});e.data.success?(f.toast.success("Registration successful! Please login."),y(!1),N({...j,email:k.email}),C({email:"",password:"",confirmPassword:"",role:"user"})):f.toast.error(e.data.message||"Registration failed")}catch(e){var t,s;console.error("Registration error:",e),f.toast.error((null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.message)||"Registration failed")}},K=()=>{y(!v)};return(0,a.jsxs)("div",{className:"py-5 px-5 md:px-12 lg:px-28",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)(l(),{src:n.L.logo,width:180,alt:"",className:"w-[130px] sm:w-auto"}),(0,a.jsx)("div",{className:"flex gap-3",children:c?(0,a.jsx)("div",{className:"relative",children:(0,a.jsxs)("button",{onClick:()=>s.push("/profile"),className:"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-solid border-black shadow-[-7px_7px_0px_#000000]",children:[(0,a.jsx)(l(),{src:E,width:24,height:24,alt:"Account",className:"w-6 h-6 rounded-full object-cover"}),(0,a.jsx)("span",{children:S||"Account"})]})}):(0,a.jsxs)("button",{onClick:()=>x(!0),className:"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-solid border-black shadow-[-7px_7px_0px_#000000]",children:["Get started ",(0,a.jsx)(l(),{src:n.L.arrow,alt:""})]})})]}),g&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white p-8 rounded-md shadow-lg w-96",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-4",children:v?"Register":"Login"}),v?(0,a.jsxs)("form",{onSubmit:G,children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-gray-700 mb-2",children:"Email"}),(0,a.jsx)("input",{type:"email",name:"email",value:k.email,onChange:Y,className:"w-full px-3 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-gray-700 mb-2",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:A?"text":"password",name:"password",value:k.password,onChange:Y,className:"w-full px-3 py-2 border border-gray-300 rounded-md pr-10",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>{R(!A)},className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600",children:A?(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,a.jsx)("path",{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}),(0,a.jsx)("path",{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"})]}):(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,a.jsx)("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),(0,a.jsx)("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]})})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{className:"block text-gray-700 mb-2",children:"Confirm Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:z?"text":"password",name:"confirmPassword",value:k.confirmPassword,onChange:Y,className:"w-full px-3 py-2 border border-gray-300 rounded-md pr-10",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>{B(!z)},className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600",children:z?(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,a.jsx)("path",{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}),(0,a.jsx)("path",{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"})]}):(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,a.jsx)("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),(0,a.jsx)("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]})})]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("button",{type:"submit",className:"bg-black text-white px-4 py-2 rounded-md",children:"Register"}),(0,a.jsx)("button",{type:"button",onClick:()=>x(!1),className:"text-gray-600 px-4 py-2",children:"Cancel"})]}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,a.jsx)("button",{type:"button",onClick:K,className:"text-blue-600 hover:underline",children:"Login"})]})})]}):(0,a.jsxs)("form",{onSubmit:Q,children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-gray-700 mb-2",children:"Email"}),(0,a.jsx)("input",{type:"email",name:"email",value:j.email,onChange:X,className:"w-full px-3 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{className:"block text-gray-700 mb-2",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:P?"text":"password",name:"password",value:j.password,onChange:X,className:"w-full px-3 py-2 border border-gray-300 rounded-md pr-10",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>{M(!P)},className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600",children:P?(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,a.jsx)("path",{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}),(0,a.jsx)("path",{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"})]}):(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,a.jsx)("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),(0,a.jsx)("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]})})]})]}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:$,onChange:e=>D(e.target.checked),className:"mr-2"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:"Remember me"})]})}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("button",{type:"submit",className:"bg-black text-white px-4 py-2 rounded-md",children:"Login"}),(0,a.jsx)("button",{type:"button",onClick:()=>x(!1),className:"text-gray-600 px-4 py-2",children:"Cancel"})]}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,a.jsx)("button",{type:"button",onClick:K,className:"text-blue-600 hover:underline",children:"Register"})]})})]})]})}),_&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white p-6 rounded-md shadow-lg max-w-sm w-full",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Confirm Logout"}),(0,a.jsx)("p",{className:"mb-6",children:"Are you sure you want to log out? You will need to log in again to access your account."}),(0,a.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,a.jsx)("button",{onClick:()=>{L(!1)},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100",children:"Cancel"}),(0,a.jsx)("button",{onClick:()=>{localStorage.removeItem("authToken"),localStorage.removeItem("userRole"),localStorage.removeItem("userId"),localStorage.removeItem("userProfilePicture"),localStorage.removeItem("userName"),d(!1),p(""),L(!1),f.toast.success("Logged out successfully"),setTimeout(()=>{window.location.href="/"},300)},className:"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800",children:"Logout"})]})]})}),(0,a.jsxs)("div",{className:"text-center my-8",children:[(0,a.jsx)("h1",{className:"text-3xl sm:text-5xl font-medium",children:"Mr.Blogger"}),(0,a.jsx)("p",{className:"mt-10 max-w-[740px] m-auto text-xs sm:text-base",children:"Discover insightful articles, trending tech news, startup journeys, and lifestyle stories — all in one place. Welcome to your daily dose of inspiration and knowledge."}),(0,a.jsxs)("form",{onSubmit:O?e=>{if(e.preventDefault(),!Z.trim()){f.toast.error("Please enter a search term");return}t(Z)}:V,className:"flex justify-between max-w-[500px] scale-75 sm:scale-100 mx-auto mt-10 border border-black shadow-[-7px_7px_0px_#000000]",action:"",children:[(0,a.jsx)("input",{onChange:O?e=>{W(e.target.value),""===e.target.value&&t("")}:e=>i(e.target.value),value:O?Z:o,type:O?"text":"email",placeholder:O?"Search blogs...":"Enter your email",className:"pl-4 outline-none flex-1",required:!0}),(0,a.jsx)("button",{type:"submit",className:"border-l border-black py-4 px-4 sm:px-8 active:bg-gray-600 active:text-white",children:O?"Search":"Subscribe"}),(0,a.jsxs)("div",{className:"relative flex items-center",children:[(0,a.jsx)("button",{type:"button",onClick:()=>{F(e=>(e&&(W(""),t("")),!e))},onMouseEnter:()=>H(!0),onMouseLeave:()=>H(!1),className:"flex items-center justify-center px-4 border-l border-black hover:bg-gray-100 transition-colors",style:{minWidth:"56px"},children:O?(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"22",height:"22",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:"2",children:[(0,a.jsx)("rect",{x:"3",y:"5",width:"18",height:"14",rx:"2",fill:"none",stroke:"currentColor",strokeWidth:"2"}),(0,a.jsx)("polyline",{points:"3,7 12,13 21,7",fill:"none",stroke:"currentColor",strokeWidth:"2"})]}):(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"22",height:"22",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:"2",children:[(0,a.jsx)("circle",{cx:"11",cy:"11",r:"8",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,a.jsx)("line",{x1:"21",y1:"21",x2:"16.65",y2:"16.65",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round"})]})}),q&&(0,a.jsx)("div",{className:"absolute -top-10 left-1/2 -translate-x-1/2 bg-black text-white text-xs rounded px-3 py-1 shadow-lg animate-fade-in z-10 whitespace-nowrap",children:O?"Switch to Subscribe mode":"Switch to Search mode"})]})]})]})]})};s(8062);var v=s(3799);function y(){let[e,t]=(0,r.useState)(""),[s,n]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{n(!0),(0,v.Z0)("/","home")},[]),s)?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.ToastContainer,{theme:"dark"}),(0,a.jsx)(x,{setSearchTerm:t}),(0,a.jsx)(p,{searchTerm:e}),(0,a.jsx)(g.Z,{})]}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-xl",children:"Loading..."})})}},3799:function(e,t,s){"use strict";s.d(t,{Z0:function(){return r},Z5:function(){return o},uf:function(){return n}});var a=s(2173);let r=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"page",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;try{console.log("Tracking page view:",{path:e,contentType:t,blogId:s});let r=document.referrer||null,n=await a.Z.post("/api/analytics",{path:e,contentType:t,blogId:s,referrer:r});console.log("Analytics tracking response:",n.data)}catch(e){console.error("Analytics error:",e)}},n=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),o=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"7days",t=localStorage.getItem("authToken");if(!t)throw Error("Authentication required");return(await a.Z.get("/api/analytics?period=".concat(e),{headers:{Authorization:"Bearer ".concat(t)}})).data}},8347:function(e,t,s){"use strict";s.d(t,{A:function(){return r},D:function(){return a}});let a=e=>{if(!e)return"";let t=e;return(t=(t=t.replace(/\{\{image:[^}]+\}\}/g,"")).replace(/\[\[[^\]]+\]\]/g,"")).replace(/\s+/g," ").trim()},r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:120,s=a(e);return s.length>t?s.substring(0,t)+"...":s}},8062:function(){},4033:function(e,t,s){e.exports=s(5313)},7948:function(e,t,s){"use strict";s.r(t),s.d(t,{Bounce:function(){return R},Flip:function(){return $},Icons:function(){return P},Slide:function(){return z},ToastContainer:function(){return O},Zoom:function(){return B},collapseToast:function(){return d},cssTransition:function(){return u},toast:function(){return I},useToast:function(){return j},useToastContainer:function(){return w}});var a=s(2265),r=function(){for(var e,t,s=0,a="",r=arguments.length;s<r;s++)(e=arguments[s])&&(t=function e(t){var s,a,r="";if("string"==typeof t||"number"==typeof t)r+=t;else if("object"==typeof t){if(Array.isArray(t)){var n=t.length;for(s=0;s<n;s++)t[s]&&(a=e(t[s]))&&(r&&(r+=" "),r+=a)}else for(a in t)t[a]&&(r&&(r+=" "),r+=a)}return r}(e))&&(a&&(a+=" "),a+=t);return a};let n=e=>"number"==typeof e&&!isNaN(e),o=e=>"string"==typeof e,l=e=>"function"==typeof e,i=e=>o(e)||l(e)?e:null,c=e=>(0,a.isValidElement)(e)||o(e)||l(e)||n(e);function d(e,t,s){void 0===s&&(s=300);let{scrollHeight:a,style:r}=e;requestAnimationFrame(()=>{r.minHeight="initial",r.height=a+"px",r.transition=`all ${s}ms`,requestAnimationFrame(()=>{r.height="0",r.padding="0",r.margin="0",setTimeout(t,s)})})}function u(e){let{enter:t,exit:s,appendPosition:r=!1,collapse:n=!0,collapseDuration:o=300}=e;return function(e){let{children:l,position:i,preventExitTransition:c,done:u,nodeRef:m,isIn:p,playToast:g}=e,h=r?`${t}--${i}`:t,f=r?`${s}--${i}`:s,x=(0,a.useRef)(0);return(0,a.useLayoutEffect)(()=>{let e=m.current,t=h.split(" "),s=a=>{a.target===m.current&&(g(),e.removeEventListener("animationend",s),e.removeEventListener("animationcancel",s),0===x.current&&"animationcancel"!==a.type&&e.classList.remove(...t))};e.classList.add(...t),e.addEventListener("animationend",s),e.addEventListener("animationcancel",s)},[]),(0,a.useEffect)(()=>{let e=m.current,t=()=>{e.removeEventListener("animationend",t),n?d(e,u,o):u()};p||(c?t():(x.current=1,e.className+=` ${f}`,e.addEventListener("animationend",t)))},[p]),a.createElement(a.Fragment,null,l)}}function m(e,t){return null!=e?{content:e.content,containerId:e.props.containerId,id:e.props.toastId,theme:e.props.theme,type:e.props.type,data:e.props.data||{},isLoading:e.props.isLoading,icon:e.props.icon,status:t}:{}}let p=new Map,g=[],h=new Set,f=e=>h.forEach(t=>t(e)),x=()=>p.size>0;function v(e,t){var s;if(t)return!(null==(s=p.get(t))||!s.isToastActive(e));let a=!1;return p.forEach(t=>{t.isToastActive(e)&&(a=!0)}),a}function y(e,t){c(e)&&(x()||g.push({content:e,options:t}),p.forEach(s=>{s.buildToast(e,t)}))}function b(e,t){p.forEach(s=>{null!=t&&null!=t&&t.containerId?(null==t?void 0:t.containerId)===s.id&&s.toggle(e,null==t?void 0:t.id):s.toggle(e,null==t?void 0:t.id)})}function w(e){let{subscribe:t,getSnapshot:s,setProps:r}=(0,a.useRef)(function(e){let t=e.containerId||1;return{subscribe(s){let r=function(e,t,s){let r=1,d=0,u=[],p=[],g=[],h=t,f=new Map,x=new Set,v=()=>{g=Array.from(f.values()),x.forEach(e=>e())},y=e=>{p=null==e?[]:p.filter(t=>t!==e),v()},b=e=>{let{toastId:t,onOpen:r,updateId:n,children:o}=e.props,i=null==n;e.staleId&&f.delete(e.staleId),f.set(t,e),p=[...p,e.props.toastId].filter(t=>t!==e.staleId),v(),s(m(e,i?"added":"updated")),i&&l(r)&&r((0,a.isValidElement)(o)&&o.props)};return{id:e,props:h,observe:e=>(x.add(e),()=>x.delete(e)),toggle:(e,t)=>{f.forEach(s=>{null!=t&&t!==s.props.toastId||l(s.toggle)&&s.toggle(e)})},removeToast:y,toasts:f,clearQueue:()=>{d-=u.length,u=[]},buildToast:(t,p)=>{var g,x;if((t=>{let{containerId:s,toastId:a,updateId:r}=t,n=f.has(a)&&null==r;return(s?s!==e:1!==e)||n})(p))return;let{toastId:w,updateId:j,data:N,staleId:k,delay:C}=p,E=()=>{y(w)},T=null==j;T&&d++;let S={...h,style:h.toastStyle,key:r++,...Object.fromEntries(Object.entries(p).filter(e=>{let[t,s]=e;return null!=s})),toastId:w,updateId:j,data:N,closeToast:E,isIn:!1,className:i(p.className||h.toastClassName),bodyClassName:i(p.bodyClassName||h.bodyClassName),progressClassName:i(p.progressClassName||h.progressClassName),autoClose:!p.isLoading&&(g=p.autoClose,x=h.autoClose,!1===g||n(g)&&g>0?g:x),deleteToast(){let e=f.get(w),{onClose:t,children:r}=e.props;l(t)&&t((0,a.isValidElement)(r)&&r.props),s(m(e,"removed")),f.delete(w),--d<0&&(d=0),u.length>0?b(u.shift()):v()}};S.closeButton=h.closeButton,!1===p.closeButton||c(p.closeButton)?S.closeButton=p.closeButton:!0===p.closeButton&&(S.closeButton=!c(h.closeButton)||h.closeButton);let I=t;(0,a.isValidElement)(t)&&!o(t.type)?I=(0,a.cloneElement)(t,{closeToast:E,toastProps:S,data:N}):l(t)&&(I=t({closeToast:E,toastProps:S,data:N}));let _={content:I,props:S,staleId:k};h.limit&&h.limit>0&&d>h.limit&&T?u.push(_):n(C)?setTimeout(()=>{b(_)},C):b(_)},setProps(e){h=e},setToggle:(e,t)=>{f.get(e).toggle=t},isToastActive:e=>p.some(t=>t===e),getSnapshot:()=>h.newestOnTop?g.reverse():g}}(t,e,f);p.set(t,r);let d=r.observe(s);return g.forEach(e=>y(e.content,e.options)),g=[],()=>{d(),p.delete(t)}},setProps(e){var s;null==(s=p.get(t))||s.setProps(e)},getSnapshot(){var e;return null==(e=p.get(t))?void 0:e.getSnapshot()}}}(e)).current;r(e);let d=(0,a.useSyncExternalStore)(t,s,s);return{getToastToRender:function(e){if(!d)return[];let t=new Map;return d.forEach(e=>{let{position:s}=e.props;t.has(s)||t.set(s,[]),t.get(s).push(e)}),Array.from(t,t=>e(t[0],t[1]))},isToastActive:v,count:null==d?void 0:d.length}}function j(e){var t,s;let[r,n]=(0,a.useState)(!1),[o,l]=(0,a.useState)(!1),i=(0,a.useRef)(null),c=(0,a.useRef)({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:d,pauseOnHover:u,closeToast:m,onClick:g,closeOnClick:h}=e;function f(){n(!0)}function x(){n(!1)}function v(t){let s=i.current;c.canDrag&&s&&(c.didMove=!0,r&&x(),c.delta="x"===e.draggableDirection?t.clientX-c.start:t.clientY-c.start,c.start!==t.clientX&&(c.canCloseOnClick=!1),s.style.transform=`translate3d(${"x"===e.draggableDirection?`${c.delta}px, var(--y)`:`0, calc(${c.delta}px + var(--y))`},0)`,s.style.opacity=""+(1-Math.abs(c.delta/c.removalDistance)))}function y(){document.removeEventListener("pointermove",v),document.removeEventListener("pointerup",y);let t=i.current;if(c.canDrag&&c.didMove&&t){if(c.canDrag=!1,Math.abs(c.delta)>c.removalDistance)return l(!0),e.closeToast(),void e.collapseAll();t.style.transition="transform 0.2s, opacity 0.2s",t.style.removeProperty("transform"),t.style.removeProperty("opacity")}}null==(s=p.get((t={id:e.toastId,containerId:e.containerId,fn:n}).containerId||1))||s.setToggle(t.id,t.fn),(0,a.useEffect)(()=>{if(e.pauseOnFocusLoss)return document.hasFocus()||x(),window.addEventListener("focus",f),window.addEventListener("blur",x),()=>{window.removeEventListener("focus",f),window.removeEventListener("blur",x)}},[e.pauseOnFocusLoss]);let b={onPointerDown:function(t){if(!0===e.draggable||e.draggable===t.pointerType){c.didMove=!1,document.addEventListener("pointermove",v),document.addEventListener("pointerup",y);let s=i.current;c.canCloseOnClick=!0,c.canDrag=!0,s.style.transition="none","x"===e.draggableDirection?(c.start=t.clientX,c.removalDistance=s.offsetWidth*(e.draggablePercent/100)):(c.start=t.clientY,c.removalDistance=s.offsetHeight*(80===e.draggablePercent?1.5*e.draggablePercent:e.draggablePercent)/100)}},onPointerUp:function(t){let{top:s,bottom:a,left:r,right:n}=i.current.getBoundingClientRect();"touchend"!==t.nativeEvent.type&&e.pauseOnHover&&t.clientX>=r&&t.clientX<=n&&t.clientY>=s&&t.clientY<=a?x():f()}};return d&&u&&(b.onMouseEnter=x,e.stacked||(b.onMouseLeave=f)),h&&(b.onClick=e=>{g&&g(e),c.canCloseOnClick&&m()}),{playToast:f,pauseToast:x,isRunning:r,preventExitTransition:o,toastRef:i,eventHandlers:b}}function N(e){let{delay:t,isRunning:s,closeToast:n,type:o="default",hide:i,className:c,style:d,controlledProgress:u,progress:m,rtl:p,isIn:g,theme:h}=e,f=i||u&&0===m,x={...d,animationDuration:`${t}ms`,animationPlayState:s?"running":"paused"};u&&(x.transform=`scaleX(${m})`);let v=r("Toastify__progress-bar",u?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${h}`,`Toastify__progress-bar--${o}`,{"Toastify__progress-bar--rtl":p}),y=l(c)?c({rtl:p,type:o,defaultClassName:v}):r(v,c);return a.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":f},a.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${h} Toastify__progress-bar--${o}`}),a.createElement("div",{role:"progressbar","aria-hidden":f?"true":"false","aria-label":"notification timer",className:y,style:x,[u&&m>=1?"onTransitionEnd":"onAnimationEnd"]:u&&m<1?null:()=>{g&&n()}}))}let k=1,C=()=>""+k++;function E(e,t){return y(e,t),t.toastId}function T(e,t){return{...t,type:t&&t.type||e,toastId:t&&(o(t.toastId)||n(t.toastId))?t.toastId:C()}}function S(e){return(t,s)=>E(t,T(e,s))}function I(e,t){return E(e,T("default",t))}I.loading=(e,t)=>E(e,T("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...t})),I.promise=function(e,t,s){let a,{pending:r,error:n,success:i}=t;r&&(a=o(r)?I.loading(r,s):I.loading(r.render,{...s,...r}));let c={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},d=(e,t,r)=>{if(null==t)return void I.dismiss(a);let n={type:e,...c,...s,data:r},l=o(t)?{render:t}:t;return a?I.update(a,{...n,...l}):I(l.render,{...n,...l}),r},u=l(e)?e():e;return u.then(e=>d("success",i,e)).catch(e=>d("error",n,e)),u},I.success=S("success"),I.info=S("info"),I.error=S("error"),I.warning=S("warning"),I.warn=I.warning,I.dark=(e,t)=>E(e,T("default",{theme:"dark",...t})),I.dismiss=function(e){var t,s;x()?null==e||o(t=e)||n(t)?p.forEach(t=>{t.removeToast(e)}):e&&("containerId"in e||"id"in e)&&((null==(s=p.get(e.containerId))?void 0:s.removeToast(e.id))||p.forEach(t=>{t.removeToast(e.id)})):g=g.filter(t=>null!=e&&t.options.toastId!==e)},I.clearWaitingQueue=function(e){void 0===e&&(e={}),p.forEach(t=>{!t.props.limit||e.containerId&&t.id!==e.containerId||t.clearQueue()})},I.isActive=v,I.update=function(e,t){void 0===t&&(t={});let s=((e,t)=>{var s;let{containerId:a}=t;return null==(s=p.get(a||1))?void 0:s.toasts.get(e)})(e,t);if(s){let{props:a,content:r}=s,n={delay:100,...a,...t,toastId:t.toastId||e,updateId:C()};n.toastId!==e&&(n.staleId=e);let o=n.render||r;delete n.render,E(o,n)}},I.done=e=>{I.update(e,{progress:1})},I.onChange=function(e){return h.add(e),()=>{h.delete(e)}},I.play=e=>b(!0,e),I.pause=e=>b(!1,e);let _="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,L=e=>{let{theme:t,type:s,isLoading:r,...n}=e;return a.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:"colored"===t?"currentColor":`var(--toastify-icon-color-${s})`,...n})},P={info:function(e){return a.createElement(L,{...e},a.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))},warning:function(e){return a.createElement(L,{...e},a.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))},success:function(e){return a.createElement(L,{...e},a.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))},error:function(e){return a.createElement(L,{...e},a.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))},spinner:function(){return a.createElement("div",{className:"Toastify__spinner"})}},M=e=>{let{isRunning:t,preventExitTransition:s,toastRef:n,eventHandlers:o,playToast:i}=j(e),{closeButton:c,children:d,autoClose:u,onClick:m,type:p,hideProgressBar:g,closeToast:h,transition:f,position:x,className:v,style:y,bodyClassName:b,bodyStyle:w,progressClassName:k,progressStyle:C,updateId:E,role:T,progress:S,rtl:I,toastId:_,deleteToast:L,isIn:M,isLoading:A,closeOnClick:R,theme:z}=e,B=r("Toastify__toast",`Toastify__toast-theme--${z}`,`Toastify__toast--${p}`,{"Toastify__toast--rtl":I},{"Toastify__toast--close-on-click":R}),$=l(v)?v({rtl:I,position:x,type:p,defaultClassName:B}):r(B,v),D=function(e){let{theme:t,type:s,isLoading:r,icon:n}=e,o=null,i={theme:t,type:s,isLoading:r};return!1===n||(l(n)?o=n(i):(0,a.isValidElement)(n)?o=(0,a.cloneElement)(n,i):r?o=P.spinner():s in P&&(o=P[s](i))),o}(e),O=!!S||!u,F={closeToast:h,type:p,theme:z},Z=null;return!1===c||(Z=l(c)?c(F):(0,a.isValidElement)(c)?(0,a.cloneElement)(c,F):function(e){let{closeToast:t,theme:s,ariaLabel:r="close"}=e;return a.createElement("button",{className:`Toastify__close-button Toastify__close-button--${s}`,type:"button",onClick:e=>{e.stopPropagation(),t(e)},"aria-label":r},a.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},a.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}(F)),a.createElement(f,{isIn:M,done:L,position:x,preventExitTransition:s,nodeRef:n,playToast:i},a.createElement("div",{id:_,onClick:m,"data-in":M,className:$,...o,style:y,ref:n},a.createElement("div",{...M&&{role:T},className:l(b)?b({type:p}):r("Toastify__toast-body",b),style:w},null!=D&&a.createElement("div",{className:r("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!A})},D),a.createElement("div",null,d)),Z,a.createElement(N,{...E&&!O?{key:`pb-${E}`}:{},rtl:I,theme:z,delay:u,isRunning:t,isIn:M,closeToast:h,hide:g,type:p,style:C,className:k,controlledProgress:O,progress:S||0})))},A=function(e,t){return void 0===t&&(t=!1),{enter:`Toastify--animate Toastify__${e}-enter`,exit:`Toastify--animate Toastify__${e}-exit`,appendPosition:t}},R=u(A("bounce",!0)),z=u(A("slide",!0)),B=u(A("zoom")),$=u(A("flip")),D={position:"top-right",transition:R,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light"};function O(e){let t={...D,...e},s=e.stacked,[n,o]=(0,a.useState)(!0),c=(0,a.useRef)(null),{getToastToRender:d,isToastActive:u,count:m}=w(t),{className:p,style:g,rtl:h,containerId:f}=t;function x(){s&&(o(!0),I.play())}return _(()=>{if(s){var e;let s=c.current.querySelectorAll('[data-in="true"]'),a=null==(e=t.position)?void 0:e.includes("top"),r=0,o=0;Array.from(s).reverse().forEach((e,t)=>{e.classList.add("Toastify__toast--stacked"),t>0&&(e.dataset.collapsed=`${n}`),e.dataset.pos||(e.dataset.pos=a?"top":"bot");let s=r*(n?.2:1)+(n?0:12*t);e.style.setProperty("--y",`${a?s:-1*s}px`),e.style.setProperty("--g","12"),e.style.setProperty("--s",""+(1-(n?o:0))),r+=e.offsetHeight,o+=.025})}},[n,m,s]),a.createElement("div",{ref:c,className:"Toastify",id:f,onMouseEnter:()=>{s&&(o(!1),I.pause())},onMouseLeave:x},d((e,t)=>{let n=t.length?{...g}:{...g,pointerEvents:"none"};return a.createElement("div",{className:function(e){let t=r("Toastify__toast-container",`Toastify__toast-container--${e}`,{"Toastify__toast-container--rtl":h});return l(p)?p({position:e,rtl:h,defaultClassName:t}):r(t,i(p))}(e),style:n,key:`container-${e}`},t.map(e=>{let{content:t,props:r}=e;return a.createElement(M,{...r,stacked:s,collapseAll:x,isIn:u(r.toastId,r.containerId),style:r.style,key:`toast-${r.key}`},t)}))}))}}},function(e){e.O(0,[580,691,396,637,971,938,744],function(){return e(e.s=264)}),_N_E=e.O()}]);
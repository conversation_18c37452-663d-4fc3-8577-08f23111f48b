"use strict";(()=>{var e={};e.id=9961,e.ids=[9961],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},49641:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>C,originalPathname:()=>v,patchFetch:()=>S,requestAsyncStorage:()=>h,routeModule:()=>f,serverHooks:()=>j,staticGenerationAsyncStorage:()=>w,staticGenerationBailout:()=>x});var s={};r.r(s),r.d(s,{DELETE:()=>p,GET:()=>m,POST:()=>y});var a=r(95419),o=r(69108),n=r(99678),c=r(91887),i=r(78070),u=r(11185),l=r.n(u);let g=new(l()).Schema({name:{type:String,required:!0,unique:!0,trim:!0},date:{type:Date,default:Date.now}}),d=l().models.Category||l().model("Category",g);async function m(){try{await (0,c.n)();let e=await d.find().sort({name:1});if(0===e.length){for(let e of["Startup","Technology","Lifestyle"])await d.create({name:e});let e=await d.find().sort({name:1});return console.log("Created default categories:",e),i.Z.json({success:!0,categories:e})}return console.log("Fetched categories:",e),i.Z.json({success:!0,categories:e})}catch(e){return console.error("Error fetching categories:",e),i.Z.json({success:!1,message:"Failed to fetch categories"},{status:500})}}async function y(e){try{await (0,c.n)();let{name:t}=await e.json();if(!t||!t.trim())return i.Z.json({success:!1,message:"Category name is required"},{status:400});if(await d.findOne({name:{$regex:RegExp(`^${t.trim()}$`,"i")}}))return i.Z.json({success:!1,message:"Category already exists"},{status:409});let r=await d.create({name:t.trim()});console.log("New category created:",r);let s=await d.find().sort({name:1});return i.Z.json({success:!0,message:"Category created successfully",category:r,categories:s})}catch(e){return console.error("Error creating category:",e),i.Z.json({success:!1,message:"Failed to create category"},{status:500})}}async function p(e){try{await (0,c.n)();let t=e.nextUrl.searchParams.get("id");if(!t)return i.Z.json({success:!1,message:"Category ID is required"},{status:400});if(!await d.findById(t))return i.Z.json({success:!1,message:"Category not found"},{status:404});await d.findByIdAndDelete(t);let r=await d.find().sort({name:1});return i.Z.json({success:!0,message:"Category deleted successfully",categories:r})}catch(e){return console.error("Error deleting category:",e),i.Z.json({success:!1,message:"Failed to delete category"},{status:500})}}(async()=>{await (0,c.n)()})();let f=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/categories/route",pathname:"/api/categories",filename:"route",bundlePath:"app/api/categories/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\categories\\route.js",nextConfigOutput:"",userland:s}),{requestAsyncStorage:h,staticGenerationAsyncStorage:w,serverHooks:j,headerHooks:C,staticGenerationBailout:x}=f,v="/api/categories/route";function S(){return(0,n.patchFetch)({serverHooks:j,staticGenerationAsyncStorage:w})}},91887:(e,t,r)=>{r.d(t,{n:()=>o});var s=r(11185),a=r.n(s);let o=async()=>{try{if(a().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await a().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},78070:(e,t,r)=>{Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return s.NextResponse}});let s=r(70457)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,2993],()=>r(49641));module.exports=s})();
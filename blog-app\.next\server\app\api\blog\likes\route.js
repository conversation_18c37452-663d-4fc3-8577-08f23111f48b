"use strict";(()=>{var e={};e.id=7853,e.ids=[7853],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},67323:(e,t,o)=>{o.r(t),o.d(t,{headerHooks:()=>b,originalPathname:()=>k,patchFetch:()=>f,requestAsyncStorage:()=>p,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>h});var r={};o.r(r),o.d(r,{GET:()=>c});var n=o(95419),s=o(69108),a=o(99678),i=o(78070),u=o(91887),l=o(27476);async function c(e){try{await (0,u.n)();let{searchParams:t}=new URL(e.url),o=t.get("id");if(!o)return i.Z.json({success:!1,message:"Blog ID is required"},{status:400});let r=await l.Z.countDocuments({blogId:o});return i.Z.json({success:!0,count:r})}catch(e){return console.error("Error fetching likes count:",e),i.Z.json({success:!1,message:"Failed to fetch likes count"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/blog/likes/route",pathname:"/api/blog/likes",filename:"route",bundlePath:"app/api/blog/likes/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\blog\\likes\\route.js",nextConfigOutput:"",userland:r}),{requestAsyncStorage:p,staticGenerationAsyncStorage:g,serverHooks:m,headerHooks:b,staticGenerationBailout:h}=d,k="/api/blog/likes/route";function f(){return(0,a.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:g})}},91887:(e,t,o)=>{o.d(t,{n:()=>s});var r=o(11185),n=o.n(r);let s=async()=>{try{if(n().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await n().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},27476:(e,t,o)=>{o.d(t,{Z:()=>a});var r=o(11185),n=o.n(r);let s=new(n()).Schema({userId:{type:String,required:!0},blogId:{type:n().Schema.Types.ObjectId,ref:"Blog",required:!0},createdAt:{type:Date,default:Date.now}});s.index({userId:1,blogId:1},{unique:!0});let a=n().models.Like||n().model("Like",s)},78070:(e,t,o)=>{Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return r.NextResponse}});let r=o(70457)}};var t=require("../../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),r=t.X(0,[1638,2993],()=>o(67323));module.exports=r})();
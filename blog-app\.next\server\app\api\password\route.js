"use strict";(()=>{var e={};e.id=3180,e.ids=[3180],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},50967:(e,r,s)=>{s.r(r),s.d(r,{headerHooks:()=>f,originalPathname:()=>y,patchFetch:()=>v,requestAsyncStorage:()=>l,routeModule:()=>p,serverHooks:()=>m,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>w});var t={};s.r(t),s.d(t,{PUT:()=>c});var o=s(95419),a=s(69108),n=s(99678),u=s(91887),i=s(96488),d=s(78070);async function c(e){try{let{userId:r,currentPassword:s,newPassword:t}=await e.json();if(!r||!s||!t)return d.Z.json({success:!1,message:"Missing required fields"},{status:400});let o=await i.Z.findById(r);if(!o)return d.Z.json({success:!1,message:"User not found"},{status:404});if(o.password!==s)return d.Z.json({success:!1,message:"Current password is incorrect"},{status:401});return o.password=t,await o.save(),d.Z.json({success:!0,message:"Password updated successfully"})}catch(e){return console.error("Password update error:",e),d.Z.json({success:!1,message:"Server error"},{status:500})}}(async()=>{await (0,u.n)()})();let p=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/password/route",pathname:"/api/password",filename:"route",bundlePath:"app/api/password/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\password\\route.js",nextConfigOutput:"",userland:t}),{requestAsyncStorage:l,staticGenerationAsyncStorage:g,serverHooks:m,headerHooks:f,staticGenerationBailout:w}=p,y="/api/password/route";function v(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:g})}},91887:(e,r,s)=>{s.d(r,{n:()=>a});var t=s(11185),o=s.n(t);let a=async()=>{try{if(o().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await o().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},96488:(e,r,s)=>{s.d(r,{Z:()=>n});var t=s(11185),o=s.n(t);let a=new(o()).Schema({email:{type:String,required:!0,unique:!0},password:{type:String,required:!0},role:{type:String,default:"user",enum:["user","admin"]},profilePicture:{type:String,default:"/default_profile.png"},name:{type:String,default:""},date:{type:Date,default:Date.now()}}),n=o().models.user||o().model("user",a)},78070:(e,r,s)=>{Object.defineProperty(r,"Z",{enumerable:!0,get:function(){return t.NextResponse}});let t=s(70457)}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[1638,2993],()=>s(50967));module.exports=t})();
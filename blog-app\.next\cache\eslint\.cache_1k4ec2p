[{"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\about\\page.jsx": "1", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\addBlog\\page.jsx": "2", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\addUser\\page.jsx": "3", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\blogList\\page.jsx": "4", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\editAuthor\\[id]\\page.jsx": "5", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\editBlog\\[id]\\page.jsx": "6", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\feedback\\page.jsx": "7", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\layout.jsx": "8", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\page.jsx": "9", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\profile\\page.jsx": "10", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\reactions\\page.jsx": "11", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\settings\\page.jsx": "12", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\subscriptions\\page.jsx": "13", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\traffic\\page.jsx": "14", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\activity\\route.js": "15", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\analytics\\debug\\route.js": "16", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\analytics\\route.js": "17", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\auth\\route.js": "18", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\authors\\route.js": "19", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\authors\\[id]\\route.js": "20", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\blog\\analytics\\route.js": "21", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\blog\\likes\\route.js": "22", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\blog\\route.js": "23", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\blog\\trending\\route.js": "24", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\categories\\route.js": "25", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\debug\\route.js": "26", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\email\\route.js": "27", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\favorites\\route.js": "28", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\feedback\\route.js": "29", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\images\\route.js": "30", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\images\\[id]\\route.js": "31", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\likes\\route.js": "32", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\password\\route.js": "33", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\profile\\route.js": "34", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\register\\route.js": "35", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\upload\\image\\route.js": "36", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\users\\route.js": "37", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\blogs\\[id]\\page.jsx": "38", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\contact\\page.jsx": "39", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\debug\\page.jsx": "40", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\favorites\\page.jsx": "41", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.js": "42", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx": "43", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\login-test\\page.jsx": "44", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\page.jsx": "45", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\privacy-policy\\page.jsx": "46", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\profile\\favorites\\page.jsx": "47", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\profile\\page.jsx": "48", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\AdminComponents\\BlogTableItem.jsx": "49", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\AdminComponents\\Sidebar.jsx": "50", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\AdminComponents\\SubsTableItem.jsx": "51", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\BlogItem.jsx": "52", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\BlogList.jsx": "53", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\CookieConsent.jsx": "54", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\EmailSubscriptionPopup.jsx": "55", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\Footer.jsx": "56", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\Header.jsx": "57", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\Icons.jsx": "58", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\TestImage.jsx": "59", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\TrendingBlogs.jsx": "60", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\config\\db.js": "61", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\models\\AnalyticsModel.js": "62", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\models\\BlogModel.js": "63", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\models\\CategoryModel.js": "64", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\models\\EmailModel.js": "65", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\models\\FavoriteModel.js": "66", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\models\\ImageModel.js": "67", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\models\\LikeModel.js": "68", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\models\\UserModel.js": "69", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\utils\\auth.js": "70", "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\utils\\token.js": "71"}, {"size": 3736, "mtime": 1751893183106, "results": "72", "hashOfConfig": "73"}, {"size": 37071, "mtime": 1751879269489, "results": "74", "hashOfConfig": "73"}, {"size": 12291, "mtime": 1747994206558, "results": "75", "hashOfConfig": "73"}, {"size": 607, "mtime": 1748960275613, "results": "76", "hashOfConfig": "73"}, {"size": 9719, "mtime": 1748791896899, "results": "77", "hashOfConfig": "73"}, {"size": 33001, "mtime": 1751879382778, "results": "78", "hashOfConfig": "73"}, {"size": 5532, "mtime": 1748096182788, "results": "79", "hashOfConfig": "73"}, {"size": 4787, "mtime": 1748963836973, "results": "80", "hashOfConfig": "73"}, {"size": 9185, "mtime": 1749226051268, "results": "81", "hashOfConfig": "73"}, {"size": 19740, "mtime": 1751873954991, "results": "82", "hashOfConfig": "73"}, {"size": 7754, "mtime": 1749226033218, "results": "83", "hashOfConfig": "73"}, {"size": 19051, "mtime": 1748792063669, "results": "84", "hashOfConfig": "73"}, {"size": 1854, "mtime": 1751893265665, "results": "85", "hashOfConfig": "73"}, {"size": 10652, "mtime": 1751893303827, "results": "86", "hashOfConfig": "73"}, {"size": 1888, "mtime": 1747719654303, "results": "87", "hashOfConfig": "73"}, {"size": 947, "mtime": 1749278240147, "results": "88", "hashOfConfig": "73"}, {"size": 5601, "mtime": 1749278240158, "results": "89", "hashOfConfig": "73"}, {"size": 1686, "mtime": 1747994630550, "results": "90", "hashOfConfig": "73"}, {"size": 3300, "mtime": 1748170374381, "results": "91", "hashOfConfig": "73"}, {"size": 2494, "mtime": 1748791535238, "results": "92", "hashOfConfig": "73"}, {"size": 1682, "mtime": 1749278240149, "results": "93", "hashOfConfig": "73"}, {"size": 808, "mtime": 1749222118690, "results": "94", "hashOfConfig": "73"}, {"size": 4841, "mtime": 1751884236175, "results": "95", "hashOfConfig": "73"}, {"size": 1737, "mtime": 1749283344162, "results": "96", "hashOfConfig": "73"}, {"size": 4105, "mtime": 1748002513232, "results": "97", "hashOfConfig": "73"}, {"size": 1220, "mtime": 1747994492166, "results": "98", "hashOfConfig": "73"}, {"size": 834, "mtime": 1705576370000, "results": "99", "hashOfConfig": "73"}, {"size": 4919, "mtime": 1747994630546, "results": "100", "hashOfConfig": "73"}, {"size": 2711, "mtime": 1748096385482, "results": "101", "hashOfConfig": "73"}, {"size": 1684, "mtime": 1750936077024, "results": "102", "hashOfConfig": "73"}, {"size": 2254, "mtime": 1750939418712, "results": "103", "hashOfConfig": "73"}, {"size": 2854, "mtime": 1749222118690, "results": "104", "hashOfConfig": "73"}, {"size": 1575, "mtime": 1747721593935, "results": "105", "hashOfConfig": "73"}, {"size": 4341, "mtime": 1751873954991, "results": "106", "hashOfConfig": "73"}, {"size": 1455, "mtime": 1751875184653, "results": "107", "hashOfConfig": "73"}, {"size": 2009, "mtime": 1750933734108, "results": "108", "hashOfConfig": "73"}, {"size": 1966, "mtime": 1747994206558, "results": "109", "hashOfConfig": "73"}, {"size": 30268, "mtime": 1751876391444, "results": "110", "hashOfConfig": "73"}, {"size": 15352, "mtime": 1751876098973, "results": "111", "hashOfConfig": "73"}, {"size": 2796, "mtime": 1747994542848, "results": "112", "hashOfConfig": "73"}, {"size": 6170, "mtime": 1750937501670, "results": "113", "hashOfConfig": "73"}, {"size": 408, "mtime": 1747673509950, "results": "114", "hashOfConfig": "73"}, {"size": 1032, "mtime": 1751877332495, "results": "115", "hashOfConfig": "73"}, {"size": 5846, "mtime": 1751893054247, "results": "116", "hashOfConfig": "73"}, {"size": 1093, "mtime": 1751874451391, "results": "117", "hashOfConfig": "73"}, {"size": 3598, "mtime": 1749227218088, "results": "118", "hashOfConfig": "73"}, {"size": 5666, "mtime": 1747994630544, "results": "119", "hashOfConfig": "73"}, {"size": 36203, "mtime": 1751875184654, "results": "120", "hashOfConfig": "73"}, {"size": 2184, "mtime": 1751879287049, "results": "121", "hashOfConfig": "73"}, {"size": 12980, "mtime": 1749278377153, "results": "122", "hashOfConfig": "73"}, {"size": 560, "mtime": 1705576654000, "results": "123", "hashOfConfig": "73"}, {"size": 1211, "mtime": 1751874806222, "results": "124", "hashOfConfig": "73"}, {"size": 8625, "mtime": 1751884255601, "results": "125", "hashOfConfig": "73"}, {"size": 2834, "mtime": 1749227213720, "results": "126", "hashOfConfig": "73"}, {"size": 5994, "mtime": 1751890064749, "results": "127", "hashOfConfig": "73"}, {"size": 1085, "mtime": 1749231389664, "results": "128", "hashOfConfig": "73"}, {"size": 25007, "mtime": 1751892561787, "results": "129", "hashOfConfig": "73"}, {"size": 1931, "mtime": 1749278954589, "results": "130", "hashOfConfig": "73"}, {"size": 1077, "mtime": 1747993388208, "results": "131", "hashOfConfig": "73"}, {"size": 7878, "mtime": 1751874216527, "results": "132", "hashOfConfig": "73"}, {"size": 882, "mtime": 1751884202483, "results": "133", "hashOfConfig": "73"}, {"size": 1242, "mtime": 1749278240149, "results": "134", "hashOfConfig": "73"}, {"size": 623, "mtime": 1749220687242, "results": "135", "hashOfConfig": "73"}, {"size": 433, "mtime": 1747829534287, "results": "136", "hashOfConfig": "73"}, {"size": 318, "mtime": 1705571366000, "results": "137", "hashOfConfig": "73"}, {"size": 608, "mtime": 1747994630546, "results": "138", "hashOfConfig": "73"}, {"size": 843, "mtime": 1750939726301, "results": "139", "hashOfConfig": "73"}, {"size": 495, "mtime": 1749230944442, "results": "140", "hashOfConfig": "73"}, {"size": 682, "mtime": 1747993559504, "results": "141", "hashOfConfig": "73"}, {"size": 726, "mtime": 1747994630545, "results": "142", "hashOfConfig": "73"}, {"size": 593, "mtime": 1747994630545, "results": "143", "hashOfConfig": "73"}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1v14f52", {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\about\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\addBlog\\page.jsx", ["357"], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\addUser\\page.jsx", ["358"], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\blogList\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\editAuthor\\[id]\\page.jsx", ["359"], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\editBlog\\[id]\\page.jsx", ["360", "361"], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\feedback\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\layout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\profile\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\reactions\\page.jsx", ["362", "363"], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\settings\\page.jsx", ["364", "365"], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\subscriptions\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\admin\\traffic\\page.jsx", ["366"], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\activity\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\analytics\\debug\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\analytics\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\auth\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\authors\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\authors\\[id]\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\blog\\analytics\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\blog\\likes\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\blog\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\blog\\trending\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\categories\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\debug\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\email\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\favorites\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\feedback\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\images\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\images\\[id]\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\likes\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\password\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\profile\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\register\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\upload\\image\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\users\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\blogs\\[id]\\page.jsx", ["367", "368", "369"], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\contact\\page.jsx", ["370", "371"], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\debug\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\favorites\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\layout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\login-test\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\privacy-policy\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\profile\\favorites\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\profile\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\AdminComponents\\BlogTableItem.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\AdminComponents\\Sidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\AdminComponents\\SubsTableItem.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\BlogItem.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\BlogList.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\CookieConsent.jsx", ["372", "373"], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\EmailSubscriptionPopup.jsx", ["374"], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\Header.jsx", ["375"], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\Icons.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\TestImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\components\\TrendingBlogs.jsx", ["376"], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\config\\db.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\models\\AnalyticsModel.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\models\\BlogModel.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\models\\CategoryModel.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\models\\EmailModel.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\models\\FavoriteModel.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\models\\ImageModel.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\models\\LikeModel.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\models\\UserModel.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\utils\\auth.js", [], [], "C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\lib\\utils\\token.js", [], [], {"ruleId": "377", "severity": 1, "message": "378", "line": 626, "column": 45, "nodeType": "379", "endLine": 630, "endColumn": 47}, {"ruleId": "380", "severity": 1, "message": "381", "line": 30, "column": 6, "nodeType": "382", "endLine": 30, "endColumn": 37, "suggestions": "383"}, {"ruleId": "377", "severity": 1, "message": "378", "line": 274, "column": 21, "nodeType": "379", "endLine": 278, "endColumn": 23}, {"ruleId": "380", "severity": 1, "message": "384", "line": 258, "column": 8, "nodeType": "382", "endLine": 258, "endColumn": 27, "suggestions": "385"}, {"ruleId": "377", "severity": 1, "message": "378", "line": 654, "column": 33, "nodeType": "379", "endLine": 658, "endColumn": 35}, {"ruleId": "377", "severity": 1, "message": "378", "line": 136, "column": 25, "nodeType": "379", "endLine": 140, "endColumn": 27}, {"ruleId": "377", "severity": 1, "message": "378", "line": 152, "column": 25, "nodeType": "379", "endLine": 156, "endColumn": 27}, {"ruleId": "377", "severity": 1, "message": "378", "line": 439, "column": 25, "nodeType": "379", "endLine": 443, "endColumn": 27}, {"ruleId": "377", "severity": 1, "message": "378", "line": 487, "column": 29, "nodeType": "379", "endLine": 491, "endColumn": 31}, {"ruleId": "380", "severity": 1, "message": "386", "line": 52, "column": 6, "nodeType": "382", "endLine": 52, "endColumn": 14, "suggestions": "387"}, {"ruleId": "380", "severity": 1, "message": "388", "line": 153, "column": 6, "nodeType": "382", "endLine": 153, "endColumn": 8, "suggestions": "389"}, {"ruleId": "380", "severity": 1, "message": "390", "line": 380, "column": 6, "nodeType": "382", "endLine": 380, "endColumn": 8, "suggestions": "391"}, {"ruleId": "392", "severity": 2, "message": "393", "line": 777, "column": 22, "nodeType": "394", "messageId": "395"}, {"ruleId": "392", "severity": 2, "message": "393", "line": 169, "column": 47, "nodeType": "394", "messageId": "395"}, {"ruleId": "392", "severity": 2, "message": "393", "line": 259, "column": 83, "nodeType": "394", "messageId": "395"}, {"ruleId": "392", "severity": 2, "message": "396", "line": 56, "column": 52, "nodeType": "394", "messageId": "395"}, {"ruleId": "392", "severity": 2, "message": "396", "line": 56, "column": 63, "nodeType": "394", "messageId": "395"}, {"ruleId": "392", "severity": 2, "message": "393", "line": 137, "column": 18, "nodeType": "394", "messageId": "395"}, {"ruleId": "392", "severity": 2, "message": "393", "line": 467, "column": 24, "nodeType": "394", "messageId": "395"}, {"ruleId": "380", "severity": 1, "message": "397", "line": 67, "column": 6, "nodeType": "382", "endLine": 67, "endColumn": 69, "suggestions": "398"}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", "ArrayExpression", ["399"], "React Hook useEffect has missing dependencies: 'fetchBlogAnalytics' and 'fetchLikesCount'. Either include them or remove the dependency array.", ["400"], "React Hook useEffect has a missing dependency: 'getAnalyticsData'. Either include it or remove the dependency array.", ["401"], "React Hook useEffect has a missing dependency: 'fetchBlogData'. Either include it or remove the dependency array.", ["402"], "React Hook useEffect has missing dependencies: 'checkFavoriteStatus', 'checkLikeStatus', and 'fetchLikesCount'. Either include them or remove the dependency array.", ["403"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "React Hook useEffect has a missing dependency: 'nextSlide'. Either include it or remove the dependency array.", ["404"], {"desc": "405", "fix": "406"}, {"desc": "407", "fix": "408"}, {"desc": "409", "fix": "410"}, {"desc": "411", "fix": "412"}, {"desc": "413", "fix": "414"}, {"desc": "415", "fix": "416"}, "Update the dependencies array to be: [users, roleFilter, searchTerm, applyFilters]", {"range": "417", "text": "418"}, "Update the dependencies array to be: [fetchBlogAnalytics, fetchLikesCount, params.id, router]", {"range": "419", "text": "420"}, "Update the dependencies array to be: [getAnalyticsData, period]", {"range": "421", "text": "422"}, "Update the dependencies array to be: [fetchBlogData]", {"range": "423", "text": "424"}, "Update the dependencies array to be: [checkFavoriteStatus, checkLikeStatus, fetchLikesCount]", {"range": "425", "text": "426"}, "Update the dependencies array to be: [currentIndex, filteredBlogs.length, visibleItems, isAnimating, nextSlide]", {"range": "427", "text": "428"}, [933, 964], "[users, roleFilter, searchTerm, applyFilters]", [9327, 9346], "[fetchBlogAnalytics, fetchLikesCount, params.id, router]", [1561, 1569], "[getAnalyticsData, period]", [5297, 5299], "[fetchBlogData]", [11795, 11797], "[checkFavoriteStatus, checkLikeStatus, fetchLikesCount]", [1922, 1985], "[currentIndex, filteredBlogs.length, visibleItems, isAnimating, nextSlide]"]
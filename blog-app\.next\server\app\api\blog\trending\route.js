"use strict";(()=>{var e={};e.id=1196,e.ids=[1196],e.modules={11185:e=>{e.exports=require("mongoose")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},89222:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>f,originalPathname:()=>q,patchFetch:()=>v,requestAsyncStorage:()=>y,routeModule:()=>b,serverHooks:()=>S,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>k});var o={};r.r(o),r.d(o,{GET:()=>m});var n=r(95419),a=r(69108),i=r(99678),s=r(78070),l=r(91887),c=r(42244),u=r(11185),d=r.n(u);let g=new(d()).Schema({blogId:{type:d().Schema.Types.ObjectId,ref:"Blog",required:!0},userId:{type:d().Schema.Types.ObjectId,ref:"User",required:!0},createdAt:{type:Date,default:Date.now}}),p=d().models.Like||d().model("Like",g);async function m(){try{await (0,l.n)();let e=(await c.Z.find({}).lean()).map(async e=>{try{let t=await p.countDocuments({blogId:e._id});return{...e,likeCount:t}}catch(t){return console.error(`Error counting likes for blog ${e._id}:`,t),{...e,likeCount:0}}}),t=(await Promise.all(e)).sort((e,t)=>t.likeCount-e.likeCount).slice(0,12);return s.Z.json({success:!0,blogs:t})}catch(e){console.error("Error fetching trending blogs:",e);try{let e=await c.Z.find({}).sort({createdAt:-1}).limit(12).lean();return s.Z.json({success:!0,blogs:e})}catch(e){return console.error("Fallback error:",e),s.Z.json({success:!1,message:"Failed to fetch trending blogs"},{status:500})}}}let b=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/blog/trending/route",pathname:"/api/blog/trending",filename:"route",bundlePath:"app/api/blog/trending/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Mr.Blog\\blog-app\\app\\api\\blog\\trending\\route.js",nextConfigOutput:"",userland:o}),{requestAsyncStorage:y,staticGenerationAsyncStorage:h,serverHooks:S,headerHooks:f,staticGenerationBailout:k}=b,q="/api/blog/trending/route";function v(){return(0,i.patchFetch)({serverHooks:S,staticGenerationAsyncStorage:h})}},91887:(e,t,r)=>{r.d(t,{n:()=>a});var o=r(11185),n=r.n(o);let a=async()=>{try{if(n().connections[0].readyState){console.log("DB Already Connected");return}let e=process.env.MONGODB_URI||"mongodb+srv://subhashanas:<EMAIL>/blog-app";await n().connect(e,{serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3}),console.log("DB Connected")}catch(e){console.error("DB Connection Error:",e.message)}}},42244:(e,t,r)=>{r.d(t,{Z:()=>i});var o=r(11185),n=r.n(o);let a=new(n()).Schema({title:{type:String,required:!0},description:{type:String,required:!0},category:{type:String,required:!0},author:{type:String,required:!0},image:{type:String,required:!0},authorImg:{type:String,required:!0},date:{type:Date,default:Date.now()}}),i=n().models.blog||n().model("blog",a)},78070:(e,t,r)=>{Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return o.NextResponse}});let o=r(70457)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[1638,2993],()=>r(89222));module.exports=o})();